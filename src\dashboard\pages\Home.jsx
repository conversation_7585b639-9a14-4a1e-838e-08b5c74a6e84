import React from 'react'
import TotalStudentAnalytic from '../../components/dashboard/home/<USER>'
import { Container } from 'react-bootstrap'
import { useTheme } from '../../context/ThemeContext'
import StudentInterestedExams from '../../components/dashboard/home/<USER>'
import TabSection from '../../components/dashboard/home/<USER>'
import RecentExams from '../../components/dashboard/home/<USER>'
import StudyNotesSection from '../../components/dashboard/home/<USER>'
import FAQSection from '../components/FAQSection'
import RaiseQuestion from '../../commonCompoenents/RaiseQuestion'
import TestSeriesSection from '../components/TestSereis'
import Banner from '../../commonCompoenents/Banner'
import SelectedPackage from '../components/SelectedPackage'
import RightFloatingIcons from '../../commonCompoenents/RightFloatingIcons'

const Home = () => {
  const { isDarkMode, theme } = useTheme();

  return (
    <>
    <Container
      fluid
      className='p-0'
      style={{
        backgroundColor: theme.colors.backgroundSecondary,
        color: theme.colors.text,
        transition: "all 0.3s ease"
      }}
    >
      <SelectedPackage/>
      <Banner/>
      <TotalStudentAnalytic/>
      <TestSeriesSection/>
      {/* <StudentInterestedExams/> */}
      {/* <TabSection/> */}
      {/* <RecentExams/> */}
      {/* <StudyNotesSection/> */}
      <FAQSection/>
      <RaiseQuestion/>
      <RightFloatingIcons/>
      </Container>
    </>
  )
}

export default Home
