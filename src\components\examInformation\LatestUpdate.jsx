import React from 'react'
import { Card, CardBody, ListGroup, ListGroupItem } from 'react-bootstrap'
import { FaClock } from 'react-icons/fa'

const LatestUpdate = ({data}) => {
  return (
    <>
         <Card className='border-danger shadow-sm bg-danger bg-opacity-10 my-5'>
            <CardBody>
            <div className="position-absolute top-0 start-0">
                      <span className="badge bg-success text-white fs-5">Recent Updates</span>
                </div>
              <p className="text-muted mt-4 d-flex align-items-center"><FaClock className='me-2'/>Last updated: {data?.lastUpdated}</p>
                <ListGroup>
                    {data?.updates.map((update, index) => (
                        <ListGroupItem key={index} className='bg-transparent'>{update}</ListGroupItem>
                    ))}
                </ListGroup>
            </CardBody>
          </Card>
    </>
  )
}

export default LatestUpdate
