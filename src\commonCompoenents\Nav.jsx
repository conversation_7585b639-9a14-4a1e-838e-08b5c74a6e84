import React, { useState } from "react";
import {
  Navbar,
  Nav,
  Form,
  FormControl,
  Button,
  Dropdown,
  Container,
} from "react-bootstrap";
import { FaBell, FaSearch, FaCaretDown, FaTimes, FaUser, FaSignOutAlt } from "react-icons/fa";
import { BsSun, BsMoon, BsDisplay } from "react-icons/bs";
import { MdDashboard } from "react-icons/md";
import "bootstrap/dist/css/bootstrap.min.css";
import { useTheme } from "../context/ThemeContext";
import { useDispatch, useSelector } from "react-redux";
import { logoutStudent } from "../redux/slice/studentSlice";
import { Link, useNavigate } from "react-router-dom";
import Swal from "sweetalert2";
import toast from "react-hot-toast";

const CustomNavbar = () => {
  const { isDarkMode, themeMode, setSystemTheme, setLightTheme, setDarkTheme, getThemeLabel } = useTheme();
  const [showSearch, setShowSearch] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const student = useSelector((state) => state?.student?.student?.student);

  const handleLogout = async () => {
    Swal.fire({
      title: "Are you sure?",
      text: "You will be logged out of your account!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, Logout",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const logout = await dispatch(logoutStudent());
          if (logout) {
            // Swal.fire("Logged Out!", "You have been logged out successfully.", "success");
            toast.success("You have been logged out successfully.")
            navigate("/");
          } else {
            toast.error("Logout failed. Try again!");
          }
        } catch (err) {
          toast.error("An error occurred during logout");
          // console.log("Error during logout:", err);
        }
      }
    });
  };

  // Theme toggle functions
  const handleThemeSelection = (themeType) => {
    switch (themeType) {
      case 'system':
        setSystemTheme();
        break;
      case 'light':
        setLightTheme();
        break;
      case 'dark':
        setDarkTheme();
        break;
    }
  };

  const getThemeIconComponent = (theme) => {
    switch (theme) {
      case 'system':
        return <BsDisplay size={16} />;
      case 'light':
        return <BsSun size={16} />;
      case 'dark':
        return <BsMoon size={16} />;
      default:
        return <BsDisplay size={16} />;
    }
  };

  return (
    <Navbar
      expand="lg"
      className="shadow-sm"
      style={{
        backgroundColor: isDarkMode ? '#1a1a1a' : '#ffffff',
        borderBottomColor: isDarkMode ? '#333' : '#dee2e6'
      }}
    >
      <Container>
        {/* Logo */}
        <Navbar.Brand as={Link} to="/" className={`ms-lg-0 ms-5 ${showSearch ? 'd-none' : 'd-inline'}`}>
          <img src="/logo.png" alt="Logo" width={180} />
        </Navbar.Brand>

         {/* <Navbar.Brand as={Link} to="/" className="cabin-sketch-bold">
          <Image src="/logo.png" width={32} /> Shashtrarth
        </Navbar.Brand> */}

        {/* Navbar toggle button for mobile view */}
        {/* <Navbar.Toggle aria-controls="navbar-nav" />
        <Navbar.Collapse id="navbar-nav"> */}
        <Nav className="me-auto">
          {/* Nav links */}
          <Nav.Link
            href="https://exam.shashtrath.com"
            target="_blank"
            className="d-lg-inline d-none"
          >
            Exams
          </Nav.Link>
          <Nav.Link href="https://blog.shashtrarth.com/" className="d-lg-inline d-none">
            60 Words and Blogs
          </Nav.Link>
          {/* <Nav.Link href="/current-affairs" className="d-lg-inline d-none">
            Current Affairs
          </Nav.Link> */}
        </Nav>

        {/* Search Bar */}
        {/* <Form className="d-flex col-md-3 align-items-center mx-md-3 gap-2">
          <FormControl
            type="search"
            placeholder="Search"
            className={`mr-2 rounded-5 d-lg-inline ${
              showSearch ? "d-inline" : "d-none"
            }`}
            aria-label="Search"
         
          />
          <FaSearch
            size={20}
            className={`cursor-pointer d-lg-none ${showSearch ? 'd-none' : 'd-inline mx-3 mt-1'}`}
            onClick={() => setShowSearch(!showSearch)}
          />
          <FaTimes
            size={20}
            className={`cursor-pointer d-lg-none ${showSearch ? 'd-inline' : 'd-none'}`}
            onClick={() => setShowSearch(!showSearch)}
          />

          <Button variant="outline-success"><FaSearch /></Button>
        </Form> */}

        {/* Other Links */}
        <div className="d-flex justify-content-between align-items-center gap-1 gap-md-3">
          <Nav.Link
            as={Link}
            to="/dashboard/reward"
            className="d-lg-inline d-none"
          >
            Refer & Earn
          </Nav.Link>

          {/* <Nav.Link
            as={Link}
            to="/dashboard/offline-tests"
            className="d-lg-inline d-none"
          >
            Offline Tests
          </Nav.Link> */}

          {/* Bell Icon */}
          {/* <Nav.Link
            href="#"
            className={`position-relative ${
              showSearch ? "d-none" : "d-inline"
            }`}
          >
            <FaBell size={20} />
          </Nav.Link> */}

          {/* Profile Icon with Dropdown */}
          <Dropdown
            align="end"
            className={`${showSearch ? "d-none" : "d-inline"}`}
          >
            <Dropdown.Toggle variant="" id="dropdown-custom-components">
              <img
                src="/avatar.png"
                alt="Profile"
                width={20}
                height={20}
                className="rounded-circle border border-2 object-fit-contain"
                style={{
                  borderColor: isDarkMode ? '#ffffff' : '#000000',
                  transition: 'border-color 0.3s ease'
                }}
              />
              {/* <FaCaretDown className="ms-2" /> */}
            </Dropdown.Toggle>

            <Dropdown.Menu>
              <Dropdown.Item as={Link} to="/dashboard/profile">
                <FaUser className="me-2" />
                Profile
              </Dropdown.Item>
              <Dropdown.Item as={Link} to="/membership">
                Membership
              </Dropdown.Item>
              <Dropdown.Divider />
              <Dropdown.Header>Theme</Dropdown.Header>
              <Dropdown.Item
                onClick={() => handleThemeSelection('system')}
                active={themeMode === 'system'}
                style={{
                  backgroundColor: themeMode === 'system' ? '#198754' : 'transparent',
                  color: themeMode === 'system' ? 'white' : 'inherit'
                }}
              >
                <BsDisplay className="me-2" />
                System
                {themeMode === 'system' && <span className="ms-auto">✓</span>}
              </Dropdown.Item>
              <Dropdown.Item
                onClick={() => handleThemeSelection('light')}
                active={themeMode === 'light'}
                style={{
                  backgroundColor: themeMode === 'light' ? '#198754' : 'transparent',
                  color: themeMode === 'light' ? 'white' : 'inherit'
                }}
              >
                <BsSun className="me-2" />
                Light
                {themeMode === 'light' && <span className="ms-auto">✓</span>}
              </Dropdown.Item>
              <Dropdown.Item
                onClick={() => handleThemeSelection('dark')}
                active={themeMode === 'dark'}
                style={{
                  backgroundColor: themeMode === 'dark' ? '#198754' : 'transparent',
                  color: themeMode === 'dark' ? 'white' : 'inherit'
                }}
              >
                <BsMoon className="me-2" />
                Dark
                {themeMode === 'dark' && <span className="ms-auto">✓</span>}
              </Dropdown.Item>
              <Dropdown.Divider />

              {/* <Form.Group className="mb-2 ms-3">
                <Form.Label>Default Language</Form.Label>
                <Form.Select aria-label="Select Language">
                  <option value="English">English</option>
                  <option value="Hindi">Hindi</option>
                </Form.Select>
              </Form.Group> */}

              {/* <Dropdown.Item as={Link} to="/settings">
                Settings
              </Dropdown.Item> */}
              <Dropdown.Item as={Button} onClick={handleLogout}>
                <FaSignOutAlt className="me-2" />
                Logout
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </div>
        {/* </Navbar.Collapse> */}
      </Container>
    </Navbar>
  );
};

export default CustomNavbar;
