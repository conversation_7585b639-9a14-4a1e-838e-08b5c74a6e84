const testData = {
    sections: [
      {
        name: "English Language",
        duration: "20:00",
        questions: [
            {
                id: 1,
                text: "What is the synonym of 'Happy'?",
                options: ["Sad", "Joyful", "Angry", "Tired"],
              },
              {
                id: 2,
                text: "Read the passage below and answer the following questions:",
                description:
                  "The sun is shining brightly in the sky. Birds are chirping, and the air is fresh with a cool breeze. It is a perfect day for a walk in the park. As the wind blows, the leaves rustle, and everything seems peaceful.",
                subQuestions: [
                  {
                    text: "What is the weather like in the passage?",
                    options: ["<PERSON>", "Rainy", "Cloudy", "Stormy"],
                  },
                  {
                    text: "Which of the following is mentioned as a feature of the day?",
                    options: ["Breeze", "Snow", "Fog", "Wind"],
                  },
                  {
                    text: "What is the mood of the passage?",
                    options: ["Peaceful", "Sad", "Angry", "Exciting"],
                  },
                ],
              },
          {
            id: 3,
            text: "Choose the correct spelling:",
            options: ["<PERSON><PERSON><PERSON>", "Receive", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"],
          },
          {
            id: 4,
            text: "Identify the verb: 'She runs fast.'",
            options: ["She", "Runs", "Fast", "The"],
          },
          {
            id: 5,
            text: "What is the antonym of 'Brave'?",
            options: ["Courageous", "Cowardly", "Strong", "Heroic"],
          },
        ],
      },
      {
        name: "Numerical Ability",
        duration: "20:00",
        questions: [
          { id: 6, text: "What is 5 + 3?", options: ["5", "8", "10", "12"] },
          { id: 7, text: "Solve: 12 * 2", options: ["14", "24", "20", "22"] },
          { id: 8, text: "What is 100/5?", options: ["10", "15", "20", "25"] },
          {
            id: 9,
            text: "Find the square root of 81.",
            options: ["8", "9", "10", "11"],
          },
          { id: 10, text: "Solve: 15 - 7", options: ["6", "8", "9", "10"] },
        ],
      },
      {
        name: "Reasoning Ability",
        duration: "20:00",
        questions: [
          {
            id: 11,
            text: "What comes next? A, C, E, G, _",
            options: ["H", "I", "J", "K"],
          },
          {
            id: 12,
            text: "Find the odd one out: Apple, Orange, Mango, Carrot",
            options: ["Apple", "Orange", "Mango", "Carrot"],
          },
          {
            id: 13,
            text: "Which shape has three sides?",
            options: ["Square", "Triangle", "Rectangle", "Pentagon"],
          },
          {
            id: 14,
            text: "What is the missing number? 2, 4, 8, 16, _",
            options: ["24", "32", "48", "64"],
          },
          {
            id: 15,
            text: "If A = 1, B = 2, C = 3, then Z = ?",
            options: ["24", "25", "26", "27"],
          },
        ],
      },
    ],
  };
  
  export default testData;
  