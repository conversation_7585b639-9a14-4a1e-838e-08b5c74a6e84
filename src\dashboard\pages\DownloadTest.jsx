import React, { useState } from 'react';
import { Con<PERSON>er, <PERSON>, <PERSON>, <PERSON>, But<PERSON>, Form, Alert } from 'react-bootstrap';
import { FaDownload, FaFilePdf, FaClock, FaQuestionCircle } from 'react-icons/fa';
import jsPDF from 'jspdf';
import { useTheme } from '../../context/ThemeContext';

// Function to format instructions with test data
const formatInstructions = (data) => [
  `This Question Booklet contains ${data.totalQuestions} questions in all.`,
  `All questions carry equal marks (${data.marksPerQuestion} marks each).`,
  "An Answer Sheet is provided to mark the answers. You must write your Roll Number and encode it and write other particulars in the space provided in the Answer Sheet.",
  "Immediately after commencement of the examination, check your Question Booklet and Answer Sheet to ensure:",
  "   • The Question Booklet Series is printed on the top left-hand corner",
  "   • The Booklet contains all printed pages",
  "   • No page or question is missing, unprinted, torn or repeated",
  "   • The Question Booklet and Answer Sheet have the same series",
  `If there is any sort of mistake either of printing or factual nature, the ${data.defaultLanguage} version will be treated as standard.`,
  `Questions and their responses are printed in ${data.languages.join(' and ')} versions in this Booklet.`,
  "Each question comprises four options-(A), (B), (C) and (D). Select ONLY ONE correct response.",
  "In the Answer Sheet, mark ONLY ONE circle with Black/Blue ink ballpoint pen for each question.",
  `For each wrong answer, ${data.negativeMarking} marks will be deducted as penalty.`,
  "Do not remove or tear off any sheet from the Question Booklet.",
  "You must hand over your Answer Sheet to the Invigilator after the examination.",
  "You are permitted to take away the Question Booklet after the examination."
];

// Dummy test data for offline download
const offlineTestData = {
  testName: "SSC CGL Mock Test - 2024",
  duration: "60 minutes",
  totalQuestions: 25,
  maxMarks: 50,
  marksPerQuestion: 2,
  negativeMarking: 0.5,
  languages: ["English", "Hindi"],
  defaultLanguage: "English",
  sections: [
    {
      name: "General Intelligence & Reasoning",
      questions: [
        {
          id: 1,
          text: "In a certain code language, 'COMPUTER' is written as 'RFUVQNPC'. How will 'KEYBOARD' be written in that code?",
          options: ["BQZXMDQF", "BQZXMEQF", "AQZXMDQF", "BQYXMDQF"],
          correctAnswer: 0
        },
        {
          id: 2,
          text: "Find the missing number in the series: 2, 6, 12, 20, 30, ?",
          options: ["42", "40", "38", "44"],
          correctAnswer: 0
        },
        {
          id: 3,
          text: "If ROSE is coded as 6821, CHAIR is coded as 73456 and PREACH is coded as 961473, what will be the code for SEARCH?",
          options: ["214673", "216473", "214763", "216743"],
          correctAnswer: 1
        },
        {
          id: 4,
          text: "A man walks 5 km towards north, then turns left and walks 4 km, then turns left and walks 5 km. How far is he from the starting point?",
          options: ["4 km", "5 km", "3 km", "9 km"],
          correctAnswer: 0
        },
        {
          id: 5,
          text: "Which of the following is different from others?",
          options: ["Square", "Rectangle", "Triangle", "Circle"],
          correctAnswer: 3
        }
      ]
    },
    {
      name: "General Knowledge & General Awareness",
      questions: [
        {
          id: 6,
          text: "Who is known as the 'Father of the Indian Constitution'?",
          options: ["Mahatma Gandhi", "Dr. B.R. Ambedkar", "Jawaharlal Nehru", "Sardar Patel"],
          correctAnswer: 1
        },
        {
          id: 7,
          text: "Which planet is known as the 'Red Planet'?",
          options: ["Venus", "Jupiter", "Mars", "Saturn"],
          correctAnswer: 2
        },
        {
          id: 8,
          text: "The currency of Japan is:",
          options: ["Yuan", "Won", "Yen", "Ringgit"],
          correctAnswer: 2
        },
        {
          id: 9,
          text: "Which of the following is the largest ocean in the world?",
          options: ["Atlantic Ocean", "Indian Ocean", "Arctic Ocean", "Pacific Ocean"],
          correctAnswer: 3
        },
        {
          id: 10,
          text: "The headquarters of UNESCO is located in:",
          options: ["New York", "Geneva", "Paris", "London"],
          correctAnswer: 2
        }
      ]
    },
    {
      name: "Quantitative Aptitude",
      questions: [
        {
          id: 11,
          text: "If the cost price of 20 articles is equal to the selling price of 16 articles, find the profit percentage.",
          options: ["20%", "25%", "30%", "35%"],
          correctAnswer: 1
        },
        {
          id: 12,
          text: "A train 125 meters long is running at 50 km/hr. In how much time will it cross a platform 100 meters long?",
          options: ["16.2 seconds", "18 seconds", "20 seconds", "22.5 seconds"],
          correctAnswer: 0
        },
        {
          id: 13,
          text: "The simple interest on Rs. 2000 for 3 years at 8% per annum is:",
          options: ["Rs. 480", "Rs. 520", "Rs. 560", "Rs. 600"],
          correctAnswer: 0
        },
        {
          id: 14,
          text: "If x + y = 10 and xy = 21, then x² + y² = ?",
          options: ["58", "62", "68", "72"],
          correctAnswer: 0
        },
        {
          id: 15,
          text: "The average of 5 consecutive odd numbers is 27. What is the largest number?",
          options: ["29", "31", "33", "35"],
          correctAnswer: 1
        }
      ]
    },
    {
      name: "English Comprehension",
      questions: [
        {
          id: 16,
          text: "Choose the correct synonym for 'ABUNDANT':",
          options: ["Scarce", "Plentiful", "Limited", "Insufficient"],
          correctAnswer: 1
        },
        {
          id: 17,
          text: "Identify the correctly spelt word:",
          options: ["Accomodation", "Accommodation", "Acommodation", "Acomodation"],
          correctAnswer: 1
        },
        {
          id: 18,
          text: "Choose the correct antonym for 'OPTIMISTIC':",
          options: ["Hopeful", "Positive", "Pessimistic", "Confident"],
          correctAnswer: 2
        },
        {
          id: 19,
          text: "Fill in the blank: 'He is _____ honest person.'",
          options: ["a", "an", "the", "no article"],
          correctAnswer: 1
        },
        {
          id: 20,
          text: "Choose the correct passive voice: 'They are building a new house.'",
          options: [
            "A new house is being built by them.",
            "A new house was being built by them.",
            "A new house has been built by them.",
            "A new house will be built by them."
          ],
          correctAnswer: 0
        }
      ]
    },
    {
      name: "Additional Questions",
      questions: [
        {
          id: 21,
          text: "Which of the following is a renewable source of energy?",
          options: ["Coal", "Natural Gas", "Solar Energy", "Petroleum"],
          correctAnswer: 2
        },
        {
          id: 22,
          text: "The process of converting solid directly to gas is called:",
          options: ["Evaporation", "Sublimation", "Condensation", "Melting"],
          correctAnswer: 1
        },
        {
          id: 23,
          text: "Who wrote the famous novel 'Pride and Prejudice'?",
          options: ["Charlotte Bronte", "Jane Austen", "Emily Dickinson", "Virginia Woolf"],
          correctAnswer: 1
        },
        {
          id: 24,
          text: "The smallest unit of matter is:",
          options: ["Molecule", "Atom", "Electron", "Proton"],
          correctAnswer: 1
        },
        {
          id: 25,
          text: "Which vitamin is produced when skin is exposed to sunlight?",
          options: ["Vitamin A", "Vitamin B", "Vitamin C", "Vitamin D"],
          correctAnswer: 3
        }
      ]
    }
  ]
};

const DownloadTest = () => {
  const { isDarkMode, theme } = useTheme();
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState('omr');
  const [logoLoaded, setLogoLoaded] = useState(false);
  const [logoImage, setLogoImage] = useState(null);

  // Function to convert image to base64
  const getBase64Image = (imgUrl) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'Anonymous';
      img.onload = () => {
        // Use original image size without resizing
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0, img.width, img.height);
        resolve(canvas.toDataURL('image/png', 1.0)); // Maximum quality
      };
      img.onerror = reject;
      img.src = imgUrl;
    });
  };

  // Function to add watermark to each page
  const addWatermark = (doc) => {
    const totalPages = doc.internal.getNumberOfPages();
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    
    for (let i = 1; i <= totalPages; i++) {
      doc.setPage(i);
      
      // Set transparency for watermark with increased opacity
      doc.saveGraphicsState();
      doc.setGState(new doc.GState({ opacity: 0.3 })); // Increased opacity from 0.2 to 0.3
    
      // Center point of the page
      const centerX = pageWidth / 2;
      const centerY = pageHeight / 2;

      // Add watermark image at the center of the page
      if (logoImage) {
        // Reduce watermark size for better visibility
        const watermarkSize = pageWidth * 0.6; // Reduced from 0.9 to 0.6
        try {
          doc.addImage(
            logoImage,
            'PNG',
            centerX - (watermarkSize/2),
            centerY - (watermarkSize/2),
            watermarkSize,
            watermarkSize,
            undefined,
            'FAST'
          );
          console.log(`Watermark added to page ${i}`); // Debug log
        } catch (error) {
          console.error('Error adding watermark to page:', error);
        }
      } else {
        console.warn('Watermark image not loaded');
      }

      doc.restoreGraphicsState();
    }
  };

  // Function to generate PDF
  const generatePDF = async () => {
    setIsGenerating(true);
    
    try {
      // Load watermark image first
      const logoBase64 = await getBase64Image('/watermark.png');
      if (!logoBase64) {
        throw new Error('Failed to load watermark image');
      }
      console.log('Watermark loaded successfully'); // Debug log
      setLogoImage(logoBase64);
      setLogoLoaded(true);

      const doc = new jsPDF();
      let yPosition = 20;
      const pageHeight = doc.internal.pageSize.height;
      const margin = 20;
      let questionNumber = 1;

      // Title Page
      doc.setFontSize(20);
      doc.setFont(undefined, 'bold');
      doc.text(offlineTestData.testName, margin, yPosition);
      
      yPosition += 15;
      doc.setFontSize(12);
      doc.setFont(undefined, 'normal');
      doc.text(`Duration: ${offlineTestData.duration}`, margin, yPosition);
      yPosition += 8;
      doc.text(`Total Questions: ${offlineTestData.totalQuestions}`, margin, yPosition);
      yPosition += 8;
      doc.text(`Maximum Marks: ${offlineTestData.maxMarks}`, margin, yPosition);
      
      yPosition += 20;
      // Set heading font and size
      doc.setFont('times', 'bold');
      doc.setFontSize(20); // Increased size for heading
      doc.text('IMPORTANT INSTRUCTIONS:', margin, yPosition);
      yPosition += 10;
      
      // Reset font settings for instructions
      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      const instructions = formatInstructions(offlineTestData);
      instructions.forEach((instruction, index) => {
        if (yPosition > pageHeight - 30) {
          doc.addPage();
          yPosition = 20;
        }
        const splitText = doc.splitTextToSize(`${index + 1}. ${instruction}`, 170);
        doc.text(splitText, margin, yPosition);
        yPosition += splitText.length * 8;
      });

      // Add questions
      offlineTestData.sections.forEach((section) => {
        // Section header
        if (yPosition > pageHeight - 40) {
          doc.addPage();
          yPosition = 20;
        }
        
        yPosition += 15;
        doc.setFont(undefined, 'bold');
        doc.setFontSize(14);
        doc.text(section.name, margin, yPosition);
        yPosition += 15;

        section.questions.forEach((question) => {
          // Check if we need a new page
          if (yPosition > pageHeight - 60) {
            doc.addPage();
            yPosition = 20;
          }

          // Question text
          doc.setFont(undefined, 'bold');
          doc.setFontSize(11);
          const questionText = `Q${questionNumber}. ${question.text}`;
          const splitQuestion = doc.splitTextToSize(questionText, 170);
          doc.text(splitQuestion, margin, yPosition);
          yPosition += splitQuestion.length * 6;

          // Options with OMR circles
          doc.setFont(undefined, 'normal');
          doc.setFontSize(10);
          question.options.forEach((option, index) => {
            const optionLetter = String.fromCharCode(65 + index); // A, B, C, D
            
            // Draw circle for OMR
            doc.circle(margin + 5, yPosition - 2, 2);
            doc.text(`${optionLetter}) ${option}`, margin + 15, yPosition);
            yPosition += 8;
          });

          yPosition += 10;
          questionNumber++;
        });
      });

      // Answer Key Page
      doc.addPage();
      yPosition = 20;
      doc.setFontSize(16);
      doc.setFont(undefined, 'bold');
      doc.text('ANSWER KEY', margin, yPosition);
      yPosition += 20;

      doc.setFontSize(10);
      doc.setFont(undefined, 'normal');
      
      let answerNumber = 1;
      offlineTestData.sections.forEach((section) => {
        section.questions.forEach((question) => {
          if (yPosition > pageHeight - 20) {
            doc.addPage();
            yPosition = 20;
          }
          
          const correctOption = String.fromCharCode(65 + question.correctAnswer);
          doc.text(`Q${answerNumber}: ${correctOption}`, margin + (answerNumber % 4) * 40, yPosition);
          
          if (answerNumber % 4 === 0) {
            yPosition += 10;
          }
          answerNumber++;
        });
      });

      // Add watermark before saving
      addWatermark(doc);

      // Save the PDF with updated name format
      const fileName = `${offlineTestData.testName.replace(/[^a-zA-Z0-9]/g, '_')}_shashtrarth_Offline_Test.pdf`;
      doc.save(fileName);
      
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Container className="py-4">
      <Row className="justify-content-center">
        <Col lg={8}>
          <Card 
            className="shadow-sm"
            style={{
              backgroundColor: theme.colors.cardBackground,
              borderColor: theme.colors.cardBorder,
              color: theme.colors.cardText
            }}
          >
            <Card.Header className="text-center">
              <h3 style={{ color: theme.colors.cardText }}>
                <FaFilePdf className="me-2 text-danger" />
                Download Offline Test
              </h3>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <h5 style={{ color: theme.colors.cardText }}>Test Details</h5>
                  <div className="mb-3">
                    <p><strong>Test Name:</strong> {offlineTestData.testName}</p>
                    <p><FaClock className="me-2" /><strong>Duration:</strong> {offlineTestData.duration}</p>
                    <p><FaQuestionCircle className="me-2" /><strong>Questions:</strong> {offlineTestData.totalQuestions}</p>
                    <p><strong>Maximum Marks:</strong> {offlineTestData.maxMarks}</p>
                  </div>
                </Col>
                <Col md={6}>
                  <h5 style={{ color: theme.colors.cardText }}>Sections Included</h5>
                  <ul className="list-unstyled">
                    {offlineTestData.sections.map((section, index) => (
                      <li key={index} className="mb-1">
                        <small>• {section.name} ({section.questions.length} questions)</small>
                      </li>
                    ))}
                  </ul>
                </Col>
              </Row>

              <Alert variant="info" className="mt-3">
                <strong>What you'll get:</strong>
                <ul className="mb-0 mt-2">
                  <li>Complete question paper with OMR-style answer bubbles</li>
                  <li>All {offlineTestData.totalQuestions} questions with multiple choice options</li>
                  <li>Answer key on the last page</li>
                  <li>Detailed instructions for offline practice</li>
                </ul>
              </Alert>

              <div className="text-center mt-4">
                <Button
                  variant="success"
                  size="lg"
                  onClick={generatePDF}
                  disabled={isGenerating}
                  className="px-4"
                >
                  {isGenerating ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" />
                      Generating PDF...
                    </>
                  ) : (
                    <>
                      <FaDownload className="me-2" />
                      Download Test PDF
                    </>
                  )}
                </Button>
              </div>

              <div className="mt-4 text-center">
                <small className="text-muted">
                  The PDF will include all questions in OMR format for offline practice.
                  Print the PDF and use it for mock test practice.
                </small>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default DownloadTest;
