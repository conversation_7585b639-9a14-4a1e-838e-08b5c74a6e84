import React, { useEffect, useState } from "react";
import { Button, Col, Form, Row } from "react-bootstrap";
import useCourses from "../../components/hooks/useCourses";
import { editStudentProfile } from "../../redux/slice/studentSlice";
import { useDispatch } from "react-redux";
import toast from "react-hot-toast";
import { getCourses } from "../../redux/slice/courseSlice";

const EditStudentProfile = ({ data, handleClose, refetch }) => {
  const [validated, setValidated] = useState(false);

  const [username, setUsername] = useState(data?.user?.username || "");
  // const [email, setEmail] = useState(data?.user?.email || "");
  const [firstName, setFirstName] = useState(data?.user?.first_name || "");
  const [lastName, setLastName] = useState(data?.user?.last_name || "");
  const [phone, setPhone] = useState(data?.phone || "");
  const [course, setCourse] = useState(data?.course || "");
  const [coursesFetchLoading, setCoursesFetchLoading] = useState(false);
  const [coursesfetchError, setCoursesfetchError] = useState(null);
  const [courses, setCourses] = useState([]);

  const dispatch = useDispatch();

    useEffect(() => {
      const fetchCourses = async () => {
        try {
          setCoursesFetchLoading(true);
          setCoursesfetchError(null);
          const response = await dispatch(getCourses());
          setCourses(response?.payload || []);
        } catch (error) {
          setCoursesfetchError(error.message || "Error fetching courses");
          console.error("Error fetching courses:", error);
        } finally {
          setCoursesFetchLoading(false);
        }
      };
      fetchCourses();
    }, []);

  const handleSubmit = async(event) => {
    event.preventDefault();
    // event.stopPropagation();

    const form = event.currentTarget;
    if (form.checkValidity() === false) {
      setValidated(true);
      return;
    }

    const editData = {
      user: {
        username,
        email: data?.user?.email,
        first_name: firstName,
        last_name: lastName,
      },
      phone,
      course,
    };

    try {
        const res = await dispatch(editStudentProfile({profileData: editData, id: data?.id}));
        if(res?.meta?.requestStatus === 'fulfilled'){
            toast.success("profile updated successfully!");
            refetch();
            handleClose();
        }
    } catch (error) {
        console.log(error);
        toast.error("Error updating profile");
    }
  };

  return (
    <Form noValidate validated={validated} onSubmit={handleSubmit}>
      <Row>
        <Col xs={12} md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Username</Form.Label>
            <Form.Control
              type="text"
              name="username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
            />
            <Form.Control.Feedback type="invalid">
              Username is required.
            </Form.Control.Feedback>
          </Form.Group>
        </Col>
        <Col xs={12} md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Phone</Form.Label>
            <Form.Control
              type="text"
              name="phone"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              pattern="^\d{10}$"
              required
            />
            <Form.Control.Feedback type="invalid">
              Enter a valid 10-digit phone number.
            </Form.Control.Feedback>
          </Form.Group>
        </Col>
      </Row>
      <Row>
        <Col xs={12} md={6}>
          <Form.Group className="mb-3">
            <Form.Label>First Name</Form.Label>
            <Form.Control
              type="text"
              name="first_name"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              required
            />
            <Form.Control.Feedback type="invalid">
              First name is required.
            </Form.Control.Feedback>
          </Form.Group>
        </Col>
        <Col xs={12} md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Last Name</Form.Label>
            <Form.Control
              type="text"
              name="last_name"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              required
            />
            <Form.Control.Feedback type="invalid">
              Last name is required.
            </Form.Control.Feedback>
          </Form.Group>
        </Col>
      </Row>
      <Row>
        <Col xs={12} md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Course</Form.Label>
            <Form.Select
              value={course}
              onChange={(e) => setCourse(e.target.value)}
              required
            >
              <option value="">Select a course</option>
              {!coursesFetchLoading &&
                courses?.map((courseItem) => (
                  <option key={courseItem.course_id} value={courseItem?.name}>
                    {courseItem?.name}
                  </option>
                ))}
            </Form.Select>
            <Form.Control.Feedback type="invalid">
              Please select a course.
            </Form.Control.Feedback>
            {coursesfetchError && (
              <div className="text-danger small mt-1">
                Error loading courses
              </div>
            )}
          </Form.Group>
        </Col>
      </Row>
      <Button type="submit" variant="success" className="w-100 mb-3">
        Save Changes
      </Button>
    </Form>
  );
};

export default EditStudentProfile;
