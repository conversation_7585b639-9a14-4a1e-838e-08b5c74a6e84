import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { <PERSON>ton, Col, Container, Image, Row, Modal } from 'react-bootstrap'; 
import { FaArrowAltCircleLeft, FaArrowAltCircleRight, FaGetPocket, FaTimes } from "react-icons/fa";
import { getPackages, setSelectedPackageId } from '../../redux/slice/packageSlice';
import Checkout from './Checkout'; // Import the Checkout component

const Packages = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [packages, setPackages] = useState([]);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [showModal, setShowModal] = useState(false); 
    const [checkoutDetails, setCheckoutDetails] = useState(null);
    const accessToken = useSelector(state => state?.student?.student?.JWT_Token?.access); // Get access token
    const isLoggedIn = !!accessToken; // Check if logged in

    useEffect(() => {
        const fetchPackages = async () => {
            const response = await dispatch(getPackages()).unwrap();
            setPackages(response);
        };
        fetchPackages();
    }, [dispatch]);

    const handleNext = () => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % packages.length);
    };

    const handlePrevious = () => {
        setCurrentIndex((prevIndex) =>
            prevIndex === 0 ? packages.length - 1 : prevIndex - 1
        );
    };

    const handleShowModal = () => setShowModal(true);
    const handleCloseModal = () => setShowModal(false);

    const handleBuyNow = () => {
        if (!isLoggedIn) {
            dispatch(setSelectedPackageId(packages[currentIndex]?.id)); // Save to store
            navigate('/login'); // Navigate to login
            return;
        }

        setCheckoutDetails({
            packageName: packages[currentIndex]?.name,
            originalPrice: packages[currentIndex]?.discount_price,
            packageId: packages[currentIndex]?.id,
            descriptions: [
                packages[currentIndex]?.description_line_01,
                packages[currentIndex]?.description_line_02,
                packages[currentIndex]?.description_line_03,
                packages[currentIndex]?.description_line_04,
                packages[currentIndex]?.description_line_05,
            ].filter(Boolean),
        });
    };

    // Add schema markup before render
    const schemaMarkup = {
        "@context": "https://schema.org",
        "@type": "Product",
        "name": packages[currentIndex]?.name || "Shashtrarth Test Series Package",
        "description": "Access to 500+ exam preparation test series on Shashtrarth platform",
        "url": "https://shashtrarth.com/packages",
        "category": "Online Education",
        "offers": {
            "@type": "Offer",
            "price": packages[currentIndex]?.discount_price,
            "priceCurrency": "INR",
            "availability": "https://schema.org/InStock",
            "seller": {
                "@type": "Organization",
                "name": "Shashtrarth",
                "url": "https://shashtrarth.com"
            }
        },
        "hasCourseInstance": {
            "@type": "CourseInstance",
            "courseMode": "online",
            "courseWorkload": "P1Y",
            "educationalLevel": "Competitive Exam Preparation",
            "teaches": packages[currentIndex]?.description_line_01
        },
        "provider": {
            "@type": "Organization",
            "name": "Shashtrarth",
            "url": "https://shashtrarth.com"
        }
    };

    return (
        <>
            <script type="application/ld+json">
                {JSON.stringify(schemaMarkup)}
            </script>
            {!checkoutDetails ? (
                <Container className="mt-md-5 pt-md-5" style={{ paddingBottom: '100px' }} id="packages">
                    <Row className="justify-content-center">
                        <Col md={12} lg={12} xs={12}>
                            <Row className="justify-content-evenly align-items-center g-md-5">
                                <Col md={5} xs={12}>
                                    <Image src="/package.svg" fluid className='image-fluid' />
                                </Col>
                                <Col md={5} xs={12}>
                                    <div>
                                        <h5>Enroll In Test Series for 500+ exams with Shashtrarth</h5>
                                        <div className="d-flex justify-content-between align-items-center">
                                            <h3 className="my-2 fs-1 fw-bold text-success">
                                                {packages[currentIndex]?.name}
                                            </h3>
                                            <div className="d-flex gap-2">
                                                <Button variant='success' onClick={handlePrevious}> <FaArrowAltCircleLeft /> </Button>
                                                <Button variant='success' onClick={handleNext}><FaArrowAltCircleRight /></Button>
                                            </div>
                                        </div>
                                        <h6 className="mt-1">
                                            ₹ {packages[currentIndex]?.discount_price}
                                        </h6>

                                        <h5 className="my-3 fw-semibold">What you get on packages</h5>
                                    </div>

                                    <div className="row">
                                        {[ 
                                            packages[currentIndex]?.description_line_01,
                                            packages[currentIndex]?.description_line_02,
                                            packages[currentIndex]?.description_line_03,
                                            packages[currentIndex]?.description_line_04,
                                            packages[currentIndex]?.description_line_05
                                        ].filter(Boolean).map((description, index) => (
                                            <div key={index} className="col-6 text-center">
                                                <i className="mb-2 text-success"> <FaGetPocket /></i>
                                                <p className="text-success">{description}</p>
                                            </div>
                                        ))}
                                    </div>

                                    <div className="d-flex justify-content-start mt-2">
                                        <button className="btn btn-success" onClick={handleShowModal}>
                                            Explore Shashtrarth Pass
                                        </button>
                                        <button
                                            className="btn btn-success mx-1"
                                            onClick={handleBuyNow}
                                        >
                                            Buy Now!
                                        </button>
                                    </div>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                </Container>
            ) : (
                <Checkout checkoutDetails={checkoutDetails} setCheckoutDetails={setCheckoutDetails} />
            )}

            <Modal show={showModal} onHide={handleCloseModal} centered size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>Shashtrarth Pass</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Image src="/pass.png" fluid />
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={handleCloseModal}>
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    );
};

export default Packages;
