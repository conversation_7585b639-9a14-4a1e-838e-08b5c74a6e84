import React, { useState, useEffect } from "react";
import { Card, Row, Col, Button } from "react-bootstrap";
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aReg<PERSON><PERSON> } from "react-icons/fa";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import data from "../../../dummyData/testSeries";

const FreeTest = ({data, loading}) => {
  // const [loading, setLoading] = useState(true);

  // Filter the tests to show only free ones
  const freeTests = data?.Tiers.flatMap((category) => category?.paper).filter((test) => test.type === "free");

  // useEffect(() => {
  //   // Simulate loading time
  //   setTimeout(() => {
  //     setLoading(false);
  //   }, 2000); // 2 seconds
  // }, []);

  return (
    <>
      <Row className="my-4">
        {loading ? (
          <>
            <Skeleton
              width="90%"
              height={40}
              baseColor="#e6ffe6"
              highlightColor="#c4f7c4"
            />
          </>
        ) : (
          <>
            <h4>{data?.subcourse_name} <span className="text-success">Free Test</span></h4>
          </>
        )}
      </Row>
      <Row>
        {loading ? (
          // Skeleton placeholders while loading
          [...Array(3)].map((_, index) => (
            <Col key={index} xs={12} md={12} lg={12} className="mb-4">
              <Card className="w-100">
                <Card.Body>
                  <div className="d-flex align-items-center gap-2">
                    <Skeleton
                      width="50%"
                      height={20}
                      baseColor="#e6ffe6"
                      highlightColor="#c4f7c4"
                    />
                    <Skeleton
                      width={60}
                      height={20}
                      baseColor="#e6ffe6"
                      highlightColor="#c4f7c4"
                    />
                  </div>

                  <Card.Text>
                    <div className="d-flex justify-content-between align-items-center">
                      <div>
                        <Skeleton
                          count={3}
                          height={20}
                          baseColor="#e6ffe6"
                          highlightColor="#c4f7c4"
                        />
                        <Skeleton
                          width="50%"
                          height={15}
                          baseColor="#e6ffe6"
                          highlightColor="#c4f7c4"
                        />
                      </div>
                      <Skeleton
                        width={120}
                        height={40}
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                      />
                    </div>
                  </Card.Text>
                </Card.Body>
              </Card>
            </Col>
          ))
        ) : freeTests ? (
          freeTests.map((test, index) => (
            <Col key={index} xs={12} md={12} lg={12} className="mb-4">
              <Card className="w-100">
                <Card.Body>
                  <div className="d-flex align-items-center gap-2">
                    <Card.Title>{test?.paper_name}</Card.Title>
                    <span className="text-white badge bg-success small">
                      FREE
                    </span>
                  </div>

                  <Card.Text>
                    <div className="d-flex justify-content-between align-items-center">
                      <div>
                        <div
                          className="d-flex align-items-center text-muted"
                          style={{ fontSize: "0.85rem" }}
                        >
                          <FaClipboardList className="me-2" />
                          <span>{test?.totalQuestions} Questions</span>
                          <span className="mx-2">|</span>
                          <FaPercent className="me-2" />
                          <span>{test?.totalMarks} Marks</span>
                          <span className="mx-2">|</span>
                          <FaRegClock className="me-2" />
                          <span>{test?.durationMinutes} minutes</span>
                        </div>
                        <br />
                        Expires on {test?.expiryDate}
                      </div>
                      <Button variant="success" className="btn-sm">Start Now</Button>
                    </div>
                  </Card.Text>
                </Card.Body>
              </Card>
            </Col>
          ))
        ) : (
          <Col xs={12}>
            <p>No free tests available.</p>
          </Col>
        )}
      </Row>
    </>
  );
};

export default FreeTest;
