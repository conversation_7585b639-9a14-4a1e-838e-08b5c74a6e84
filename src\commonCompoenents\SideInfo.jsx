import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getSignUpContents } from '../redux/slice/signupContentSlice';

const SideInfo = () => {
  const dispatch = useDispatch();
  const [randomContent, setRandomContent] = useState(null);

  useEffect(() => {
    dispatch(getSignUpContents())
      .then((response) => {
        const content = response?.payload; // Assuming payload contains the data
        if (content?.length) {
          const randomIndex = Math.floor(Math.random() * content.length);
          setRandomContent(content[randomIndex]);
        }
      })
      .catch((error) => console.error('Error fetching signup content:', error));
  }, [dispatch]);

  return (
    <>
      <h1 className="cabin-sketch-bold" >
        {randomContent?.heading || 'Default Heading'}
      </h1>
      <p className="text-muted fs-4 font-semibold">
        {randomContent?.subtext_1 || 'Default Subtext 1'}
      </p>
      {randomContent?.subtext_2 && (
        <p className="text-muted fs-4 font-semibold">
          {randomContent?.subtext_2}
        </p>
      )}
    </>
  );
};

export default SideInfo;
