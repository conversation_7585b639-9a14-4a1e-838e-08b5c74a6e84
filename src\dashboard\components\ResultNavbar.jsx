import React, { useState } from 'react'
import { But<PERSON>, Modal, Navbar } from 'react-bootstrap'
import { Link } from 'react-router-dom'
import PrintPopup from './PrintPopup' 
import { FaGooglePlay, FaArrowLeft } from 'react-icons/fa'; 

const ResultNavbar = ({testData}) => {
    const [print, setPrint] = useState(false);
    
      const handleClose = () => setPrint(false);
      const handleShow = () => setPrint(true);
  return (
    <>
    <Modal show={print} onHide={handleClose} centered>
        <Modal.Header closeButton className="p-2">
          <Modal.Title>Download Shashtrarth App</Modal.Title>
        </Modal.Header>
        <Modal.Body className="p-0">
          <PrintPopup />
        </Modal.Body>
      </Modal>
        <Navbar
        fixed="top"
        bg="light"
        className="shadow-sm px-3 py-2 d-flex justify-content-between"
      >
        <Link
          to="/dashboard"
          style={{ textDecoration: "none" }}
          className="d-flex align-items-center gap-2 text-black"
        >
          <FaArrowLeft className="fs-5 text-success"/> {/* Replaced text with icon */}
          <span className='fs-6'> DASHBOARD </span>
        </Link>
        <div className="d-flex gap-3">
          <Button variant="success" className="shadow-sm" onClick={handleShow}>
          <FaGooglePlay className="me-2" /> Download App
          </Button>
          {/* <Button variant="outline-success" className="shadow-sm">
            Solution
          </Button> */}
        </div>
      </Navbar>
    </>
  )
}

export default ResultNavbar
