import React, { useEffect, useState } from "react";
import { GoogleLogin } from "@react-oauth/google";
import { jwtDecode } from "jwt-decode";
import { Container, Row, Col, Button, Form, ProgressBar } from "react-bootstrap";
import toast, { Toaster } from "react-hot-toast";
import { FaChalkboardTeacher } from "react-icons/fa";
import { useNavigate, useParams } from "react-router-dom";
import NavBar from "../../commonCompoenents/NavBar";
import SideInfo from "../../commonCompoenents/SideInfo";
import { getStudentList, googleLoginStudent, registerStudent, verifyOtp } from "../../redux/slice/studentSlice";
import { useDispatch } from "react-redux";
import useCourses from "../../components/hooks/useCourses";
import { FcGoogle } from "react-icons/fc";
import { useTheme } from "../../context/ThemeContext";
import { getCourses } from "../../redux/slice/courseSlice";
import OtpVerification from "../../components/ResendOtpButton";
import ResendOtpButton from "../../components/ResendOtpButton";

const Signup = () => {
  const { isDarkMode, theme } = useTheme();
  const navigate = useNavigate();
  const { referral_id } = useParams();
  // State to manage form inputs for each step
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [username, setUsername] = useState("");
  const [phone, setPhone] = useState("");
  const [course, setCourse] = useState("");
  const [referralCode, setReferralCode] = useState(
    referral_id ? referral_id : ""
  );
  const [otp, setOtp] = useState("");
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [loadReg, setLoadReg] = useState(false);
  const [loadVerifyOtp, setVerifyOtp] = useState(false);
  const [step, setStep] = useState(1);
  const [loadingGoogle, setLoadingGoogle] = useState(false);
  const [allStudents, setAllStudent] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [coursesFetchLoading, setCoursesFetchLoading] = useState(false);
  const [coursesfetchError, setCoursesfetchError] = useState(null);
  const [courses, setCourses] = useState([]);

  const [errors, setErrors] = useState({});

  const dispatch = useDispatch();

  // const {
  //   allStudents,
  //   isLoading: studentListFetchLoading,
  //   error: studentListFetchError,
  // } = useStudentList();

  useEffect(() => {
    const fetchStudentList = async () => {
      setIsLoading(true);
      try {
        const res = await dispatch(getStudentList());
        if (res?.meta?.requestStatus === "fulfilled") {
          setAllStudent(res?.payload);
        }
      } catch (error) {
        console.log("Error fetching student list", error);
        // setError(error.message || 'Error fetching student list');
      } finally {
        setIsLoading(false);
      }
    };
    const fetchCourses = async () => {
      try {
        setCoursesFetchLoading(true);
        setCoursesfetchError(null);
        const response = await dispatch(getCourses());
        setCourses(response?.payload || []);

        // Check for selected course ID in local storage after fetching courses
        const selectedCourseId = localStorage.getItem("selectedCourseId");
        // console.log("Selected Course ID from localStorage:", selectedCourseId); // Log the course ID
        if (selectedCourseId) {
          const matchedCourse = response?.payload?.find(
            (course) => course.course_id === parseInt(selectedCourseId)
          );
          if (matchedCourse) {
            setCourse(matchedCourse.name); // Auto-select the course name
          }
        }
      } catch (error) {
        setCoursesfetchError(error.message || "Error fetching courses");
        console.error("Error fetching courses:", error);
      } finally {
        setCoursesFetchLoading(false);
      }
    };

    fetchStudentList();
    fetchCourses();
  }, []);

  const validateStep1 = () => {
    const stepErrors = {};
    if (!firstName.trim()) stepErrors.firstName = "First name is required";
    // if (!lastName.trim())
    //   stepErrors.lastName = 'Last name is required';
    if (!email.trim()) stepErrors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(email)) stepErrors.email = "Email is invalid";
    if (/\S+@\S+\.\S+/.test(email) && email) {
      const emailExists =
        allStudents.length > 0 &&
        allStudents.find((student) => student?.user?.email === email);
      if (emailExists) stepErrors.email = "Email already exists";
    }
    if (!password) stepErrors.password = "Password is required";
    else if (password.length < 8)
      stepErrors.password = "Password must be at least 8 characters";
    else if (!/[A-Z]/.test(password) && !/[!@#$%^&*(),.?":{}|<>]/.test(password))
      stepErrors.password = "Password must contain at least one uppercase letter or one special character";
    if (password !== confirmPassword)
      stepErrors.confirmPassword = "Passwords do not match";

    setErrors(stepErrors);
    return Object.keys(stepErrors).length === 0;
  };

  const validateStep2 = () => {
    const stepErrors = {};
    if (!username.trim()) stepErrors.username = "Username is required";
    else if (
      allStudents.length > 0 &&
      allStudents.find((student) => student?.user?.username === username)
    ) {
      stepErrors.username = "Username already exists";
    } else if (username.length < 3)
      stepErrors.username = "Username must be at least 3 characters";
    if (!phone.trim()) stepErrors.phone = "Phone no. is required";
    else if (!/^\d{10}$/.test(phone))
      stepErrors.phone = "Phone no. must be 10 digits";
    else if (
      allStudents.length > 0 &&
      allStudents.find((student) => student?.phone === phone)
    )
      stepErrors.phone = "Phone no. already exists";
    if (!course.trim()) stepErrors.course = "Course is required";

    setErrors(stepErrors);
    return Object.keys(stepErrors).length === 0;
  };
  const validateStep3 = () => {
    const stepErrors = {};
    if (!otp.trim()) stepErrors.otp = "OTP is required";
    else if (otp.length < 6 || otp.length > 6) {
      stepErrors.otp = "OTP must be 6 digit";
    }

    setErrors(stepErrors);
    return Object.keys(stepErrors).length === 0;
  };

  const handleContinue = () => {
    // setStep(step + 1);
    let isValid = false;
    switch (step) {
      case 1:
        isValid = validateStep1();
        break;
      case 2:
        isValid = validateStep2();
        break;
      case 3:
        isValid = validateStep3();
        break;
      default:
        break;
    }

    if (isValid) {
      setStep((prev) => prev + 1);
    }
  };

  const handleBack = () => {
    setStep((prev) => prev - 1);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const userData = {
      user: {
        username: username,
        email: email,
        first_name: firstName,
        last_name: lastName,
        password: password,
      },
      phone: phone,
      course: course,
      referral_code: referralCode ? referralCode : null,
    };
    setLoadReg(true);
    try {
      const response = await dispatch(
        registerStudent({ studentData: userData })
      );
      // console.log(response.payload);
      if (response?.payload) {
        setStep(4);
        setIsOtpSent(true);
        setLoadReg(false);
        toast.success(response?.payload?.message);
      }
      setLoadReg(false);
    } catch (error) {
      console.error("Error during registration:", error);
      toast.error("An error occurred while registering. Please try again.");
      setLoadReg(false);
    }
  };

  const handleOtpVerification = async () => {
    if (!validateStep3()) {
      return;
    }
    setVerifyOtp(true);
    const otpData = {
      otp: otp,
      email_user: email,
    };
    try {
      const response = await dispatch(verifyOtp({ otpData: otpData }));
      // console.log(response.data);
      if (response?.payload) {
        setVerifyOtp(false);
        toast.success("otp succeessfully verified");
        navigate("/login");
      } else {
        toast.error("Invalid OTP. Please try again.");
      }
    } catch (error) {
      setVerifyOtp(false);
      console.error("Error during OTP verification:", error);
      toast.error("An error occurred while verifying OTP. Please try again.");
    }
  };

  const handleGoogleLoginSuccess = async (response) => {
    try {
      setLoadingGoogle(true);
      const decode = jwtDecode(response?.credential);
      console.log(decode);
      const loginData = {
        email: decode?.email,
        first_name: decode?.given_name,
        last_name: decode?.family_name || "",
        image: decode?.picture,
      };
      const res = await dispatch(
        googleLoginStudent({ googleloginData: loginData })
      );
      if (res?.meta?.requestStatus === "fulfilled") {
        navigate("/dashboard");
        toast.success("Login successful!");
        setLoadingGoogle(false);
      } else if (res?.meta?.requestStatus === "rejected") {
        toast.error("Google Login failed. Please try again.");
        setLoadingGoogle(false);
      }
    } catch (error) {
      console.error("Error decoding the Google token:", error);
      toast.error("Google Login failed. Please try again.");
      setLoadingGoogle(false);
    }
  };

  // Google Login Error Handler
  const handleGoogleLoginError = () => {
    toast.error("Google Login failed. Please try again.");
  };

  // Add schema markup before return statement
  const schemaMarkup = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Student Signup - Shashtrarth",
    "description": "Create your student account at Shashtrarth. Access online courses and learning materials with easy registration process.",
    "url": "https://shashtrarth.com/signup",
    "mainEntity": {
      "@type": "WebForm",
      "name": "Student Registration Form",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://shashtrarth.com/signup",
        "actionPlatform": [
          "https://schema.org/DesktopWebPlatform",
          "https://schema.org/MobileWebPlatform"
        ]
      }
    },
    "offers": {
      "@type": "Offer",
      "category": "Online Education",
      "availability": "https://schema.org/InStock"
    },
    "provider": {
      "@type": "EducationalOrganization",
      "name": "Shashtrarth",
      "url": "https://shashtrarth.com"
    }
  };

  return (
    <>
      <Toaster />
      <script type="application/ld+json">
        {JSON.stringify(schemaMarkup)}
      </script>
      <NavBar />
      {loadingGoogle ? (
        <>
          <Container>
            <Row className="justify-content-center mt-5">
              <Col xs={12} md={12} className="mt-5">
                <div className="text-center mt-5">
                  <FcGoogle size={48} />
                  <p className="text-center">Please wait...</p>
                </div>
              </Col>
            </Row>
          </Container>
        </>
      ) : (
        <Container
          className="d-flex flex-column justify-content-center align-items-center"
          style={{
            minHeight: "100vh",
            minWidth: "100vw",
            background: isDarkMode
              ? "linear-gradient(130deg, #1a1a1a, #2d4a3a), url('https://www.transparenttextures.com/patterns/gplay.png')"
              : "linear-gradient(130deg, white,rgb(129, 194, 141)), url('https://www.transparenttextures.com/patterns/gplay.png')",
            backgroundBlendMode: "overlay",
            opacity: 0.9,
            transition: "background 0.3s ease"
          }}
        >
          <Row className="justify-content-center align-items-center py-5 gap-5">
            <Col md={5} lg={5} xs={0} className="mt-5 d-md-block d-none">
              <SideInfo />
            </Col>
            <Col md={5} lg={3} xs={12} className="p-md-0 p-4 mt-5">


              <Row
                className="rounded-4 border-2 border-md border p-md-4 p-2"
                style={{
                  backgroundColor: theme.colors.cardBackground, // Always white (as per requirement)
                  borderColor: theme.colors.cardBorder,
                  color: theme.colors.cardText,
                  transition: "all 0.3s ease"
                }}
              >

                {/* Progress Bar */}
                <ProgressBar
                  now={(step / 4) * 100}
                  style={{ width: "100%", height: "0.5rem", padding: "0 !important" }}
                />

                <Row className="text-center mt-3">
                  <FaChalkboardTeacher size={60} color="#146c43" />


                  <h3
                    className="mt-3"
                    style={{
                      color: "#146c43",
                    }}
                  >
                    {step === 1 || step === 2
                      ? "Signup"
                      : step === 3
                        ? "Referal Code"
                        : "Verify OTP"}
                  </h3>
                </Row>



                <Form onSubmit={handleSubmit} className="w-100 mt-4">
                  {/* First Step: Signup Form */}
                  {step === 1 && (
                    <>
                      <Form.Group className="mb-3">
                        <Form.Control
                          type="text"
                          placeholder="First Name"
                          value={firstName}
                          onChange={(e) => setFirstName(e.target.value)}
                          style={{
                            backgroundColor: "#e4e4e7",
                            color: "#3F3D56",
                          }}
                          isInvalid={!!errors.firstName}
                        />
                        <Form.Control.Feedback type="invalid">
                          {errors.firstName}
                        </Form.Control.Feedback>
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Control
                          type="text"
                          placeholder="Last Name"
                          value={lastName}
                          onChange={(e) => setLastName(e.target.value)}
                          style={{
                            backgroundColor: "#e4e4e7",
                            color: "#3F3D56",
                          }}
                        />
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Control
                          type="email"
                          placeholder="Email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          style={{
                            backgroundColor: "#e4e4e7",
                            color: "#3F3D56",
                          }}
                          isInvalid={!!errors.email}
                        />
                        <Form.Control.Feedback type="invalid">
                          {errors.email}
                        </Form.Control.Feedback>
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Control
                          type="password"
                          placeholder="Password"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          style={{
                            backgroundColor: "#e4e4e7",
                            color: "#3F3D56",
                          }}
                          isInvalid={!!errors.password}
                        />
                        <Form.Control.Feedback type="invalid">
                          {errors.password}
                        </Form.Control.Feedback>
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Control
                          type="password"
                          placeholder="Confirm Password"
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          style={{
                            backgroundColor: "#e4e4e7",
                            color: "#3F3D56",
                          }}
                          isInvalid={!!errors.confirmPassword}
                        />
                        <Form.Control.Feedback type="invalid">
                          {errors.confirmPassword}
                        </Form.Control.Feedback>
                      </Form.Group>

                      <Button
                        type="button"
                        variant="success"
                        className="w-100"
                        style={{ color: "#f3f4f6" }}
                        onClick={handleContinue}
                      >
                        Continue
                      </Button>
                    </>
                  )}

                  {/* Second Step: Additional Info Form */}
                  {step === 2 && (
                    <>
                      <Form.Group className="mb-1">
                        <Form.Control
                          type="text"
                          placeholder="Username"
                          value={username}
                          onChange={(e) => setUsername(e.target.value)}
                          style={{
                            backgroundColor: "#e4e4e7",
                            color: "#3F3D56",
                          }}
                          isInvalid={!!errors.username}
                        />
                        <Form.Control.Feedback type="invalid">
                          {errors.username}
                        </Form.Control.Feedback>
                      </Form.Group>
                      <Form.Text className="text-muted">
                        Please remember your username.
                      </Form.Text>

                      <Form.Group className="mb-3 mt-3">
                        <Form.Control
                          type="text"
                          placeholder="10 digit mobile no"
                          value={phone}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (value.length <= 10) {
                              setPhone(value);
                            } else {
                              toast.error("Phone number cannot exceed 10 digits");
                            }
                          }}
                          style={{
                            backgroundColor: "#e4e4e7",
                            color: "#3F3D56",
                          }}
                          isInvalid={!!errors.phone}
                        />
                        <Form.Control.Feedback type="invalid">
                          {errors.phone}
                        </Form.Control.Feedback>
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Select
                          value={course}
                          onChange={(e) => setCourse(e.target.value)}
                          style={{
                            backgroundColor: "#e4e4e7",
                            color: "#3F3D56",
                          }}
                          isInvalid={!!errors.course}
                        >
                          <option value="">Select Course</option>
                          {!coursesFetchLoading &&
                            courses?.map((course) => (
                              <option
                                key={course.course_id}
                                value={course?.name}
                              >
                                {course?.name}
                              </option>
                            ))}
                        </Form.Select>
                        <Form.Control.Feedback type="invalid">
                          {errors.course}
                        </Form.Control.Feedback>
                        {coursesFetchLoading && (
                          <div className="text-muted small mt-1">
                            Loading courses...
                          </div>
                        )}
                        {coursesfetchError && (
                          <div className="text-danger small mt-1">
                            Error loading courses
                          </div>
                        )}
                      </Form.Group>

                      <Row className="mb-3">
                        <Col className="d-flex justify-content-start">
                          <Button
                            variant="link"
                            className="text-muted"
                            onClick={handleBack}
                          >
                            Back
                          </Button>
                        </Col>
                        <Col className="d-flex justify-content-end">
                          <Button
                            variant="success"
                            className="w-100"
                            style={{ color: "#f3f4f6" }}
                            onClick={handleContinue}
                          >
                            Continue
                          </Button>
                        </Col>
                      </Row>
                    </>
                  )}

                  {/* Third Step: Referral Code Form */}
                  {step === 3 && (
                    <>
                      <Form.Group className="mb-3">
                        <Form.Control
                          type="text"
                          placeholder="Referral Code"
                          value={referralCode}
                          onChange={(e) => setReferralCode(e.target.value)}
                          style={{
                            backgroundColor: "#e4e4e7",
                            color: "#3F3D56",
                          }}
                        />
                      </Form.Group>

                      <Row className="mb-3">
                        <Col className="d-flex justify-content-start">
                          <Button
                            variant="link"
                            className="text-muted"
                            onClick={handleBack}
                          >
                            Back
                          </Button>
                        </Col>
                        <Col className="d-flex justify-content-end">
                          <Button
                            type="submit"
                            variant="success"
                            className="w-100"
                            style={{ color: "#f3f4f6" }}
                            disabled={loadReg}
                          >
                            {loadReg ? "Submitting.." : "Submit"}
                          </Button>
                        </Col>
                      </Row>
                    </>
                  )}
                  {/* OTP Verification Step */}
                  {isOtpSent && step === 4 && (
                    <>
                      <Row>
                        <Col sm={8} md={7} lg={7}>
                          <Form.Group className="mb-3">
                            <Form.Control
                              type="text"
                              placeholder="Enter OTP"
                              value={otp}
                              onChange={(e) => setOtp(e.target.value)}
                              style={{
                                backgroundColor: "#e4e4e7",
                                color: "#3F3D56",
                              }}
                              isInvalid={!!errors.otp}
                            />
                            <Form.Control.Feedback type="invalid">
                              {errors.otp}
                            </Form.Control.Feedback>
                          </Form.Group>
                        </Col>
                        <Col sm={4} md={5} lg={5}>
                          <Button
                            type="button"
                            variant="success"
                            className="w-100"
                            style={{ color: "#f3f4f6" }}
                            onClick={handleOtpVerification}
                            disabled={loadVerifyOtp}
                          >
                            {loadVerifyOtp ? "Verifying.." : "Verify"}
                          </Button>
                        </Col>
                      </Row>




                      <ResendOtpButton />

                    </>
                  )}
                </Form>
                <Row className="mt-4">
                  <Col className="text-center">
                    <p
                      style={{
                        color: isDarkMode ? '#888888' : '#6c757d',
                        transition: "color 0.3s ease"
                      }}
                    >
                      Or Continue With
                    </p>
                  </Col>
                </Row>

                {/* Social Icons directly integrated in Signup */}
                <Row className="d-flex justify-content-center">
                  <Col className="d-flex justify-content-center">
                    <GoogleLogin
                      onSuccess={handleGoogleLoginSuccess}
                      onError={handleGoogleLoginError}
                      useOneTap
                    />
                    {/* <Button
                    variant="outline-secondary"
                    className="mx-2"
                    style={{ padding: "10px", borderRadius: "8px" }}>
                    <img
                      src="/google.png"
                      alt="Google"
                      style={{ width: "30px", height: "30px" }}
                    />
                  </Button> */}
                  </Col>
                </Row>

                <Row className="">
                  <Col className="d-flex justify-content-center align-items-center">
                    <p className="text-muted m-0">Already have an account? </p>
                    <Button
                      variant="link"
                      onClick={() => navigate("/login")}
                      className="text-muted"
                    >
                      Login
                    </Button>
                  </Col>
                </Row>
              </Row>
            </Col>
          </Row>
        </Container>
      )}
    </>
  );
};

export default Signup;
