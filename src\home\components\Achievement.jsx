import React from 'react'
import { <PERSON>, Container, <PERSON> } from 'react-bootstrap'
import { useTheme } from '../../context/ThemeContext'

const Achievement = () => {
  const { isDarkMode, theme } = useTheme();

  return (
    <>
        <Container className="mt-5" >
        <Row className="justify-content-center">
          <Col md={12} lg={10} xs={12}>
            <Row
              className="px-4 py-4 rounded-4"
              style={{
                backgroundColor: isDarkMode ? 'rgba(13, 202, 240, 0.2)' : 'rgba(13, 202, 240, 0.1)',
                transition: 'background-color 0.3s ease'
              }}
            >
              {[
                {
                  iconColor: "success",
                  bgColor: "bg-success bg-opacity-10",
                  circleColor: "bg-success bg-opacity-25",
                  title: "Registered Students",
                  value: "Not enough data",
                  svgPath:
                    "M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z",
                },
                {
                  iconColor: "warning",
                  bgColor: "bg-warning bg-opacity-10",
                  circleColor: "bg-warning bg-opacity-25",
                  title: "Student Selections",
                  value: "Not enough data",
                  svgPath:
                    "M16.5 18.75h-9m9 0a3 3 0 0 1 3 3h-15a3 3 0 0 1 3-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 0 1-.982-3.172M9.497 14.25a7.454 7.454 0 0 0 .981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 0 0 7.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 0 0 2.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.492a46.32 46.32 0 0 1 2.916.52 6.003 6.003 0 0 1-5.395 4.972m0 0a6.726 6.726 0 0 1-2.749 1.35m0 0a6.772 6.772 0 0 1-3.044 0",
                },
                {
                  iconColor: "danger",
                  bgColor: "bg-danger bg-opacity-10",
                  circleColor: "bg-danger bg-opacity-25",
                  title: "Tests Attempted",
                  value: "Not enough data",
                  svgPath:
                    "M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z",
                },
                {
                  iconColor: "primary",
                  bgColor: "bg-primary bg-opacity-10",
                  circleColor: "bg-primary bg-opacity-25",
                  title: "Courses Purchased",
                  value: "Not enough data",
                  svgPath:
                    "M8.625 12a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 0 1-2.555-.337A5.972 5.972 0 0 1 5.41 20.97a5.969 5.969 0 0 1-.474-.065 4.48 4.48 0 0 0 .978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25Z",
                },
              ].map((item, index) => (
                <Col key={index} md={3} className='mb-md-0 mb-4'>
                  <div
                    className={`rounded-4 p-3 d-flex flex-column justify-content-center align-items-center`}
                    style={{
                      backgroundColor: isDarkMode
                        ? (item.iconColor === 'success' ? 'rgba(25, 135, 84, 0.2)' :
                           item.iconColor === 'danger' ? 'rgba(220, 53, 69, 0.2)' :
                           item.iconColor === 'primary' ? 'rgba(13, 110, 253, 0.2)' : 'rgba(108, 117, 125, 0.2)')
                        : (item.iconColor === 'success' ? 'rgba(25, 135, 84, 0.1)' :
                           item.iconColor === 'danger' ? 'rgba(220, 53, 69, 0.1)' :
                           item.iconColor === 'primary' ? 'rgba(13, 110, 253, 0.1)' : 'rgba(108, 117, 125, 0.1)'),
                      color: theme.colors.text,
                      transition: 'all 0.3s ease'
                    }}
                  >
                    <div
                      className={`rounded-circle text-${item.iconColor} p-2 mb-3`}
                      style={{
                        backgroundColor: isDarkMode
                          ? (item.iconColor === 'success' ? 'rgba(25, 135, 84, 0.4)' :
                             item.iconColor === 'danger' ? 'rgba(220, 53, 69, 0.4)' :
                             item.iconColor === 'primary' ? 'rgba(13, 110, 253, 0.4)' : 'rgba(108, 117, 125, 0.4)')
                          : (item.iconColor === 'success' ? 'rgba(25, 135, 84, 0.25)' :
                             item.iconColor === 'danger' ? 'rgba(220, 53, 69, 0.25)' :
                             item.iconColor === 'primary' ? 'rgba(13, 110, 253, 0.25)' : 'rgba(108, 117, 125, 0.25)'),
                        transition: 'background-color 0.3s ease'
                      }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth="1.5"
                        stroke="currentColor"
                        width="30px"
                        height="30px"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d={item.svgPath}
                        />
                      </svg>
                    </div>
                    <p style={{ color: theme.colors.text, marginBottom: '0.5rem' }}>{item.title}</p>
                    <h5 style={{ color: theme.colors.text, fontWeight: 'bold' }}>{item.value}</h5>
                  </div>
                </Col>
              ))}
            </Row>
          </Col>
        </Row >
      </Container>
    </>
  )
}

export default Achievement
