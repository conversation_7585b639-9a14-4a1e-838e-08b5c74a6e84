import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { getStudentList } from '../../redux/slice/studentSlice';

const useStudentList = () => {
  const [allStudents, setAllStudents] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const dispatch = useDispatch();

  useEffect(() => {
    const fetchStudentList = async () => {
      setIsLoading(true);
      try {
        const res = await dispatch(getStudentList());
        if (res.payload) {
          setAllStudents(res.payload);
          setError(null);
        } else {
          setError('Error fetching student list');
        }
      } catch (error) {
        setError(error.message || 'Error fetching student list');
      } finally {
        setIsLoading(false);
      }
    };

    fetchStudentList();
  }, [dispatch]);

  return { allStudents, isLoading, error };
};

export default useStudentList;