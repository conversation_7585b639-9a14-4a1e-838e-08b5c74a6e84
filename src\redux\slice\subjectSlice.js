import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Get Subjects Thunk
export const getSubjects = createAsyncThunk(
  'subject/getSubjects',
  async (_, { getState, rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SUBJECT}`,
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching subjects');
    }
  }
);

// Get Single Subject Thunk
export const getSubject = createAsyncThunk(
  'subject/getSubject',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SUBJECT}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching subject');
    }
  }
);

// Get Sections Thunk
export const getSections = createAsyncThunk(
  'subject/getSections',
  async (_, { rejectWithValue }) => {
    try {
      console.log("Making API call to fetch sections..."); // Debug log
      const response = await axios.get(`${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_SECTIONS}`);
      console.log("API response for getSections:", response.data); // Debug log
      return response.data;
    } catch (error) {
      console.error("Error in getSections API call:", error); // Debug log
      return rejectWithValue(error.response?.data || "Error fetching sections");
    }
  }
);

// Subject Click Counter Thunk
export const subjectClickCounter = createAsyncThunk(
  'subject/subjectClickCounter',
  async (subject_id, { rejectWithValue }) => {
    try {
      if (!subject_id) throw new Error("Subject ID is required");

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SUBJECT_CLICK_COUNTER}${subject_id}/`
      );

      return response.data;
    } catch (error) {
      console.error("Error updating click count:", error); // For debugging
      return rejectWithValue({
        message: error.response?.data?.message || "Error updating click count",
        status: error.response?.status || 500,
      });
    }
  }
);

// Section Click Counter Thunk
export const sectionClickCounter = createAsyncThunk(
  "subject/sectionClickCounter",
  async (sectionData, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SECTION_CLICK_COUNTER}`,
        sectionData // Directly pass the corrected payload
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

const subjectSlice = createSlice({
  name: 'subject',
  initialState: {
    subject: [],
    subjects: [],
    sections: [],
    isLoading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder

      // Get Subjects
      .addCase(getSubjects.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getSubjects.fulfilled, (state, action) => {
        state.isLoading = false;
        state.subjects = action.payload.data; 
      })
      .addCase(getSubjects.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Get Single Subject
      .addCase(getSubject.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getSubject.fulfilled, (state, action) => {
        state.isLoading = false;
        state.subject = action.payload;
      })
      .addCase(getSubject.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Get Sections
      .addCase(getSections.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getSections.fulfilled, (state, action) => {
        state.isLoading = false;
        state.sections = action.payload.data; // Assuming the API returns data in this format
      })
      .addCase(getSections.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Subject Click Counter
      .addCase(subjectClickCounter.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(subjectClickCounter.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(subjectClickCounter.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Section Click Counter
      .addCase(sectionClickCounter.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(sectionClickCounter.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(sectionClickCounter.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export default subjectSlice.reducer;
