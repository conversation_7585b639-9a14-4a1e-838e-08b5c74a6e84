import React from "react";
import { <PERSON>, But<PERSON> } from "react-bootstrap";
import { GiNotebook } from "react-icons/gi";
import { FaEye, FaPlus } from "react-icons/fa";
import { LuLanguages } from "react-icons/lu";

const TestSeriesCard = ({ test, addTestLoading, handleAddTestSeries, navigate }) => {
  return (
    <Card style={{ width: "20rem", margin: "10px" }} className="shadow-sm p-2">
      <div className="d-flex justify-content-start gap-4 align-items-center p-2 text-white rounded-top">
        <span className="bg-success px-2 py-1 rounded d-flex align-items-center fw-semibold fs-6">
          <GiNotebook className="me-2" />
          {test.course}
        </span>
        <span className={`${test.paid ? "bg-danger text-white" : "bg-success"} py-1 px-3 rounded fw-semibold`}>
          {test.paid ? "Paid" : "Free"}
        </span>
      </div>

      <Card.Body>
        <Card.Title className="fs-6">{test.sub_course_name}</Card.Title>
        <div className="d-flex justify-content-between align-items-center">
          <small>{test?.paper_details.length > 0 ? `${test?.paper_details.length} Tests` : '0 Tests'}</small>
        </div>
        <div className="d-flex align-items-center gap-3">
          <Button
            variant="success"
            className="w-75 btn-sm py-1 mt-4 d-flex align-items-center justify-content-center gap-2"
            disabled={addTestLoading}
            onClick={() => handleAddTestSeries(test?.subcourse_slug)}
          >
            {addTestLoading ? 'Adding..' : (<><FaPlus /> Add Test Series</>)}
          </Button>
          <Button
            variant="outline-success"
            className="w-25 btn-sm py-1 mt-4 d-flex align-items-center justify-content-center gap-2"
            onClick={() => navigate(`/dashboard/test-series/${test?.subcourse_slug}`)}
          >
            <FaEye /> View
          </Button>
        </div>
      </Card.Body>

      <div className="p-2 bg-light d-flex align-items-center justify-content-between">
        <small>
          <a href="#!" className="text-success text-decoration-none">
            Syllabus
          </a>
        </small>
        <small>
          <span className="ms-2">
            <LuLanguages /> {test.language}
          </span>
        </small>
      </div>
    </Card>
  );
};

export default TestSeriesCard;
