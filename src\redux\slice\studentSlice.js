import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Set default configurations for Axios
axios.defaults.withCredentials = true; // This ensures cookies are sent with every request
axios.defaults.baseURL = import.meta.env.VITE_BASE_URL; // Set the base URL for all Axios requests

// Function to get CSRF token from cookies
const getCSRFToken = () => {
  const name = "csrftoken";
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop().split(";").shift();
  return null;
};

// Function to get Authorization token (JWT or similar) from cookies or state
const getAuthToken = (getState) => {
  const state = getState();
  console.log(state);
  
  return {
    accessToken: state.student.student["JWT_Token"].access,
    refreshToken: state.student.student["JWT_Token"].refresh
  };
};

export const registerStudent = createAsyncThunk(
  'student/registerStudent',
  async ({studentData}, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_REGISTER_API}`,
        studentData,
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error("Registration error:", error);
      return rejectWithValue(error.response?.data || 'Something went wrong');
    }
  }
);

export const verifyOtp = createAsyncThunk(
  'student/verifyOtp',
  async ({otpData}, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_VERIFY_OTP_API}`,
        otpData,
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error("OTP verification error:", error);
      return rejectWithValue(error.response?.data || 'OTP verification failed');
    }
  }
);



// Async thunk to handle OTP resend API call
export const resendOtp = createAsyncThunk(
  'student/resendOtp',
  async (_, { rejectWithValue }) => {
    try {
      const csrfToken = getCSRFToken(); // Ensure this function is correctly implemented

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_RESEND_OTP}`,
        {},
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
            ...(csrfToken && { 'X-CSRFToken': csrfToken }), // Add CSRF token only if available
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error('Resend OTP Error:', error); // Debugging log
      return rejectWithValue(
        error.response?.data?.message || 'Resend OTP failed. Please try again.'
      );
    }
  }
);

// Async thunk to handle login API call
export const loginStudent = createAsyncThunk(
  'student/loginStudent',
  async ({loginData}, { rejectWithValue }) => {
    try {
      const csrfToken = getCSRFToken(); // Get the CSRF token from cookies

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_LOGIN_API}`,
        loginData,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken, // Include CSRF token in the headers
          },
        }
      );
      return response.data; // Return student data upon successful login
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Login failed');
    }
  }
);

// Async thunk to handle google login API call
export const googleLoginStudent = createAsyncThunk(
  'student/googleLoginStudent',
  async ({googleloginData}, { rejectWithValue }) => {
    try {
      const csrfToken = getCSRFToken(); // Get the CSRF token from cookies

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GOOGLE_LOGIN_API}`,
        googleloginData,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken, // Include CSRF token in the headers
          },
        }
      );
      return response.data; // Return student data upon successful login
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Login failed');
    }
  }
);

// Async thunk to handle logout API call
export const logoutStudent = createAsyncThunk(
  'student/logoutStudent',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { accessToken, refreshToken } = getAuthToken(getState);

      // Even if tokens are missing, we'll proceed with local logout
      if (!accessToken || !refreshToken) {
        console.log("Tokens are missing, proceeding with local logout");
        return true;
      }

      try {
        const formData = new FormData();
        formData.append('refresh', refreshToken);

        const response = await axios.post(
          `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_LOGOUT_API}`,
          formData,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );

        if (response.status === 204) {
          console.log("Logout successful");
          return true;
        }
      } catch (apiError) {
        console.log("API logout failed, proceeding with local logout");
        // We don't reject here - we want the reducer to clear the state anyway
        return true;
      }

      return true; // Always return success to trigger state cleanup
    } catch (error) {
      // Even in case of any unexpected error, we return success
      // This ensures the reducer will clear the state
      console.error("Logout error:", error);
      return true;
    }
  }
);


// Async thunk to get the student profile
export const getStudentProfile = createAsyncThunk(
  'student/getStudentProfile',
  async ({id}, { getState, rejectWithValue }) => {
    try {
      const {accessToken} = getAuthToken(getState);

      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PROFILE_API}${id}/`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
        }
      );
      return response.data; // Return student profile data
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch profile');
    }
  }
);

// Async thunk to edit the student profile
export const editStudentProfile = createAsyncThunk(
  'student/editStudentProfile',
  async ({profileData, id}, { getState, rejectWithValue }) => {
    try {
      const {accessToken} = getAuthToken(getState);

      const response = await axios.patch(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_EDIT_PROFILE_API}${id}/`,
        profileData,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data; // Return updated student profile
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to update profile');
    }
  }
);

// Async thunk to delete the student profile
export const deleteStudentProfile = createAsyncThunk(
  'student/deleteStudentProfile',
  async (_, { rejectWithValue }) => {
    try {
      const authToken = getAuthToken();

      const response = await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_DELETE_PROFILE_API}`,
        {
          headers: {
            'Authorization': `Bearer ${authToken}`,
          },
        }
      );
      return response.data; // Return success message on successful deletion
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to delete profile');
    }
  }
);


// Async thunk to handle "Forgot Password" API call
export const forgotPassword = createAsyncThunk(
  'student/forgotPassword',
  async (email, { rejectWithValue }) => {
    try {
      const csrfToken = getCSRFToken(); // Get CSRF token if needed

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_FORGOT_PASSWORD_API}`,
        { email },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken, // Include CSRF token in the headers
          },
        }
      );
      return response.data; // Return response data (success message or token)
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Something went wrong');
    }
  }
);

// Async thunk to handle "Forgot Password OTP Verification" API call
export const verifyForgotPasswordOtp = createAsyncThunk(
  'student/verifyForgotPasswordOtp',
  async ({ email, otp, new_password, confirm_password }, { rejectWithValue }) => {
    try {
      const csrfToken = getCSRFToken(); // Get CSRF token if needed

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_VERIFY_FORGOT_PASSWORD_OTP_API}`,
        { email, otp, new_password, confirm_password },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken, // Include CSRF token in the headers
          },
        }
      );
      return response.data; // Return response data (success message or token)
    } catch (error) {
      return rejectWithValue(error.response?.data || 'OTP verification failed');
    }
  }
);

// Async thunk to get the student list (for admin)
export const getStudentList = createAsyncThunk(
  'student/getStudentList',
  async (_, { rejectWithValue }) => {
    try {
      // const {accessToken} = getAuthToken();

      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_STUDENT_LIST_API}`,
        // {
        //   headers: {
        //     'Authorization': `Bearer ${authToken}`,
        //   },
        // }
      );
      return response.data; // Return the list of students
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch student list');
    }
  }
);

export const getReferralData = createAsyncThunk(
  'student/getReferralData',
  async (_, { getState, rejectWithValue }) => {
    try {
      const {accessToken} = getAuthToken(getState);

      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CREATE_QR_CODE}`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
        }
      );
      return response.data; // Return the list of students
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch referral data');
    }
  }
);

// Slice for handling student data
const studentSlice = createSlice({
  name: 'student',
  initialState: {
    student: null,
    otpVerified: false,
    loading: false,
    otpLoading: false,
    forgotPasswordLoading: false,
    forgotPasswordSuccess: false,
    error: null,
    studentList: [],
    // referralData: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Handle registration actions
      .addCase(registerStudent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(registerStudent.fulfilled, (state, action) => {
        state.loading = false;
        state.student = action.payload;
      })
      .addCase(registerStudent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle OTP verification actions
      .addCase(verifyOtp.pending, (state) => {
        state.otpLoading = true;
        state.error = null;
      })
      .addCase(verifyOtp.fulfilled, (state, action) => {
        state.otpLoading = false;
        state.otpVerified = true;
      })
      .addCase(verifyOtp.rejected, (state, action) => {
        state.otpLoading = false;
        state.error = action.payload;
      })

      // Handle OTP resend actions
      .addCase(resendOtp.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(resendOtp.fulfilled, (state, action) => {
        state.loading = false;
        // Optionally handle the successful OTP resend
      })
      .addCase(resendOtp.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle login actions
      .addCase(loginStudent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginStudent.fulfilled, (state, action) => {
        state.loading = false;
        state.student = action.payload;
      })
      .addCase(loginStudent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle google login actions
      .addCase(googleLoginStudent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(googleLoginStudent.fulfilled, (state, action) => {
        state.loading = false;
        state.student = action.payload;
      })
      .addCase(googleLoginStudent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle logout actions
      .addCase(logoutStudent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(logoutStudent.fulfilled, (state) => {
        state.loading = false;
        state.student = null;
        state.otpVerified = false; // Reset other relevant state
      })
      .addCase(logoutStudent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        // Clear user data even if the API call fails
        state.student = null;
        state.otpVerified = false;
      })

      // Handle profile actions
      .addCase(getStudentProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getStudentProfile.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(getStudentProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle edit profile actions
      .addCase(editStudentProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(editStudentProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.student = action.payload;
      })
      .addCase(editStudentProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle delete profile actions
      .addCase(deleteStudentProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteStudentProfile.fulfilled, (state) => {
        state.loading = false;
        state.student = null;
      })
      .addCase(deleteStudentProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Handle forgot password actions

      .addCase(forgotPassword.pending, (state) => {
        state.forgotPasswordLoading = true;
        state.error = null;
        state.forgotPasswordSuccess = false;
      })
      .addCase(forgotPassword.fulfilled, (state, action) => {
        state.forgotPasswordLoading = false;
        state.forgotPasswordSuccess = true;
      })
      .addCase(forgotPassword.rejected, (state, action) => {
        state.forgotPasswordLoading = false;
        state.error = action.payload;
        state.forgotPasswordSuccess = false;
      })
      // Handle forgot password OTP verification actions
      .addCase(verifyForgotPasswordOtp.pending, (state) => {
        state.forgotPasswordOtpLoading = true;
        state.error = null;
        state.forgotPasswordSuccess = false;
      })
      .addCase(verifyForgotPasswordOtp.fulfilled, (state, action) => {
        state.forgotPasswordOtpLoading = false;
        state.forgotPasswordSuccess = true;
      })
      .addCase(verifyForgotPasswordOtp.rejected, (state, action) => {
        state.forgotPasswordOtpLoading = false;
        state.error = action.payload;
        state.forgotPasswordSuccess = false;
      })

      // Handle student list actions
      .addCase(getStudentList.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getStudentList.fulfilled, (state, action) => {
        state.loading = false;
        state.studentList = action.payload;
      })
      .addCase(getStudentList.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle student referral actions
      .addCase(getReferralData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getReferralData.fulfilled, (state, action) => {
        state.loading = false;
        // state.referralData = action.payload;
      })
      .addCase(getReferralData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload
      });
  },
});

export default studentSlice.reducer;
