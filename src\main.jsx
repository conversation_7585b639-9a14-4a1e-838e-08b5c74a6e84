import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux'; // Import Provider to wrap the app
import './index.css';
import './styles/theme.css'; // Import theme styles
import App from './App.jsx';
import 'bootstrap/dist/css/bootstrap.min.css';
import store from './redux/store.js';
import { GoogleOAuthProvider } from "@react-oauth/google";
import { persistor } from './redux/persistor.js';
import { PersistGate } from 'redux-persist/integration/react';
import { HelmetProvider } from 'react-helmet-async';
import { ThemeProvider } from './context/ThemeContext.jsx';

const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID;
// console.log(GOOGLE_CLIENT_ID);

// 🔥 Constants for persistence cleanup
const PERSIST_KEY = 'root';
const EXPIRE_TIME_IN_MS = 6 * 60 * 60 * 1000; // 6 hours in milliseconds

// Function to check if persisted state has expired
const checkPersistExpiration = () => {
  const savedTimestamp = localStorage.getItem(`${PERSIST_KEY}_timestamp`);

  if (savedTimestamp) {
    const currentTime = new Date().getTime();
    const elapsedTime = currentTime - savedTimestamp;

    if (elapsedTime > EXPIRE_TIME_IN_MS) {
      // Clear persisted state
      persistor.purge();
      localStorage.removeItem(`${PERSIST_KEY}_timestamp`);
      console.log('🧹 Persisted state cleared after 6 hours.');
    }
  } else {
    // Set the current timestamp if not already set
    localStorage.setItem(`${PERSIST_KEY}_timestamp`, new Date().getTime());
  }
};

// Check expiration on app load
checkPersistExpiration();

// Initialize theme on app load
const initializeTheme = () => {
  try {
    const savedThemeMode = localStorage.getItem('themeMode');
    const systemPrefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

    let isDarkMode = false;
    let actualMode = 'system';

    if (savedThemeMode === 'dark') {
      isDarkMode = true;
      actualMode = 'dark';
    } else if (savedThemeMode === 'light') {
      isDarkMode = false;
      actualMode = 'light';
    } else if (savedThemeMode === 'system') {
      isDarkMode = systemPrefersDark;
      actualMode = 'system';
    } else {
      // No saved preference, default to system
      isDarkMode = systemPrefersDark;
      actualMode = 'system';
      localStorage.setItem('themeMode', 'system');
    }

    const root = document.documentElement;
    const body = document.body;

    // Clear any existing theme classes
    root.classList.remove('dark-theme', 'light-theme');
    body.classList.remove('dark-theme', 'light-theme');

    if (isDarkMode) {
      root.classList.add('dark-theme');
      body.classList.add('dark-theme');
      root.setAttribute('data-theme', 'dark');
    } else {
      root.classList.add('light-theme');
      body.classList.add('light-theme');
      root.setAttribute('data-theme', 'light');
    }

    // Store the actual applied state for React to read
    sessionStorage.setItem('initialThemeApplied', JSON.stringify({
      mode: actualMode,
      isDark: isDarkMode,
      timestamp: Date.now()
    }));

    console.log('Theme initialized:', { mode: actualMode, isDark: isDarkMode });
  } catch (error) {
    console.error('Error initializing theme:', error);
  }
};

// Initialize theme immediately
initializeTheme();

// Set an interval to check every hour if the app remains open
setInterval(() => {
  checkPersistExpiration();
}, 60 * 60 * 1000); // Every 1 hour

// Register the service worker
if ("serviceWorker" in navigator) {
  navigator.serviceWorker
    .register(`/firebase-messaging-sw.js`)
    .then((registration) => {
      console.log("Service Worker registered with scope: ", registration.scope);
    })
    .catch((error) => {
      console.error("Service Worker registration failed: ", error);
    });
}

// Wrap the App component with Provider and pass the store
createRoot(document.getElementById('root')).render(
  <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
    <Provider store={store}>
      <PersistGate
        loading={
          <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
            <img src="/shash_logo.png" alt="Loading..." style={{ width: 'auto', height: '3rem' }} />
            <h6 className="text-center text-muted" style={{ marginLeft: '1rem' }}>Loading......</h6>
          </div>
        }
        persistor={persistor}
      >
        <ThemeProvider>
          <HelmetProvider>
            <App />
          </HelmetProvider>
        </ThemeProvider>
      </PersistGate>
    </Provider>
  </GoogleOAuthProvider>
);
