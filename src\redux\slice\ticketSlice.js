import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the authToken from Redux state
const getAuthToken = (getState) => {
  const state = getState();
  return {
    accessToken: state.student.student["JWT_Token"].access,
  };
};

// Create a ticket
export const createTicket = createAsyncThunk(
  'tickets/create',
  async ({ data }, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      let requestBody;
      let headers = {
        Authorization: `Bearer ${accessToken}`,
      };

      // Check if attachments exist
      if (data.attachments instanceof File) {
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
          if (key === "attachments") {
            formData.append("attachments", data.attachments);
          } else if (Array.isArray(data[key])) {
            data[key].forEach((value) => {
              formData.append(`${key}`, value);
            });
          } else {
            formData.append(key, data[key]);
          }
        });

        requestBody = formData;
        headers["Content-Type"] = "multipart/form-data";
      } else {
        requestBody = data;
        headers["Content-Type"] = "application/json";
      }

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TICKET}`,
        requestBody,
        { headers }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to create ticket");
    }
  }
);

 

const ticketSlice = createSlice({
  name: 'tickets',
  initialState: {
    tickets: [],
    ticket: null,
    loading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Create ticket
      .addCase(createTicket.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createTicket.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(createTicket.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
     
  },
});

export default ticketSlice.reducer;
