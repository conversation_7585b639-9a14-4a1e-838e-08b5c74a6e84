import React, { useContext, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ThemeContext } from '../context/ThemeContext';
import {
  IoHomeOutline,
  IoHome,
  IoBookmarkOutline,
  IoBookmark,
  IoMenuOutline,
  IoMenu
} from 'react-icons/io5';
import { MdOutlineSmartToy } from 'react-icons/md';
import { useSelector } from 'react-redux';
import ChatBot from './ChatBot';
import Sidebar from './Sidebar';

const BottomNav = () => {
  const { isDarkMode } = useContext(ThemeContext);
  const location = useLocation();
  const navigate = useNavigate();
  const accessToken = useSelector((state) => state?.student?.student?.JWT_Token?.access);

  const [showSidebar, setShowSidebar] = useState(false);
  const [showChatBot, setShowChatBot] = useState(false);

  const currentTab = (() => {
    if (location.pathname.startsWith('/chatbot')) return 'chatbot';
    if (location.pathname.startsWith('/saved')) return 'saved';
    if (location.pathname === '/' || location.pathname.startsWith('/home')) return 'home';
    return '';
  })();

  const getTabColor = (tabName) => {
    const active = currentTab === tabName;
    return active ? '#198754' : isDarkMode ? '#666' : '#999';
  };

  const handleNav = (tab) => {
    switch (tab) {
      case 'home':
        navigate('/');
        break;
      case 'saved':
        navigate('/saved');
        break;
      case 'chatbot':
        setShowChatBot(true);
        break;
      case 'more':
        setShowSidebar(true);
        break;
      default:
        break;
    }
  };

  if (!accessToken) return null;

  return (
    <>
      {/* Add global style to ensure content is not hidden behind the navbar */}
      <style>
        {`
          .main-content, main, .app-content {
            padding-bottom: 70px !important;
          }
        `}
      </style>
      {showSidebar && (
        <Sidebar show={showSidebar} onClose={() => setShowSidebar(false)} />
      )}

      {showChatBot && (
        <ChatBot onClose={() => setShowChatBot(false)} />
      )}

      <nav
        style={{
          position: 'fixed',
          left: 0,
          right: 0,
          bottom: 0,
          height: 60,
          background: isDarkMode ? '#1a1a1a' : '#fff',
          borderTop: `1px solid ${isDarkMode ? '#333' : '#e0e0e0'}`,
          display: 'flex',
          zIndex: 1000,
          boxShadow: '0 -2px 8px rgba(0,0,0,0.08)',
        }}
        className="bottom-nav"
      >
        <button
          onClick={() => handleNav('home')}
          style={{
            flex: 1,
            background: 'none',
            border: 'none',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            padding: 0,
          }}
        >
          {currentTab === 'home' ? (
            <IoHome size={24} color={getTabColor('home')} />
          ) : (
            <IoHomeOutline size={24} color={getTabColor('home')} />
          )}
          <span style={{ fontSize: 12, color: getTabColor('home'), marginTop: 4 }}>
            Home
          </span>
        </button>

        <button
          onClick={() => handleNav('saved')}
          style={{
            flex: 1,
            background: 'none',
            border: 'none',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            padding: 0,
          }}
        >
          {currentTab === 'saved' ? (
            <IoBookmark size={24} color={getTabColor('saved')} />
          ) : (
            <IoBookmarkOutline size={24} color={getTabColor('saved')} />
          )}
          <span style={{ fontSize: 12, color: getTabColor('saved'), marginTop: 4 }}>
            Saved
          </span>
        </button>

        <button
          onClick={() => handleNav('chatbot')}
          style={{
            flex: 1,
            background: 'none',
            border: 'none',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            padding: 0,
          }}
        >
          <MdOutlineSmartToy size={24} color={getTabColor('chatbot')} />
          <span style={{ fontSize: 12, color: getTabColor('chatbot'), marginTop: 4 }}>
            Acharya AI
          </span>
        </button>

        <button
          onClick={() => handleNav('more')}
          style={{
            flex: 1,
            background: 'none',
            border: 'none',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            padding: 0,
          }}
        >
          {currentTab === 'more' ? (
            <IoMenu size={24} color={getTabColor('more')} />
          ) : (
            <IoMenuOutline size={24} color={getTabColor('more')} />
          )}
          <span style={{ fontSize: 12, color: getTabColor('more'), marginTop: 4 }}>
            More
          </span>
        </button>
      </nav>
    </>
  );
};

export default BottomNav;
