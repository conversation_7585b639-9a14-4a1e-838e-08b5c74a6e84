import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Function to get Auth Token from Redux store
const getAuthToken = (getState) => {
  const state = getState();
  return {
    accessToken: state?.student?.student["JWT_Token"]?.access,
    refreshToken: state?.student?.student["JWT_Token"]?.refresh,
  };
};


// Get All Test Series Thunk (with Auth)
export const getTestSeries = createAsyncThunk(
  'dashboard/getTestSeries',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TEST_SERIES_CARD}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      return response.data; // Handle data at the component level
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching test series');
    }
  }
);

// Get Test Series by Sub-Course Slug Thunk (with Auth)
export const getTestSeriesBySubCourse = createAsyncThunk(
  "dashboard/getTestSeriesBySubCourse",
  async (slug, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);

      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TEST_SERIES}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      console.log("Raw API Response:", response); // Debugging

      if (response.data?.testSeries) {
        return response.data.testSeries; // ✅ Correctly accessing API response
      } else {
        return rejectWithValue("No test series data found.");
      }
    } catch (error) {
      console.error("API Error:", error);
      return rejectWithValue(
        error.response?.data || "Error fetching test series for sub-course"
      );
    }
  }
);


const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState: {
    isLoading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Get All Test Series
      .addCase(getTestSeries.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getTestSeries.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(getTestSeries.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Get Test Series by Sub-Course Slug
      .addCase(getTestSeriesBySubCourse.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getTestSeriesBySubCourse.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(getTestSeriesBySubCourse.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export default dashboardSlice.reducer;
