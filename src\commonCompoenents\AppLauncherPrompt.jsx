import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";

const AppLauncherPrompt = () => {
  const [show, setShow] = useState(false);

  useEffect(() => {
    const userAgent = navigator.userAgent.toLowerCase();
    const isAndroid = userAgent.includes("android");
    const isMobile = /android|iphone|ipad|ipod/i.test(userAgent);

    if (!isMobile || !isAndroid) return;

    // Try to open app via deep link
    const scheme = "com.shashtrarth://";
    const tempLink = document.createElement("a");
    tempLink.href = scheme;
    tempLink.style.display = "none";
    document.body.appendChild(tempLink);
    tempLink.click();

    // After 5s, show popup if still in browser (i.e., app didn't open)
    const timeout = setTimeout(() => {
      if (!document.hidden) {
        setShow(true);
      }
    }, 5000);

    const handleVisibilityChange = () => {
      if (document.hidden) {
        clearTimeout(timeout);
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      clearTimeout(timeout);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);

  const handleOpenApp = () => {
    const scheme = "com.shashtrarth://";
    const fallback = "https://play.google.com/store/apps/details?id=com.shashtrarth";

    let didRedirect = false;

    const visibilityHandler = () => {
      if (document.hidden) {
        didRedirect = true;
        clearTimeout(fallbackTimeout);
        document.removeEventListener("visibilitychange", visibilityHandler);
      }
    };

    document.addEventListener("visibilitychange", visibilityHandler);

    const tempLink = document.createElement("a");
    tempLink.href = scheme;
    tempLink.style.display = "none";
    document.body.appendChild(tempLink);
    tempLink.click();

    const fallbackTimeout = setTimeout(() => {
      if (!didRedirect && !document.hidden) {
        window.location.href = fallback;
      }
    }, 2000);

    setShow(false);
  };

  const handleContinueInBrowser = () => {
    setShow(false);
  };

  return (
    <>
      <Modal
        show={show}
        onHide={handleContinueInBrowser}
        backdrop="static"
        centered
        style={{ backgroundColor: "rgba(0, 0, 0, 0.5)" }}
      >
        <Modal.Header closeButton>
          <Modal.Title>Open in App?</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          It looks like the app is not installed. Would you like to open it in the app or continue in the browser?
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleContinueInBrowser}>
            Continue in Browser
          </Button>
          <Button variant="primary" onClick={handleOpenApp}>
            Open in App
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default AppLauncherPrompt;
