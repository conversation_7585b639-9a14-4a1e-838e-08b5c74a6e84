import React from "react";
import { Line } from "react-chartjs-2";
import { Card } from "react-bootstrap";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const MarksDistribution = () => {
  // Dummy data for marks distribution
  const data = {
    labels: ["0", "0-4", "5-8", "9-12", "13-16", "17-20"],
    datasets: [
      {
        label: "Number of Students",
        data: [5, 12, 18, 10, 6], // Example student count for each range
        borderColor: "#007bff",
        backgroundColor: "rgba(0, 123, 255, 0.5)",
        tension: 0.4,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        display: true,
      },
    },
    scales: {
      x: {
        title: {
          display: true,
          text: "Marks",
        },
        grid: {
          display: false, // Removes vertical grid lines
        },
        border: {
          color: "#000", // Axis line color
          width: 2, // Make axis line bold
        },
      },
      y: {
        title: {
          display: true,
          text: "Number of Students",
        },
        border: {
          color: "#000", // Axis line color
          width: 2, // Make axis line bold
        },
      },
    },
  };

  return (
    <Card className="border-0 bg-light p-md-3 rounded">
      <Card.Body>
        <Card.Title className="h5">Marks Distribution</Card.Title>
        <Line data={data} options={options} />
      </Card.Body>
    </Card>
  );
};

export default MarksDistribution;
