import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Fetch all contacts
export const fetchContacts = createAsyncThunk(
  'contacts/fetchContacts',
  async (_, { getState, rejectWithValue }) => {
    try {
      const accessToken = getState()?.student?.student?.JWT_Token?.access;
      const response = await fetch(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_FETCH_CONTACTS}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      if (!response.ok) throw new Error('Failed to fetch contacts');
      return await response.json();
    } catch (err) {
      return rejectWithValue(err.message);
    }
  }
);

// Get relationship status by id
export const getRelationshipStatus = createAsyncThunk(
  'contacts/getRelationshipStatus',
  async (id, { getState, rejectWithValue }) => {
    try {
      const accessToken = getState()?.student?.student?.JWT_Token?.access;
      const url = `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_RELATIONSHIP_STATUS.replace(/\/$/, '')}/${id}/`;
      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      if (!response.ok) throw new Error('Failed to fetch relationship status');
      return await response.json();
    } catch (err) {
      return rejectWithValue(err.message);
    }
  }
);

// Get matched contacts
export const getMatchedContacts = createAsyncThunk(
  'contacts/getMatchedContacts',
  async (_, { getState, rejectWithValue }) => {
    try {
      const accessToken = getState()?.student?.student?.JWT_Token?.access;
      const response = await fetch(
        `${import.meta.env.VITE_BASE_URL}api/contacts/matched/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      if (!response.ok) throw new Error('Failed to fetch matched contacts');
      return await response.json();
    } catch (err) {
      return rejectWithValue(err.message);
    }
  }
);

const contactSlice = createSlice({
  name: 'contacts',
  initialState: {
    loading: false,
    error: null,
    matchedContacts: [],
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // fetchContacts
      .addCase(fetchContacts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchContacts.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(fetchContacts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // getRelationshipStatus
      .addCase(getRelationshipStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getRelationshipStatus.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(getRelationshipStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // getMatchedContacts
      .addCase(getMatchedContacts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getMatchedContacts.fulfilled, (state, action) => {
        state.loading = false;
        state.matchedContacts = action.payload?.results || action.payload || [];
      })
      .addCase(getMatchedContacts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default contactSlice.reducer;
