import React from 'react';
import { Container, <PERSON>, <PERSON> } from 'react-bootstrap';

const Footer = () => {
  return (
    <footer className="mt-5 bg-dark text-light py-5">
      <Container fluid>
        <Row className="justify-content-center">
          <Col md={12} lg={10}> 
            <Row className="justify-content-between">
              <Col md={3} sm={6} xs={12}> 
                <h5>Company</h5>
                <ul className="list-unstyled">
                  <li><a href="#" className="text-decoration-none text-light">About Us</a></li>
                  <li><a href="#" className="text-decoration-none text-light">Contact Us</a></li>
                  <li><a href="#" className="text-decoration-none text-light">Media</a></li>
                  <li><a href="#" className="text-decoration-none text-light">Careers</a></li>
                  <li><a href="#" className="text-decoration-none text-light">Careers for Faculties</a></li>
                  <li><a href="#" className="text-decoration-none text-light">Franchise</a></li>
                  <li><a href="#" className="text-decoration-none text-light">Content Partner</a></li>
                  <li><a href="#" className="text-decoration-none text-light">Online Support</a></li>
                </ul>
              </Col>

              <Col md={3} sm={6} xs={12}>
                <h5>Popular Goals</h5>
                <ul className="list-unstyled">
                  <li><a href="#" className="text-decoration-none text-light">Banking & Insurance</a></li>
                  <li><a href="#" className="text-decoration-none text-light">SSC</a></li>
                  <li><a href="#" className="text-decoration-none text-light">Railways</a></li>
                  <li><a href="#" className="text-decoration-none text-light">Teaching</a></li>
                  <li><a href="#" className="text-decoration-none text-light">Defence</a></li>
                  <li><a href="#" className="text-decoration-none text-light">Engineering</a></li>
                  <li><a href="#" className="text-decoration-none text-light">UPSC</a></li>
                  <li><a href="#" className="text-decoration-none text-light">NEET</a></li>
                  <li><a href="#" className="text-decoration-none text-light">Faculty Excellence Program</a></li>
                </ul>
              </Col>

              <Col md={3} sm={6} xs={12}>
                <h5>Products</h5>
                <ul className="list-unstyled">
                  <li><a href="#" className="text-decoration-none text-light">Mock Tests</a></li>
                  <li><a href="#" className="text-decoration-none text-light">Live Classes</a></li>
                  <li><a href="#" className="text-decoration-none text-light">Video Courses</a></li>
                  <li><a href="#" className="text-decoration-none text-light">Ebooks</a></li>
                  <li><a href="#" className="text-decoration-none text-light">Books</a></li>
                  <li><a href="#" className="text-decoration-none text-light">NEET Rank & College Predictor</a></li>
                  <li><a href="#" className="text-decoration-none text-light">CUET College Predictor</a></li>
                  <li><a href="#" className="text-decoration-none text-light">JAIIB Result Predictor</a></li>
                  <li><a href="#" className="text-decoration-none text-light">CAIIB Result Predictor</a></li>
                </ul>
              </Col>

              <Col md={3} sm={6} xs={12}>
                  <h5>Latest Mock Tests</h5>
                  <ul className="list-unstyled">
                    <li><a href="#" className="text-decoration-none text-light">JAIIB CAIB Mock Test</a></li>
                    <li><a href="#" className="text-decoration-none text-light">CUET Mock Test</a></li>
                    <li><a href="#" className="text-decoration-none text-light">SSC CGL Mock Test</a></li>
                    <li><a href="#" className="text-decoration-none text-light">SSC CHSL Mock Test</a></li>
                    <li><a href="#" className="text-decoration-none text-light">SSC CGL Tier-II Mock Test</a></li>
                    <li><a href="#" className="text-decoration-none text-light">SSC MTS Mock Test</a></li>
                    <li><a href="#" className="text-decoration-none text-light">RRB NTPC Mock Test</a></li>
                  </ul>
              </Col>
            </Row>
          </Col>
        </Row>
      </Container>
    </footer>
  );
};

export default Footer;