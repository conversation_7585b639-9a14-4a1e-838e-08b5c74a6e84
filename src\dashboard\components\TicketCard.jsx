import React, { useState } from 'react';
import { Card, Button, Modal, Row, Col, Badge } from 'react-bootstrap';
import { useDispatch } from 'react-redux';
import { FaEnvelope, FaPhoneAlt, FaClipboard, FaTag, FaFlag, FaCalendarAlt, FaUserAlt, FaExclamationTriangle, FaCircle } from 'react-icons/fa'; // Importing different React Icons
import { AiOutlineFileAdd } from 'react-icons/ai'; // Additional icon for attachments

const TicketCard = ({ ticket }) => {
  const [showModal, setShowModal] = useState(false);

  // Handle opening and closing the ticket details modal
  const handleViewClick = () => {
    setShowModal(true);
  };
  const handleCloseModal = () => setShowModal(false);

  // Determine background class based on ticket status
  const getStatusClass = (status) => {
    switch (status) {
      case 'closed':
        return 'text-success';

      case 'in-progress':
        return 'text-warning';

      default:
        return 'text-danger';
    }
  };

  return (
    <>
      {/* Main Card - Showing essential information */}
      <Card className="mt-3 shadow-sm border-1 rounded-3 p-3 position-relative">
        <Card.Body>
          {/* Status Badge in the Top Right Corner */}
          {/* <Badge
            className="position-absolute top-0 end-0 m-3"
            bg={getStatusClass(ticket?.ticket_status)}
          >
            {ticket?.ticket_status}
          </Badge> */}

          <Row className="mb-2">
            <Col>
              <Card.Title>{ticket?.subject}</Card.Title>
              <Card.Subtitle className="mb-2 text-muted">
                <FaCircle className={`${getStatusClass(ticket?.ticket_status)}`} />{" "}
                {ticket?.ticket_status}
              </Card.Subtitle>
            </Col>
          </Row>

          <Row className="mb-3">
            <Col md={12}>
              <p>
                <FaUserAlt className="text-success" /> <strong>Cust. Profile:</strong>{" "}
                {ticket?.customer?.user?.first_name} {ticket?.customer?.user?.last_name}
              </p>
              <p>
                <FaEnvelope className="text-success" /> <strong>Cust. Email:</strong>{" "}
                {ticket?.customer?.user?.email}
              </p>
              <p>
                <FaPhoneAlt className="text-success" /> <strong>Cust. Contact:</strong>{" "}
                {ticket?.customer?.contact}
              </p>
            </Col>
            <Col md={12}>
              <p>
                <FaFlag className="text-success" /> <strong>Cust. Status:</strong>{" "}
                {ticket?.customer?.account_status}
              </p>
              <p>
                <FaTag className="text-success" /> <strong>Ticket Assign:</strong>{" "}
                {ticket?.ticket_assign || "Not assigned"}
              </p>
            </Col>
          </Row>

          {/* Action Buttons */}
          <Row className="mt-3">
            <Col className="d-flex justify-content-center">
              <Button
                variant="success"
                className="m-1 w-100"
                size="sm"
                onClick={handleViewClick}
              >
                View More
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Modal for Viewing All Ticket Details */}
      <Modal show={showModal} onHide={handleCloseModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>{ticket?.subject}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {/* Multi-column layout for ticket details */}
          <Row className="mb-3">
            <Col md={6}>
              <p><FaClipboard className="text-success" /> <strong>Description:</strong> {ticket?.description}</p>
              <p><FaFlag className="text-success" /> <strong>Priority:</strong> {ticket?.priority}</p>
              <p><FaTag className="text-success" /> <strong>Tags:</strong> {ticket?.tags}</p>
              <p><FaExclamationTriangle className="text-success" /> <strong>Resolve Summary:</strong> {ticket?.resolve_summary || "No summary provided"}</p>
            </Col>
            <Col md={6}>
              <p><FaFlag className={`${getStatusClass(ticket?.ticket_status)}`} /> <strong>Status:</strong> {ticket?.ticket_status}</p>
              <p><FaCalendarAlt className="text-success" /> <strong>Update Date:</strong> {new Date(ticket?.update_date).toLocaleString()}</p>
              <p><FaCalendarAlt className="text-success" /> <strong>Resolved Date:</strong> {ticket?.date_resolved ? new Date(ticket?.date_resolved).toLocaleString() : 'Not resolved yet'}</p>
            </Col>
          </Row>

          {/* Attachments Section */}
          {ticket?.attachments && (
            <div className="mt-3">
              <strong><AiOutlineFileAdd className="text-success" /> Attachments: </strong>
              <div>
                <img
                  src={`${import.meta.env.VITE_BASE_URL}/${ticket?.attachments}`}
                  alt={`${ticket?.subject}`}
                  style={{ width: '100%', maxHeight: '300px', objectFit: 'cover', marginBottom: '10px' }}
                />
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseModal}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default TicketCard;
