import React from "react";
import { Container, Row, Col, Card } from "react-bootstrap";
import { <PERSON>aFile<PERSON>lt, Fa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Fa<PERSON><PERSON><PERSON><PERSON><PERSON>, FaTicketAlt } from "react-icons/fa";
import { Link } from "react-router-dom";
import { useTheme } from "../../context/ThemeContext"; // Added import for Link

const ExamInfoSection = () => {
  const { isDarkMode, theme } = useTheme();

  return (
    <section id="exams" className="pt-5">
    <Container fluid style={{ backgroundColor: "#e0f8d8" }} className="py-5">
      <Container>
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h2
            className="text-center"
            style={{
              color: '#000000', // Always keep black text regardless of theme
              fontWeight: 'bold'
            }}
          >
            Stay Updated with Competitive Exams
          </h2>
          {/* <Link to="https://contributor.shashtrarth.com/" className="btn btn-success">
            Visit Now!
          </Link> */}
          <Link to="#" className="btn btn-success">
            Coming Soon!
          </Link>
        </div>
        <Row className="text-center">
          <Col md={3} sm={6} className="mb-4">
            <Card className="shadow p-3 border-0" style={{ backgroundColor: "#ffffff" }}>
              <FaCalendarCheck size={35} color="#28a745" className="mb-3 mx-auto" />
              <Card.Body>
                <Card.Title>Upcoming Exams</Card.Title>
                <Card.Text>Get details on all upcoming competitive exams and their schedules.</Card.Text>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} sm={6} className="mb-4">
            <Card className="shadow p-3 border-0" style={{ backgroundColor: "#ffffff" }}>
              <FaTicketAlt size={35} color="#28a745" className="mb-3 mx-auto" />
              <Card.Body>
                <Card.Title>Hall Tickets</Card.Title>
                <Card.Text>Download hall tickets and admit cards for your exams easily.</Card.Text>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} sm={6} className="mb-4">
            <Card className="shadow p-3 border-0" style={{ backgroundColor: "#ffffff" }}>
              <FaClipboardList size={35} color="#28a745" className="mb-3 mx-auto" />
              <Card.Body>
                <Card.Title>Syllabus</Card.Title>
                <Card.Text>Access the latest syllabus and study guides for various exams.</Card.Text>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} sm={6} className="mb-4">
            <Card className="shadow p-3 border-0" style={{ backgroundColor: "#ffffff" }}>
              <FaFileAlt size={35} color="#28a745" className="mb-3 mx-auto" />
              <Card.Body>
                <Card.Title>Test Patterns</Card.Title>
                <Card.Text>Understand the latest test patterns to prepare effectively.</Card.Text>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </Container>
    </section>
  );
};

export default ExamInfoSection;