import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { MdOutlinePlayCircleFilled, MdOutlineFileCopy, MdQuiz, MdDescription, MdOutlineBook } from 'react-icons/md';
import { useTheme } from '../../../context/ThemeContext';

const TotalStudentAnalytic = () => {
  const { isDarkMode, theme } = useTheme();
  const data = [
    { 
      icon: <MdOutlinePlayCircleFilled size={30} color="white" />,
      title: "Live Classes",
      bgColor: "#28A745" // Green background
    },
    { 
      icon: <MdOutlineFileCopy size={30} color="white" />,
      title: "Live Test & Quizzes",
      bgColor: "#20c997" // Teal background
    },
    { 
      icon: <MdQuiz size={30} color="white" />,
      title: "Free Quizzes",
      bgColor: "#fd7e14" // Orange background
    },
    { 
      icon: <MdDescription size={30} color="white" />,
      title: "Prev. Year Papers",
      bgColor: "#ffc107" // Yellow background
    },
    { 
      icon: <MdOutlineBook size={30} color="white" />,
      title: "GK & CA",
      bgColor: "#6c757d" // Gray background
    }
  ];

  return (
    <Container className="mt-5">
      <Row className="justify-content-center m-md-0 m-3">
        <Col
          md={8}
          className='rounded-4 border border-1 shadow-sm'
          style={{
            backgroundColor: theme.colors.cardBackground, // Always white (as per requirement)
            borderColor: theme.colors.cardBorder,
            color: theme.colors.cardText,
            transition: "all 0.3s ease"
          }}
        >
          <div className="d-flex justify-content-between flex-wrap">
            {data.map((item, index) => (
              <div key={index} className="text-center p-3">
                <div 
                  className="rounded-circle p-2 mb-2"
                  style={{
                    width: '30px',
                    height: '30px',
                    backgroundColor: item.bgColor,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    margin: '0 auto' // Center the icon in the circle
                  }}
                >
                  {item.icon}
                </div>
                <small className='fw-semibold'>{item.title}</small>
                <small className="text-muted d-block mt-1">Coming Soon!</small> {/* Moved span below */}
              </div>
            ))}
          </div>
        </Col>
      </Row>
    </Container>
  );
};

export default TotalStudentAnalytic;
