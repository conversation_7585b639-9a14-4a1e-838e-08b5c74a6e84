import React, { useEffect, useState } from 'react';
import {  FaBars, FaTimes } from "react-icons/fa";
import NavBar from "../../commonCompoenents/NavBar";
import Footer from "../components/Footer";
import { Container, Row, Col, <PERSON>, Card } from 'react-bootstrap';

const PrivacyPolicy = () => {
  const [activeSection, setActiveSection] = useState('');
  const [isNavVisible, setIsNavVisible] = useState(true);

  const toggleNav = () => {
    setIsNavVisible(!isNavVisible);
  };

  useEffect(() => {
    const handleScroll = () => {
      const sections = Array.from({ length: 16 }, (_, i) => `section${i + 1}`);
      let currentSection = '';
      sections.forEach((section) => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= 100 && rect.bottom >= 100) {
            currentSection = section;
          }
        }
      });
      setActiveSection(currentSection);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <>
      <NavBar />
      <section style={{ backgroundColor: '#ecd9c6' }}>
        <Container className="mt-5">
          {/* Privacy Policy Header */}
          <Row className="text-center mb-5">
            <Col>
              <h1 className="mt-5 display-4 text-uppercase font-weight-bold">Privacy Policy</h1>
              <p className="lead">Your privacy is important to us. Please read our policy carefully.</p>
              <p><strong>Last Updated:</strong> 2025 </p>
            </Col>
          </Row>

          {/* Privacy Policy Content - Multi-Column Layout */}
          <Row>
            {/* Sidebar - Floating Navigation Links */}
            <div style={{
              position: 'fixed',
              top: '15%',
              right: '2%',
              width: isNavVisible ? '250px' : '50px',
              zIndex: 1000,
              backgroundColor: '#fff',
              padding: isNavVisible ? '10px' : '5px',
              borderRadius: '8px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
              transition: 'width 0.3s, padding 0.3s'
            }}>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  cursor: 'pointer'
                }}
                onClick={toggleNav}
              >
                <h4 className="font-weight-bold" style={{ display: isNavVisible ? 'block' : 'none' }}>Navigation</h4>
                {isNavVisible ? <FaTimes /> : <FaBars />}
              </div>
              {isNavVisible && (
                <ul style={{ listStyleType: 'none', padding: 0, fontSize: "0.8rem" }}>
                  {[
                    "1. Introduction",
                    "2. Data Collection",
                    "3. Purpose of Data Collection",
                    "4. Data Sharing",
                    "5. Data Security",
                    "6. Data Retention",
                    "7. User Rights",
                    "8. Cookies and Tracking",
                    "9. International Data Transfers",
                    "10. Minors and Children’s Privacy",
                    "11. Changes to Privacy Policy",
                    "12. User Consent",
                    "13. Legal Basis for Processing",
                    "14. Third-Party Links and Integrations",
                    "15. Contact Information",
                    "16. Applicable Laws and Regulations"
                  ].map((title, i) => (
                    <li key={i}>
                      <a
                        href={`#section${i + 1}`}
                        style={{
                          color: activeSection === `section${i + 1}` ? 'green' : 'black',
                          fontWeight: activeSection === `section${i + 1}` ? 'bold' : 'normal',
                          textDecoration: 'none'
                        }}
                      >
                        {title}
                      </a>
                    </li>
                  ))}
                </ul>
              )}
            </div>

            {/* Main Content Section */}
            <Col lg={9} md={8}>
              {[
                {
                  id: "section1",
                  title: "1. Introduction",
                  content: (
                    <>
                      <p><strong>Overview of Privacy Commitment:</strong> At Librainian, we take user privacy seriously and prioritize the security of your personal data.</p>
                      <p><strong>Scope:</strong> This privacy policy applies to registered users of our platform.</p>
                      <p><strong>Definitions:</strong></p>
                      <ul>
                        <li><strong>Personal Data:</strong> Refers to any information related to an identified or identifiable person.</li>
                        <li><strong>Service:</strong> Refers to the Librainian platform, including its web and mobile applications.</li>
                        <li><strong>Third-party Analytical Tools:</strong> Refer to external services such as Google Analytics that help us understand user behavior.</li>
                      </ul>
                    </>
                  )
                },
                {
                  id: "section2",
                  title: "2. Data Collection",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Types of Data Collected</h6>
                      <ul>
                        <li><strong>Personal Information:</strong> Full name, email address, contact information, father’s name, address, age, gender, location, locality, city, state, image (optional), and course.</li>
                        <li><strong>Financial Information:</strong> Payment details processed through Razorpay and PhonePe, including payment mode.</li>
                        <li><strong>Behavioral Data:</strong> Clickstream data and browsing patterns within the app, collected via tools like Google Analytics and Microsoft Clarity.</li>
                        <li><strong>Sensitive Data:</strong> Not collected (e.g., health or biometric data).</li>
                        <li><strong>Device Data:</strong> Metadata such as device IDs, mobile carrier, time zone (inferred via browser, primarily Chrome).</li>
                      </ul>
                      <h6 className="font-weight-bold">b. Collection Methods</h6>
                      <ul>
                        <li>Registration Forms: Users provide personal details during signup.</li>
                        <li>Automated Tracking: Cookies, pixels, and tracking technologies gather usage data.</li>
                        <li>Surveys and Feedback: Optional data provided via forms.</li>
                        <li>Customer Support: Info collected from support tickets.</li>
                      </ul>
                      <h6 className="font-weight-bold">c. Third-Party Data</h6>
                      <ul>
                        <li>Social Media Login: To be implemented.</li>
                        <li>Third-Party APIs: Data from integrated services.</li>
                      </ul>
                    </>
                  )
                },
                {
                  id: "section3",
                  title: "3. Purpose of Data Collection",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Service Functionality</h6>
                      <ul>
                        <li><strong>Account Creation and Management:</strong> Using personal information to create and manage user accounts, activate packages, and render services.</li>
                        <li><strong>Service Personalization:</strong> Tailoring content and features based on user preferences and activity.</li>
                      </ul>
                      <h6 className="font-weight-bold">b. Performance and Analytics</h6>
                      <ul>
                        <li><strong>Internal Analytics:</strong> Using data to analyze and improve service performance (e.g., monitoring load times, feature usage).</li>
                      </ul>
                      <h6 className="font-weight-bold">c. Communication and Notifications</h6>
                      <ul>
                        <li><strong>Transactional Emails:</strong> Emails for order confirmations, password resets, or account-related messages.</li>
                        <li><strong>Marketing:</strong> Users may receive marketing emails and notifications.</li>
                        <li><strong>Push Notifications:</strong> Under development for mobile app users.</li>
                      </ul>
                      <h6 className="font-weight-bold">d. Security and Fraud Detection</h6>
                      <ul>
                        <li><strong>Account Security:</strong> Personal data helps prevent unauthorized access. Security features are handled by Cloudflare, including a Zone-level Web Application Firewall (WAF) and SSL/TLS encryption.</li>
                        <li><strong>Monitoring for Suspicious Activity:</strong> Malicious activities, fraud attempts, or data breaches are monitored by Cloudflare.</li>
                      </ul>
                    </>
                  )
                },
                {
                  id: "section4",
                  title: "4. Data Sharing",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Sharing with Third-Party Services</h6>
                      <ul>
                        <li><strong>Purpose of Sharing:</strong> Data is shared with third-party services for payment processing (Razorpay), security (Cloudflare), analytics (Google Analytics), and communication (BulkSMS, Gmail).</li>
                        <li><strong>Vendor Agreements:</strong> All third-party providers are contractually bound to protect user data.</li>
                        <li><strong>List of Third-Party Partners:</strong> Google Analytics, Cloudflare, Microsoft Clarity, Razorpay, BulkSMS, Gmail.</li>
                      </ul>
                      <h6 className="font-weight-bold">b. Sharing for Legal Compliance</h6>
                      <ul>
                        <li><strong>Government Requests:</strong> We comply with government or law enforcement requests where required by law. We will disclose user data when requested by authorized bodies for legal or regulatory purposes.</li>
                        <li><strong>Legal Disputes:</strong> Data may be shared in the event of legal proceedings or disputes, to protect our rights, comply with a subpoena, or respond to a legal claim.</li>
                      </ul>
                    </>
                  )
                },
                {
                  id: "section5",
                  title: "5. Data Security",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Security Measures</h6>
                      <ul>
                        <li><strong>Encryption:</strong> SSL/TLS for secure transmission.</li>
                        <li><strong>Access Control:</strong> Restricted to authorized personnel.</li>
                        <li><strong>Monitoring:</strong> Regular audits and access tracking.</li>
                      </ul>
                      <h6 className="font-weight-bold">b. Data Breach Protocol</h6>
                      <ul>
                        <li><strong>Notification:</strong> Prompt alerts to affected users.</li>
                        <li><strong>Mitigation:</strong> Measures like password resets or account disabling.</li>
                      </ul>
                    </>
                  )
                },
                {
                  id: "section6",
                  title: "6. Data Retention",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Retention Period</h6>
                      <ul>
                        <li>While the account remains active.</li>
                        <li>Longer if required by law (e.g., tax or audit compliance).</li>
                      </ul>
                      <h6 className="font-weight-bold">b. Deletion Requests</h6>
                      <ul>
                        <li><strong>Process:</strong> Request via contact support.</li>
                        <li><strong>Exceptions:</strong> Certain data may be retained.</li>
                      </ul>
                    </>
                  )
                },
                {
                  id: "section7",
                  title: "7. User Rights",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Access and Transparency</h6>
                      <p>Users may view and request copies of their data.</p>
                      <h6 className="font-weight-bold">b. Correction</h6>
                      <p>Update or fix profile details at any time.</p>
                      <h6 className="font-weight-bold">c. Deletion</h6>
                      <p>Request to delete personal data ("Right to be Forgotten").</p>
                      <h6 className="font-weight-bold">d. Portability</h6>
                      <p>Data provided in structured, machine-readable format.</p>
                      <h6 className="font-weight-bold">e. Objection to Processing</h6>
                      <p>Opt out of marketing or automated profiling.</p>
                    </>
                  )
                },
                {
                  id: "section8",
                  title: "8. Cookies and Tracking",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Types of Cookies</h6>
                      <ul>
                        <li><strong>Session Cookies:</strong> Maintain user session.</li>
                        <li><strong>Persistent Cookies:</strong> Remember login and preferences.</li>
                        <li><strong>Third-Party Cookies:</strong> Used for ads or analytics.</li>
                      </ul>
                      <h6 className="font-weight-bold">b. Cookie Management</h6>
                      <ul>
                        <li><strong>Browser Settings:</strong> Users can block or clear cookies.</li>
                        <li><strong>Consent Banner:</strong> Consent will be requested via popup.</li>
                      </ul>
                      <h6 className="font-weight-bold">c. Tracking Technologies</h6>
                      <p>Pixels & Tags: Includes pixel tags and web beacons.</p>
                    </>
                  )
                },
                {
                  id: "section9",
                  title: "9. International Data Transfers",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Storage Locations</h6>
                      <p>Data may be stored outside your country, including the EU and US.</p>
                      <h6 className="font-weight-bold">b. Safeguards</h6>
                      <p>Standard Contractual Clauses and Privacy Shield frameworks in place.</p>
                    </>
                  )
                },
                {
                  id: "section10",
                  title: "10. Minors and Children’s Privacy",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Age Restrictions</h6>
                      <p>Minimum age: [X] years old.</p>
                      <p>Parental consent required if data from minors is collected.</p>
                      <h6 className="font-weight-bold">b. Parental Rights</h6>
                      <p>Access, manage, or request deletion of a child’s data.</p>
                    </>
                  )
                },
                {
                  id: "section11",
                  title: "11. Changes to Privacy Policy",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Version History</h6>
                      <p>Users can view previous policy versions.</p>
                      <p>Major changes will be highlighted.</p>
                      <h6 className="font-weight-bold">b. Effective Date</h6>
                      <p>This version is effective as of [Date].</p>
                    </>
                  )
                },
                {
                  id: "section12",
                  title: "12. User Consent",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Consent at Signup</h6>
                      <p>Users consent to the policy during account creation.</p>
                      <h6 className="font-weight-bold">b. Withdrawing Consent</h6>
                      <p>Opt-out options available. Some features may be restricted if consent is withdrawn.</p>
                    </>
                  )
                },
                {
                  id: "section13",
                  title: "13. Legal Basis for Processing",
                  content: (
                    <>
                      <ul>
                        <li><strong>Consent:</strong> Where legally required.</li>
                        <li><strong>Legitimate Interest:</strong> For service delivery and analytics.</li>
                        <li><strong>Contractual Necessity:</strong> To fulfill user agreements.</li>
                        <li><strong>Legal Obligations:</strong> Tax, compliance, or audit-related retention.</li>
                      </ul>
                    </>
                  )
                },
                {
                  id: "section14",
                  title: "14. Third-Party Links and Integrations",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Third-Party Websites</h6>
                      <p>This policy does not apply to external websites.</p>
                      <h6 className="font-weight-bold">b. App Integrations</h6>
                      <p>Data may be shared via OAuth or API-based integrations.</p>
                    </>
                  )
                },
                {
                  id: "section15",
                  title: "15. Contact Information",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Privacy Concerns</h6>
                      <p><strong>Email:</strong> [your email]</p>
                      <p><strong>Address:</strong> [your mailing address]</p>
                      <h6 className="font-weight-bold">b. Data Protection Officer</h6>
                      <p><strong>Contact:</strong> [DPO contact details] (if applicable)</p>
                    </>
                  )
                },
                {
                  id: "section16",
                  title: "16. Applicable Laws and Regulations",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Governing Laws</h6>
                      <p>This policy complies with applicable laws such as GDPR, CCPA, etc.</p>
                      <h6 className="font-weight-bold">b. User Rights by Region</h6>
                      <p>Regional differences in user rights will be respected.</p>
                    </>
                  )
                }
              ].map(({ id, title, content }) => (
                <Card id={id} className="border-0 mb-5" key={id}>
                  <Card.Body>
                    <h5 className="text-uppercase font-weight-bold">{title}</h5>
                    {content}
                  </Card.Body>
                </Card>
              ))}
            </Col>
          </Row>
        </Container>
      </section>
      <Footer />
    </>
  );
};

export default PrivacyPolicy;
