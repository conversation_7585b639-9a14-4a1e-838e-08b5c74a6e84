import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { Button } from "react-bootstrap";
import { resendOtp } from "../redux/slice/studentSlice";

const ResendOtpButton = () => {
  const dispatch = useDispatch();
  const [timer, setTimer] = useState(10); // 60-second timer
  const [attempts, setAttempts] = useState(0); // Resend attempts (max 3)
  const [isDisabled, setIsDisabled] = useState(true); // Button state

  useEffect(() => {
    let interval;
    if (timer > 0) {
      interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
    } else {
      setIsDisabled(false); // Enable button after timer ends
    }

    return () => clearInterval(interval);
  }, [timer]);

  const handleResendOtp = () => {
    if (attempts < 3) {
      dispatch(resendOtp());
      setAttempts((prev) => prev + 1);
      setTimer(60); // Reset timer
      setIsDisabled(true);
    }
  };

  return (
    <>
      <div className="d-flex justify-content-center m-1">
        <Button
          onClick={handleResendOtp}
          disabled={isDisabled || attempts >= 3}
          variant={isDisabled || attempts >= 3 ? "secondary" : "primary"}
          className="px-4 py-2 rounded-md text-white font-semibold transition-all"
        >
          {attempts >= 3 ? "Max Attempts Reached" : `Resend OTP${timer > 0 ? ` (${timer}s)` : ""}`}
        </Button>
      </div>
      <h6 className="text-sm text-center text-gray-600">
        {attempts < 3
          ? `Attempts: ${attempts}/3`
          : "You have reached the maximum attempts."}
      </h6>
    </>
  );
};

export default ResendOtpButton;
