import React, { useState } from "react";
import { Con<PERSON><PERSON>, Navbar, <PERSON>, Col, Button, Spinner} from "react-bootstrap"; 
import OverlayTrigger from "react-bootstrap/OverlayTrigger";
import Tooltip from "react-bootstrap/Tooltip";
import TestStarted from "./TestStarted";
import TestInstruction from "./TestInstruction";
import GeneralInstruction from "./GeneralInstruction";
import { useDispatch, useSelector } from "react-redux";

const TestAttempt = () => {
  const [page, setPage] = useState(1);
  const [isChecked, setIsChecked] = useState(false);
  const [isTestStarted, setIsTestStarted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  // const [testPaper, setTestPaper] = useState();

  const dispatch = useDispatch();

  const testPaper = useSelector((state) => state?.paper?.testData);

  const handleNextPage = () => {
    if (page === 1) {
      setPage(2);
    }
  };

  const handleBackPage = () => {
    setPage(1);
  };

  const handleTestStart = async() => {
    if (isChecked) {
      setIsLoading(true);
      // try {
      //   const res = await dispatch(generatePaper());
      //   if(res?.meta?.requestStatus === 'fulfilled'){
      //     setTestPaper(res?.payload);
      //     setIsTestStarted(true);
      //     setIsLoading(false);
      //   }
      // } catch (error) {
      //   console.log("An error occured while generating test paper", error);
      // }finally{
      //   setIsLoading(false);
      // }
      setTimeout(() => {
        setIsLoading(false);
        setIsTestStarted(true);
      }, 3000); // Show loading screen for 3 seconds
    }
  };

  if (isLoading) {
    return (
      <div className="d-flex flex-column justify-content-start align-items-center pt-5 vh-100">
        <Spinner animation="border" variant="success" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="text-secondary text-center">Please wait...</p>
      </div>
    );
  }

  if (isTestStarted) {
    // return <TestStarted />;
    return <TestStarted testData2={testPaper}/>;
  }

  return (
    <>
      {/* Navbar */}
      <Navbar className="shadow-sm" bg="light" expand="lg">
        <Container fluid>
          <Navbar.Brand>
            <img src="/logo.png" alt="Logo" className="d-inline-block align-top" width="180" />
            <span className="ms-3">Test Name</span>
          </Navbar.Brand>
        </Container>
      </Navbar>

      {/* Page content */}
      <Container className="mt-4" fluid>
        {page === 1 && ( <GeneralInstruction/> )}
        {page === 2 && ( <TestInstruction isChecked={isChecked} setIsChecked={setIsChecked}/> )}
      </Container>

      <div className="fixed-bottom bg-light py-3 border border-top-3">
        <Container>
          <Row className="justify-content-between">
            {page === 1 && (
              <Col className="d-flex justify-content-end">
                <Button onClick={handleNextPage} className="ms-2" variant="primary">Next</Button>
              </Col>
            )}

            {page === 2 && (
              <>
                <Col>
                  <Button variant="secondary" onClick={handleBackPage}>Previous</Button>
                </Col>
                <Col>
                  <OverlayTrigger placement="top"
                    overlay={
                      !isChecked ? (
                        <Tooltip id="tooltip-disabled">
                          Please check the declaration to proceed.
                        </Tooltip>
                      ) : (
                        <></>
                      )
                    }
                  >
                    <span className="d-inline-block">
                      <Button variant="success" disabled={!isChecked} onClick={handleTestStart} style={!isChecked ? { pointerEvents: "none" } : {}}>
                        I am Ready to Begin
                      </Button>
                    </span>
                  </OverlayTrigger>
                </Col>
              </>
            )}
          </Row>
        </Container>
      </div>
    </>
  );
};

export default TestAttempt;
