import React from 'react'
import { <PERSON><PERSON>, Card, Image } from 'react-bootstrap'
import { FaCheckCircle } from 'react-icons/fa'

const PassBox = () => {
  return (
    <>
        <Card className="">
                <div className="position-absolute top-0 start-0">
                      <span className="badge bg-warning text-dark">NEW</span>
                </div>
                <div className="p-3">
                  <div className="d-flex justify-content-start align-items-center gap-2">
                    
                        <Image
                          src="/logo.png"
                          alt="sahshtrarth Logo"
                          className=""
                          width={180}
                        />
                        <span className="badge bg-success text-light">
                          PASS PRO
                        </span>
                  </div>
                  <h5 className="mt-3">
                      The Ultimate Subscription for Pro Aspirants
                  </h5>
                  <ul className="list-unstyled">
                        <li>
                          <FaCheckCircle className="me-2 text-success" /> All
                          Test Series
                        </li>
                        <li>
                          <FaCheckCircle className="me-2 text-success" /> All
                          Prev. Year Paper
                        </li>
                        <li>
                          <FaCheckCircle className="me-2 text-success" />{" "}
                          Unlimited Practice
                        </li>
                        <li>
                          <FaCheckCircle className="me-2 text-success" /> Pro
                          Live Tests
                        </li>
                        <li>
                          <FaCheckCircle className="me-2 text-success" />{" "}
                          Unlimited Test Re-Attempts
                        </li>
                  </ul>
                    <Button variant="success" className="mt-3 w-100">
                      Get Pass Pro
                    </Button>
                </div>
              </Card>
    </>
  )
}

export default PassBox
