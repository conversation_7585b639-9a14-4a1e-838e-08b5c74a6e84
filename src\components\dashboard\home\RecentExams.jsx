import React from 'react';
import { Container, Row, Col, Button } from 'react-bootstrap';
import { MdOutlineRailwayAlert } from 'react-icons/md';

const RecentExams = () => {
  const exams = [
    { name: 'RRB ALP' },
    { name: 'RRB Technician Grade 1' },
    { name: 'RRB Technician' },
    { name: 'RRB Group D' },
    { name: 'RRB NTPC' },
    { name: 'RPF SI' },
    { name: 'RPF Constable' },
  ];

  return (
    <Container className="mt-5">
      <Row className="justify-content-center">
        <Col md={10}>
          <h4 className="mb-3"><span className='text-success'>All Railway Exams 2025</span> Exams Covered</h4>
          <div className="d-flex align-items-center flex-wrap">
            {exams.map((exam, index) => (
              <Button
              variant=''
                key={index} 
                className="border p-2 m-2 d-flex gap-2 align-items-center" 
                style={{ minWidth: '120px' }}
              >
                <MdOutlineRailwayAlert className="text-danger" size={24} />
                <p className="m-0 mt-2">{exam.name}</p>
              </Button>
            ))}
          </div>
        </Col>
      </Row>
    </Container>
  );
};

export default RecentExams;