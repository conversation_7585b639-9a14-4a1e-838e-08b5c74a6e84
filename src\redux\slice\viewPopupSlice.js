import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Base API URL for popups
const API_URL = `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_POPUP_ENDPOINT}`;

// Initial state
const initialState = {
  isLoading: false,
  error: null,
  popups: [],
  currentPopup: null,
  shownPopups: [], // Track which popups have been shown to avoid duplicates
};

// Function to get Authorization token from state
const getAuthToken = (getState) => {
  const state = getState();
  try {
    return state.student?.student?.["JWT_Token"]?.access || null;
  } catch (error) {
    return null;
  }
};

// Fetch all active popups for the current user
export const fetchActivePopups = createAsyncThunk(
  "viewPopup/fetchActive",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      // Add authorization header if token exists
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await axios.get(API_URL, {
        headers,
        withCredentials: true,
      });

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error fetching popups");
    }
  }
);

// Filter and select popup based on current page and criteria
export const selectPopupForPage = createAsyncThunk(
  "viewPopup/selectForPage",
  async ({ currentPath }, { getState, dispatch, rejectWithValue }) => {
    try {
      // First fetch active popups
      const result = await dispatch(fetchActivePopups()).unwrap();
      const popups = Array.isArray(result) ? result : result.results || [];
      
      const state = getState();
      const shownPopups = state.viewPopup.shownPopups;

      // Filter popups based on criteria
      const validPopups = popups.filter(popup => {
        // Skip if already shown in this session
        if (shownPopups.includes(popup.id)) {
          return false;
        }

        // Skip if missing required fields
        if (!popup.display_duration || popup.delay_ms === null || popup.delay_ms === undefined) {
          return false;
        }

        // Skip if page_target is null and we want to be strict about targeting
        // For now, we'll allow null page_target to mean "show on all pages"
        if (popup.page_target && popup.page_target !== currentPath) {
          return false;
        }

        // Skip if page_target is empty string (different from null)
        if (popup.page_target === "") {
          return false;
        }

        return true;
      });

      if (validPopups.length === 0) {
        return null;
      }

      // Select random popup from valid ones
      const randomIndex = Math.floor(Math.random() * validPopups.length);
      const selectedPopup = validPopups[randomIndex];

      return selectedPopup;
    } catch (error) {
      return rejectWithValue(error.message || "Error selecting popup");
    }
  }
);

const viewPopupSlice = createSlice({
  name: "viewPopup",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentPopup: (state) => {
      state.currentPopup = null;
    },
    markPopupAsShown: (state, action) => {
      const popupId = action.payload;
      if (!state.shownPopups.includes(popupId)) {
        state.shownPopups.push(popupId);
      }
    },
    resetShownPopups: (state) => {
      state.shownPopups = [];
    },
    setCurrentPopup: (state, action) => {
      state.currentPopup = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Active Popups
      .addCase(fetchActivePopups.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchActivePopups.fulfilled, (state, action) => {
        state.isLoading = false;
        state.popups = Array.isArray(action.payload) ? action.payload : action.payload.results || [];
      })
      .addCase(fetchActivePopups.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Select Popup for Page
      .addCase(selectPopupForPage.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(selectPopupForPage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentPopup = action.payload;
      })
      .addCase(selectPopupForPage.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

// Actions
export const { 
  clearError, 
  clearCurrentPopup, 
  markPopupAsShown, 
  resetShownPopups,
  setCurrentPopup 
} = viewPopupSlice.actions;

// Reducer
export default viewPopupSlice.reducer;
