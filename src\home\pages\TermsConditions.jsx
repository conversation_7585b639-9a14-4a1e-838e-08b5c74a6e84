import React, { useEffect, useState } from 'react';
import { FaBars, FaTimes } from "react-icons/fa";
import NavBar from "../../commonCompoenents/NavBar";
import Footer from "../components/Footer";
import { Container, Row, Col, <PERSON>ton, Card } from 'react-bootstrap';

const PrivacyPolicy = () => {
  const [activeSection, setActiveSection] = useState('');
  const [isNavVisible, setIsNavVisible] = useState(true); // State for navigation visibility

  const toggleNavVisibility = () => {
    setIsNavVisible(!isNavVisible);
  };

  useEffect(() => {
    const handleScroll = () => {
      const sections = [
        'section1', 'section2', 'section3', 'section4', 'section5',
        'section6', 'section7', 'section8', 'section9', 'section10',
        'section11', 'section12', 'section13', 'section14', 'section15',
        'section16', 'section17', 'section18', 'section19', 'section20',
        'section21'
      ];
      let currentSection = '';
      sections.forEach((section) => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= 100 && rect.bottom >= 100) {
            currentSection = section;
          }
        }
      });
      setActiveSection(currentSection);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);



  return (
    <>
      <NavBar />
      <section style={{ backgroundColor: '#ecd9c6', }}>
        <Container className="mt-5">
         
          {/* Privacy Policy Header */}
          <Row className="text-center mb-5">
            <Col>
              <h1 className="mt-5 display-4 text-uppercase font-weight-bold">Privacy Policy</h1>
              <p className="lead">Your privacy is important to us. Please read our policy carefully.</p>
              <p><strong>Last Updated:</strong> 2025 </p>
            </Col>
          </Row>

          {/* Privacy Policy Content - Multi-Column Layout */}
          <Row>
            {/* Sidebar - Floating Navigation Links */}
            <div style={{
              position: 'fixed',
              top: '15%',
              right: '2%',
              width: isNavVisible ? '250px' : '50px',
              zIndex: 1000,
              backgroundColor: '#fff',
              padding: isNavVisible ? '10px' : '5px',
              borderRadius: '8px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
              transition: 'width 0.3s, padding 0.3s'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <h4 className="font-weight-bold" style={{ display: isNavVisible ? 'block' : 'none' }}>Navigation</h4>
                <button
                  onClick={toggleNavVisibility}
                  style={{
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '1.2rem'
                  }}
                >
                  {isNavVisible ? <FaTimes /> : <FaBars />}
                </button>
              </div>
              {isNavVisible && (
                <ul style={{ listStyleType: 'none', padding: 0, fontSize: '0.8rem' }}>
                  <li>
                    <a
                      href="#section1"
                      style={{
                        color: activeSection === 'section1' ? 'green' : 'black',
                        fontWeight: activeSection === 'section1' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      1. Introduction
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section2"
                      style={{
                        color: activeSection === 'section2' ? 'green' : 'black',
                        fontWeight: activeSection === 'section2' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      2. Acceptance of Terms
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section3"
                      style={{
                        color: activeSection === 'section3' ? 'green' : 'black',
                        fontWeight: activeSection === 'section3' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      3. Definitions
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section4"
                      style={{
                        color: activeSection === 'section4' ? 'green' : 'black',
                        fontWeight: activeSection === 'section4' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      4. Account Registration & Responsibilities
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section5"
                      style={{
                        color: activeSection === 'section5' ? 'green' : 'black',
                        fontWeight: activeSection === 'section5' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      5. Service Description & Scope
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section6"
                      style={{
                        color: activeSection === 'section6' ? 'green' : 'black',
                        fontWeight: activeSection === 'section6' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      6. User Obligations
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section7"
                      style={{
                        color: activeSection === 'section7' ? 'green' : 'black',
                        fontWeight: activeSection === 'section7' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      7. Subscription and Fees
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section8"
                      style={{
                        color: activeSection === 'section8' ? 'green' : 'black',
                        fontWeight: activeSection === 'section8' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      8. Free Trials and Promotions
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section9"
                      style={{
                        color: activeSection === 'section9' ? 'green' : 'black',
                        fontWeight: activeSection === 'section9' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      9. Cancellation and Termination
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section10"
                      style={{
                        color: activeSection === 'section10' ? 'green' : 'black',
                        fontWeight: activeSection === 'section10' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      10. Modifications to the Service and Terms
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section11"
                      style={{
                        color: activeSection === 'section11' ? 'green' : 'black',
                        fontWeight: activeSection === 'section11' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      11. Intellectual Property Rights
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section12"
                      style={{
                        color: activeSection === 'section12' ? 'green' : 'black',
                        fontWeight: activeSection === 'section12' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      12. User-Generated Content
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section13"
                      style={{
                        color: activeSection === 'section13' ? 'green' : 'black',
                        fontWeight: activeSection === 'section13' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      13. Privacy Policy Integration
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section14"
                      style={{
                        color: activeSection === 'section14' ? 'green' : 'black',
                        fontWeight: activeSection === 'section14' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      14. Data Security and Management
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section15"
                      style={{
                        color: activeSection === 'section15' ? 'green' : 'black',
                        fontWeight: activeSection === 'section15' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      15. Third-Party Services & Integration
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section16"
                      style={{
                        color: activeSection === 'section16' ? 'green' : 'black',
                        fontWeight: activeSection === 'section16' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      16. Limitation of Liability
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section17"
                      style={{
                        color: activeSection === 'section17' ? 'green' : 'black',
                        fontWeight: activeSection === 'section17' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      17. Governing Law & Jurisdiction
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section18"
                      style={{
                        color: activeSection === 'section18' ? 'green' : 'black',
                        fontWeight: activeSection === 'section18' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      18. Dispute Resolution
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section19"
                      style={{
                        color: activeSection === 'section19' ? 'green' : 'black',
                        fontWeight: activeSection === 'section19' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      19. Amendment of Terms
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section20"
                      style={{
                        color: activeSection === 'section20' ? 'green' : 'black',
                        fontWeight: activeSection === 'section20' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      20. Contact Information
                    </a>
                  </li>
                  <li>
                    <a
                      href="#section21"
                      style={{
                        color: activeSection === 'section21' ? 'green' : 'black',
                        fontWeight: activeSection === 'section21' ? 'bold' : 'normal',
                        textDecoration: 'none'
                      }}
                    >
                      21. Miscellaneous Provisions
                    </a>
                  </li>
                </ul>
              )}
            </div>

            {/* Main Content Section */}
            <Col lg={9} md={8}>
              {/* Section 1: Introduction */}
              <Card id="section1" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">1. Introduction</h5>
                  <p><strong>Purpose:</strong> These terms and conditions (the "Terms") are intended to legally bind you when you access or use our services, whether directly or indirectly.</p>
                  <p><strong>Scope:</strong> These Terms apply to the use of the Librainian web app, Android app, and iOS app, along with any related software.</p>
                  <p><strong>Effective Date:</strong> These Terms are effective as of July 7, 2024.</p>
                </Card.Body>
              </Card>

              {/* Section 2: Acceptance of Terms */}
              <Card id="section2" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">2. Acceptance of Terms</h5>
                  <p><strong>User Consent:</strong> By clicking "Accept" or using the service, you consent to abide by these Terms of Use.</p>
                  <p><strong>Eligibility:</strong> Users must be 13 years of age or older, up to 85 years of age, to use the services.</p>
                  <p><strong>Amendments:</strong> We may update these Terms without your consent. If changes are made, you will be notified. Continuing to use our services implies acceptance of the changes.</p>
                </Card.Body>
              </Card>

              {/* Section 3: Definitions */}
              <Card id="section3" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">3. Definitions</h5>
                  <p><strong>Service:</strong> Librainian provides a digital platform for private libraries to manage seat allocation, fee collection, analytics, and streamline operations. It also allows libraries to use ad spaces on their walls through our network.</p>
                  <p><strong>User:</strong> Refers to the individual using the Librainian platform, which may include library staff, subscribers, trial users, and ad investors.</p>
                  <p><strong>Content:</strong> Content includes user-generated content and application content.</p>
                  <ul>
                    <li><strong>User-Generated Content:</strong> Any materials, including profiles and feedback submitted by users, for which they are solely responsible.</li>
                    <li><strong>Application Content:</strong> Content owned by Librainian, including text, graphics, logos, and intellectual property protected under copyright and trademark laws.</li>
                  </ul>
                </Card.Body>
              </Card>

              {/* Section 4: Account Registration & Responsibilities */}
              <Card id="section4" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">4. Account Registration & Responsibilities</h5>
                  <p><strong>Account Creation:</strong> To create an account, users must provide their name, phone number, address, and a secure password.</p>
                  <p><strong>Accurate Information:</strong> You are obligated to provide accurate and up-to-date information for proper communication.</p>
                  <p><strong>Security:</strong> You are responsible for maintaining the confidentiality of your password and account.</p>
                  <p><strong>Unauthorized Use:</strong> In case of unauthorized access, your account will be blocked to ensure security.</p>
                </Card.Body>
              </Card>

              {/* Section 5: Service Description & Scope */}
              <Card id="section5" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">5. Service Description & Scope</h5>
                  <h5 className="font-weight-bold">Core Features:</h5>
                  <ul>
                    <li>Seat Allocation: Simplifies seat management for library users.</li>
                    <li>Fee Collection: Tracks and manages user payments.</li>
                    <li>Analytics and Reporting: Provides usage insights to library administrators.</li>
                    <li>Sublibrarian Feature: Allows delegation of tasks to sub-administrators in advanced plans.</li>
                    <li>AdBrain: Enables libraries to sell ad spaces on their walls, generating revenue.</li>
                    <li>24/7 Accessibility: Access the platform at any time.</li>
                    <li>Credit Point System: Flexible usage system based on credits.</li>
                  </ul>
                  <h5 className="font-weight-bold">Service Limitations:</h5>
                  <ul>
                    <li>Subscription-based access based on your chosen plan.</li>
                    <li>Ad placement approval is required, and we reserve the right to reject ads violating policies.</li>
                    <li>Some services rely on third-party integrations and may be subject to their disruptions.</li>
                    <li>Service availability depends on your internet connectivity.</li>
                    <li>We may modify or discontinue services without prior notice.</li>
                  </ul>
                  <h5 className="font-weight-bold">Support Services:</h5>
                  <p>Customer support is available 24/7 via email, chat (7 am to 10 pm), and calls (10 am to 6 pm on weekdays).</p>
                </Card.Body>
              </Card>

              {/* Section 6: User Obligations */}
              <Card id="section6" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">6. User Obligations</h5>
                  <p><strong>Legal Use:</strong> Comply with local, state, and national laws.</p>
                  <p><strong>Prohibited Behavior:</strong> No hacking, fraud, or abuse.</p>
                  <p><strong>User Responsibility:</strong> You are liable for the content you share.</p>
                </Card.Body>
              </Card>

              {/* Section 7: Subscription and Fees */}
              <Card id="section7" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">7. Subscription and Fees</h5>
                  <p><strong>Subscription Plans & Coupons:</strong> Various plans with credit points and discounts available via coupons.</p>
                  <p><strong>Billing:</strong> Processed through Razorpay. Check the app for cycles and details.</p>
                  <p><strong>Late Payments:</strong> May result in service suspension.</p>
                  <p><strong>Refund Policy:</strong> No refunds. A 10-day trial is offered for evaluation.</p>
                </Card.Body>
              </Card>

              {/* Section 8: Free Trials and Promotions */}
              <Card id="section8" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">8. Free Trials and Promotions</h5>
                  <p><strong>Eligibility:</strong> Available to all new users.</p>
                  <p><strong>Duration:</strong> 10 days. Service will auto-renew unless canceled.</p>
                </Card.Body>
              </Card>

              {/* Section 9: Cancellation and Termination */}
              <Card id="section9" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">9. Cancellation and Termination</h5>
                  <p><strong>User-Initiated Cancellation:</strong> Subscription ends upon non-renewal.</p>
                  <p><strong>Consequences:</strong> Access will be restricted.</p>
                  <p><strong>Service-Initiated Termination:</strong> May occur due to policy violations.</p>
                </Card.Body>
              </Card>

              {/* Section 10: Modifications to the Service and Terms */}
              <Card id="section10" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">10. Modifications to the Service and Terms</h5>
                  <p><strong>Right to Modify:</strong> We may change services or terms.</p>
                  <p><strong>Notifications:</strong> Updates posted on the website.</p>
                  <p><strong>Consent:</strong> Continued use = acceptance of changes.</p>
                </Card.Body>
              </Card>

              {/* Section 11: Intellectual Property Rights */}
              <Card id="section11" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">11. Intellectual Property Rights</h5>
                  <p><strong>Ownership:</strong> All IP belongs to Librainian (Pinak Venture).</p>
                  <p><strong>User License:</strong> Non-exclusive, non-transferable right to use.</p>
                  <p><strong>Restrictions:</strong> No copying, modifying, or reverse engineering.</p>
                </Card.Body>
              </Card>

              {/* Section 12: User-Generated Content */}
              <Card id="section12" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">12. User-Generated Content</h5>
                  <p><strong>Content Ownership:</strong> You retain rights to your own content.</p>
                  <p><strong>License to Use:</strong> Grants us the right to use content for display/promotion.</p>
                  <p><strong>Content Guidelines:</strong> No harmful, unethical, or abusive material allowed.</p>
                  <p><strong>Content Removal:</strong> We reserve the right to remove violations and restrict accounts.</p>
                </Card.Body>
              </Card>

              {/* Section 13: Privacy Policy Integration */}
              <Card id="section13" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">13. Privacy Policy Integration</h5>
                  <p><strong>View Policy:</strong> [Link to Privacy Policy]</p>
                  <p><strong>Data Collection:</strong> As per our Privacy Policy.</p>
                  <p><strong>Consent:</strong> Use of platform implies agreement with our data practices.</p>
                </Card.Body>
              </Card>

              {/* Section 14: Data Security and Management */}
              <Card id="section14" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">14. Data Security and Management</h5>
                  <p><strong>Security Measures:</strong> HTTPS, injection prevention, session security.</p>
                  <p><strong>Backups:</strong> Daily backups to prevent data loss.</p>
                </Card.Body>
              </Card>

              {/* Section 15: Third-Party Services & Integration */}
              <Card id="section15" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">15. Third-Party Services & Integration</h5>
                  <p><strong>Integrations:</strong> Google Analytics, Razorpay, Gmail.</p>
                  <p><strong>Disclaimer:</strong> Not liable for failures caused by third parties.</p>
                </Card.Body>
              </Card>

              {/* Section 16: Limitation of Liability */}
              <Card id="section16" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">16. Limitation of Liability</h5>
                  <p><strong>Direct Damages:</strong> Limited to the last paid subscription amount.</p>
                  <p><strong>Indirect Damages:</strong> Not liable for any special or consequential damages.</p>
                  <p><strong>Indemnification:</strong> Users agree to hold Librainian harmless from legal claims.</p>
                </Card.Body>
              </Card>

              {/* Section 17: Governing Law & Jurisdiction */}
              <Card id="section17" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">17. Governing Law & Jurisdiction</h5>
                  <p><strong>Applicable Law:</strong> Governed by the laws of [Insert Country/State].</p>
                  <p><strong>Jurisdiction:</strong> All disputes will be handled in [Insert Jurisdiction].</p>
                </Card.Body>
              </Card>

              {/* Section 18: Dispute Resolution */}
              <Card id="section18" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">18. Dispute Resolution</h5>
                  <p><strong>Arbitration:</strong> All disputes resolved via arbitration, not in court.</p>
                </Card.Body>
              </Card>

              {/* Section 19: Amendment of Terms */}
              <Card id="section19" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">19. Amendment of Terms</h5>
                  <p><strong>Right to Amend:</strong> We can update Terms anytime.</p>
                  <p><strong>Notification:</strong> Changes posted on the website.</p>
                </Card.Body>
              </Card>

              {/* Section 20: Contact Information */}
              <Card id="section20" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">20. Contact Information</h5>
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Phone:</strong> 6207628282</p>
                  <p><strong>Business Hours:</strong></p>
                  <ul>
                    <li><strong>Email:</strong> 24/7</li>
                    <li><strong>Calls:</strong> Weekdays, 10 AM – 6 PM</li>
                  </ul>
                </Card.Body>
              </Card>

              {/* Section 21: Miscellaneous Provisions */}
              <Card id="section21" className="border-0 mb-5">
                <Card.Body>
                  <h5 className="text-uppercase font-weight-bold">21. Miscellaneous Provisions</h5>
                  <p><strong>Severability:</strong> Invalid provisions don’t affect the rest.</p>
                  <p><strong>Waiver:</strong> Not enforcing rights doesn’t waive them.</p>
                  <p><strong>Entire Agreement:</strong> These Terms constitute the full agreement.</p>
                  <p><strong>Assignment:</strong> User rights are non-transferable.</p>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </section>
      <Footer />
    </>
  );
};

export default PrivacyPolicy;
