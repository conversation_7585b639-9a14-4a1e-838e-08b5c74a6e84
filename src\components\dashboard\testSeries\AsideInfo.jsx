import React, { useState, useEffect } from "react";
import { Card } from "react-bootstrap";
import { FaArrowRight } from "react-icons/fa";
import Skeleton from "react-loading-skeleton"; // Import the Skeleton component
import { Link } from "react-router-dom";

const AsideInfo = () => {
  // Set up a loading state
  const [isLoading, setIsLoading] = useState(true);

  // Simulate a data fetch with useEffect (e.g., API call)
  useEffect(() => {
    // Set a timeout to simulate loading delay
    const timer = setTimeout(() => {
      setIsLoading(false); // Stop loading after 3 seconds
    }, 3000);

    return () => clearTimeout(timer); // Clean up the timeout
  }, []);

  return (
    <>
      {/* More Test Series for You Section */}
      {isLoading ? (
        <Skeleton
          height={40}
          width="100%"
          baseColor="#e6ffe6"
          highlightColor="#c4f7c4"
        />
      ) : (
        <div className="mb-3">
          <h5>More Test Series for You</h5>
        </div>
      )}

      {isLoading ? (
        <Skeleton
          height={60}
          width="100%"
          baseColor="#e6ffe6"
          highlightColor="#c4f7c4"
        />
      ) : (
        <Link to="/dashboard" style={{ textDecoration: "none", color: "inherit" }}>
          <div
            className="bg-white p-3 mb-3 shadow-sm"
            style={{
              transition: "transform 0.3s ease-in-out",
              cursor: "pointer",
            }}
            onMouseEnter={(e) =>
              (e.currentTarget.style.transform = "scale(1.05)")
            }
            onMouseLeave={(e) => (e.currentTarget.style.transform = "scale(1)")}
          >
            <div className="d-flex justify-content-between align-items-center">
              <span>Explore more test series</span>
              <FaArrowRight />
            </div>
          </div>
        </Link>
      )}

      {isLoading ? (
        <Skeleton
          height={60}
          width="100%"
          baseColor="#e6ffe6"
          highlightColor="#c4f7c4"
        />
      ) : (
        <Link to="/dashboard" style={{ textDecoration: "none", color: "inherit" }}>
          <div
            className="bg-white p-3 mb-3 shadow-sm"
            style={{
              transition: "transform 0.3s ease-in-out",
              cursor: "pointer",
            }}
            onMouseEnter={(e) =>
              (e.currentTarget.style.transform = "scale(1.05)")
            }
            onMouseLeave={(e) => (e.currentTarget.style.transform = "scale(1)")}
          >
            <div className="d-flex justify-content-between align-items-center">
              <span>Explore more test series</span>
              <FaArrowRight />
            </div>
          </div>
        </Link>
      )}

      {isLoading ? (
        <Skeleton
          height={60}
          width="100%"
          baseColor="#e6ffe6"
          highlightColor="#c4f7c4"
        />
      ) : (
        <Link to="/dashboard" style={{ textDecoration: "none", color: "inherit" }}>
          <div
            className="bg-white p-3 mb-3 shadow-sm"
            style={{
              transition: "transform 0.3s ease-in-out",
              cursor: "pointer",
            }}
            onMouseEnter={(e) =>
              (e.currentTarget.style.transform = "scale(1.05)")
            }
            onMouseLeave={(e) => (e.currentTarget.style.transform = "scale(1)")}
          >
            <div className="d-flex justify-content-between align-items-center">
              <span>Explore more test series</span>
              <FaArrowRight />
            </div>
          </div>
        </Link>
      )}

      {isLoading ? (
        <Skeleton
          height={60}
          width="100%"
          baseColor="#e6ffe6"
          highlightColor="#c4f7c4"
        />
      ) : (
        <Link to="/dashboard" style={{ textDecoration: "none", color: "inherit" }}>
          <div
            className="bg-white p-3 mb-3 shadow-sm"
            style={{
              transition: "transform 0.3s ease-in-out",
              cursor: "pointer",
            }}
            onMouseEnter={(e) =>
              (e.currentTarget.style.transform = "scale(1.05)")
            }
            onMouseLeave={(e) => (e.currentTarget.style.transform = "scale(1)")}
          >
            <div className="d-flex justify-content-between align-items-center">
              <span>Explore more test series</span>
              <FaArrowRight />
            </div>
          </div>
        </Link>
      )}

      {/* Earn Money Card with Linear Gradient */}
      <Link to="/dashboard/reward" style={{ textDecoration: "none" }}>
        <Card
          className="mt-4 shadow-sm"
          style={{
            background: "linear-gradient(135deg, #ff9d2f, #ff6126)", // Gradient from orange to deep orange
            color: "#fff", // White text for contrast
            transition: "transform 0.3s ease-in-out",
          }}
          onMouseEnter={(e) =>
            (e.currentTarget.style.transform = "scale(1.05)")
          }
          onMouseLeave={(e) => (e.currentTarget.style.transform = "scale(1)")}
        >
          {/* <Card.Body>
            {isLoading ? (
              <>
                <Skeleton
                  height={20}
                  width="60%"
                  baseColor="#e6ffe6"
                  highlightColor="#c4f7c4"
                />
                <Skeleton
                  height={20}
                  width="80%"
                  baseColor="#e6ffe6"
                  highlightColor="#c4f7c4"
                />
              </>
            ) : (
              <>
                <h6 className="text-center">Earn Money</h6>
                <p className="text-center">
                  Refer and earn by sharing test series
                </p>
              </>
            )}
          </Card.Body> */}
        </Card>
      </Link>
    </>
  );
};

export default AsideInfo;
