import React, { useEffect } from 'react';
import { useTheme } from '../context/ThemeContext';

const ThemeWrapper = ({ children }) => {
  const { isDarkMode, theme } = useTheme();

  useEffect(() => {
    // Apply theme styles to body and html
    const root = document.documentElement;
    const body = document.body;
    
    // Set CSS custom properties on root
    Object.entries(theme.colors).forEach(([key, value]) => {
      const cssVarName = `--theme-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
      root.style.setProperty(cssVarName, value);
    });

    // Ensure body has proper background
    body.style.backgroundColor = theme.colors.background;
    body.style.color = theme.colors.text;
    
    // Add theme class to body for global styling
    if (isDarkMode) {
      body.classList.add('dark-theme');
      body.classList.remove('light-theme');
    } else {
      body.classList.add('light-theme');
      body.classList.remove('dark-theme');
    }
  }, [isDarkMode, theme]);

  return (
    <div 
      className={`theme-wrapper ${isDarkMode ? 'dark-theme' : 'light-theme'}`}
      style={{
        backgroundColor: theme.colors.background,
        color: theme.colors.text,
        minHeight: '100vh',
        transition: 'background-color 0.3s ease, color 0.3s ease'
      }}
    >
      {children}
    </div>
  );
};

export default ThemeWrapper;
