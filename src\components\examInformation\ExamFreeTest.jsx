import React from 'react'
import { <PERSON><PERSON>, <PERSON>, Col } from 'react-bootstrap'
import { FaClipboardList, FaLanguage, FaPercent, FaRegClock } from 'react-icons/fa'

const ExamFreeTest = ({data}) => {
  return (
    <>
    <h4 className='mt-5 mb-4'>{data?.name} <span className='text-success'>Free Tests</span></h4>
        {
            data?.test_series.map((test, index) => (
            <Col key={index} xs={12} md={12} lg={12} className="mb-3">
              <Card className="shadow-sm border-0 w-100">
                <Card.Body>
                  <div className="d-md-flex align-items-center gap-2">
                    <Card.Title>{test.name}<span style={{fontSize: "0.75rem"}} className="fw-normal text-white ms-3 py-1 px-3 badge bg-success small d-md-none d-inline">
                      FREE
                    </span></Card.Title>
                    <span className="text-white badge bg-success small d-md-inline d-none">
                      FREE
                    </span>
                  </div>

                  <Card.Text>
                    <div className="d-flex flex-md-row flex-column justify-content-between align-items-center">
                      {/* <div> */}
                        <div
                          className="d-flex align-items-center text-muted"
                          style={{ fontSize: "0.75rem" }}
                        >
                          <FaClipboardList className="me-2" />
                          <span>{test.totalQuestions} Questions</span>
                          <span className="mx-2">|</span>
                          <FaPercent className="me-2" />
                          <span>{test.totalMarks} Marks</span>
                          <span className="mx-2">|</span>
                          <FaRegClock className="me-2" />
                          <span>{test.durationMinutes} minutes</span>
                        </div>
                      {/* </div> */}
                      <Button variant="success" className='d-md-inline d-none'>Start Now</Button>
                      <Button variant="success" className='d-md-none w-100 mt-3 py-1'>Start Now</Button>
                    </div>
                  </Card.Text>
                </Card.Body>
                <Card.Footer className='small bg-success bg-opacity-10'>
                <FaLanguage size={20}/> {test.language.join(", ")}
                </Card.Footer>
              </Card>
            </Col>
          ))
        }
    </>
  )
}

export default ExamFreeTest
