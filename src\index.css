/* Import Google Font */
@import url('https://fonts.googleapis.com/css2?family=Cabin+Sketch:wght@400;700&display=swap');

/* Apply Cabin Sketch to all elements */
/* * {
  font-family: "Cabin Sketch", sans-serif;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
} */

.cabin-sketch-regular {
  font-family: "Cabin Sketch", sans-serif;
  font-weight: 400;
  font-style: normal;
}

.cabin-sketch-bold {
  font-family: "Cabin Sketch", sans-serif;
  font-weight: 700;
  font-style: normal;
}

/* Apply bold font to headings */
/* h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  font-family: "Cabin Sketch", sans-serif;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
} */


/* Apply normal font weight to other elements */
/* body, p, span, div, a, button, input, textarea, select, option, label, ul, ol, li, table, th, td {
  font-weight: 400;
} */

.col-responsive {
    height: auto !important;
  }

  
  /* For 'md' and larger screens, set the height to 92vh */
  @media (min-width: 768px) {
    .col-responsive {
      height: 92vh !important;
    }
  }

  @media (max-width: 768px) {
    .custom-offcanvas {
      width: 75% !important; /* Sidebar takes 75% of screen */
    }
    
    .offcanvas-backdrop.show {
      opacity: 0.5 !important; /* Slightly visible background */
    }
  }


/* Change background color of active accordion */
.custom-accordion .accordion-button:not(.collapsed) {
  background-color: #28a745 !important; /* Bootstrap success green */
  color: white !important;
  box-shadow: none !important; /* Removes the focus outline */
}

/* Change background color of the accordion body */
.custom-accordion .accordion-body {
  background-color: #d4edda !important; /* Lighter green background */
  color: #155724 !important; /* Dark green text */
}

/* Remove the blue outline (focus ring) when clicked */
.custom-accordion .accordion-button:focus {
  box-shadow: none !important;
  outline: none !important;
}


.footer-link{
  color: white;
  transition: all 0.4s ease-in;
}

.footer-link:hover{
  color: "#198754" !important;
}






/* Custom Scrollbar for the entire app */
* {
  scrollbar-width: thin;
  scrollbar-color: #ccffcc #f8f9fa; /* Light Green thumb, Light Gray track (Firefox) */
}

/* WebKit-based browsers (Chrome, Edge, Safari) */
*::-webkit-scrollbar {
  width: 8px;
}

*::-webkit-scrollbar-track {
  background: #f8f9fa; /* Light Gray track */
  border-radius: 10px;
}

*::-webkit-scrollbar-thumb {
  background: #ccffcc; /* Light Green thumb */
  border-radius: 10px;
}

*::-webkit-scrollbar-thumb:hover {
  background: #99cc99; /* Slightly darker green on hover */
}


.progress {
  padding: 0 !important;
}

.bottom-nav {
  display: flex;
}

@media (min-width: 768px) {
  .bottom-nav {
    display: none !important;
  }
}