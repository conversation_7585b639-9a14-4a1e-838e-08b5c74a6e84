import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form, Card, Row, Col, Container } from "react-bootstrap";
import {
  FaWallet,
  FaArrowDown,
  FaCheckCircle,
  FaCoins,
  FaFrownOpen,
} from "react-icons/fa";
import Confetti from "react-confetti";
import ScratchCards from "../components/ScratchCards";
import { useDispatch } from "react-redux";
import toast from "react-hot-toast";
import { getAllScratchCards, scratchScratchCard, getWalletData, raiseWithdrawalRequest } from "../../redux/slice/rewardSlice";
import ReferralData from "../../commonCompoenents/ReferralData";

const Rewards = () => {
  const dispatch = useDispatch();
  const [scratchCards, setScratchCards] = useState([]);
  const [selectedCard, setSelectedCard] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [scratchValue, setScratchValue] = useState(0);
  const [widthdrawlAmout, setWidthdrawlAmout] = useState();
  const [showOverlay, setShowOverlay] = useState(false);
  const [isPrizeWon, setIsPrizeWon] = useState(false);
  const [prizeValue, setPrizeValue] = useState(0);
  const [walletData, setWalletData] = useState({
    totalCash: "...",
    withdrawals: "...",
    settled: "...",
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    // Fetch scratch cards
    dispatch(getAllScratchCards())
      .unwrap()
      .then((data) => setScratchCards(data))
      .catch((error) => console.error("Failed to fetch scratch cards:", error));

    // Fetch wallet data
    dispatch(getWalletData())
      .unwrap()
      .then((data) => {
        setWalletData({
          totalCash: data.total_wallet_balance,
          withdrawals: data.pending_withdrawals_amount,
          settled: data.settled_withdrawals_amount,
        });
      })
      .catch((error) => console.error("Failed to fetch wallet data:", error));
  }, [dispatch]);

  const handleCompleteScratch = (value, cardId) => {
    setSelectedCard(cardId); // Store the card ID for dispatching later
    setShowOverlay(true);
    setPrizeValue(value);
    setIsPrizeWon(value > 0);
  };

  const handleClaimPrize = () => {
    if (selectedCard) {
      dispatch(scratchScratchCard(selectedCard))
        .unwrap()
        .then(() => {
          setScratchCards((prevCards) =>
            prevCards.map((card) =>
              card.id === selectedCard ? { ...card, is_scratched: true } : card
            )
          );
        })
        .catch((error) => console.error("Failed to scratch card:", error));
    }
    setShowOverlay(false);
  };

  const handleWithdraw = () => {
    const newErrors = {};
    const upiId = document.getElementById("upiId").value;

    if (!upiId) {
      newErrors.upiId = "Please enter a valid UPI ID.";
    }

    if (!widthdrawlAmout || isNaN(widthdrawlAmout) || widthdrawlAmout <= 0) {
      newErrors.amount = "Please enter a valid withdrawal amount.";
    } else if (parseFloat(widthdrawlAmout) > parseFloat(walletData.totalCash)) {
      newErrors.amount = "Withdrawal amount cannot exceed wallet balance.";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    const payload = {
      amount: parseFloat(widthdrawlAmout),
      upi_id: upiId,
    };

    dispatch(raiseWithdrawalRequest(payload))
      .unwrap()
      .then(() => {
        toast.success("Withdrawal request raised successfully!");
        setErrors({});
        setWidthdrawlAmout(""); // Reset the form
        document.getElementById("upiId").value = ""; // Clear UPI ID field
      })
      .catch((error) => {
        if (error?.error) {
          toast.error(error.error);
          setErrors({ apiError: error.error });
        } else {
          toast.error("Failed to raise withdrawal request. Please try again later.");
          setErrors({ apiError: "Failed to raise withdrawal request. Please try again later." });
        }
      });
  };

  return (
    <>
      <Container className="mt-4">
        <Row>
          <Col md={4}>
            <ReferralData profile={false} />
          </Col>

          <Col md={6}>
            <Card className="p-4 shadow">
              <Card.Body>
                <h3 className="text-center mb-4">Nayan</h3>
                <Row className="mb-3 text-center">
                  <Col>
                    <FaWallet size={24} className="text-success" />
                    <p>Wallet</p>
                    <p>₹{walletData.totalCash}</p>
                  </Col>
                  <Col>
                    <FaArrowDown size={24} className="text-primary" />
                    <p> Pending </p>
                    <p>₹{walletData.withdrawals}</p>
                  </Col>
                  <Col>
                    <FaCheckCircle size={24} className="text-warning" />
                    <p>Settled</p>
                    <p>₹{walletData.settled}</p>
                  </Col>
                </Row>

                <Form>
                  <Form.Group controlId="upiId" className="mb-3">
                    <Form.Label>Enter Your UPI ID</Form.Label>
                    <Form.Control
                      type="text"
                      placeholder="Enter UPI ID"
                      isInvalid={!!errors.upiId}
                    />
                    <Form.Control.Feedback type="invalid">
                      {errors.upiId}
                    </Form.Control.Feedback>
                  </Form.Group>

                  <Form.Group controlId="withdrawAmount" className="mb-3">
                    <Form.Label>Enter Amount to Withdraw</Form.Label>
                    <Form.Control
                      type="number"
                      placeholder="Enter amount"
                      value={widthdrawlAmout}
                      onChange={(e) => setWidthdrawlAmout(e.target.value)}
                      isInvalid={!!errors.amount}
                    />
                    <Form.Control.Feedback type="invalid">
                      {errors.amount}
                    </Form.Control.Feedback>
                  </Form.Group>

                  {errors.apiError && (
                    <div className="text-danger mb-3">{errors.apiError}</div>
                  )}

                  <Button variant="success" className="w-100" onClick={handleWithdraw}>
                    Withdraw
                  </Button>
                </Form>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Scratch Result Overlay */}
        {showOverlay && (
          <div
            className="position-absolute d-flex justify-content-center align-items-center"
            style={{
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.7)",
              zIndex: 9999,
            }}
          >
            <div className="text-center text-white">
              {isPrizeWon ? (
                <>
                  <Confetti
                    width={window.innerWidth}
                    height={window.innerHeight}
                  />
                  <FaCoins size={80} className="mb-3 text-warning" />
                  <h3>You've won ₹{prizeValue}!</h3>
                  <Button
                    variant="success"
                    className="mt-3"
                    onClick={handleClaimPrize}
                  >
                    Claim
                  </Button>
                </>
              ) : (
                <>
                  <FaFrownOpen size={80} className="mb-3 text-danger" />
                  <h3>Better luck next time!</h3>
                  <Button
                    variant="secondary"
                    className="mt-3"
                    onClick={handleClaimPrize}
                  >
                    Try Again
                  </Button>
                </>
              )}
            </div>
          </div>
        )}
      </Container>

      {/* Scratch Cards Display */}
      <section>
        <div className="mt-5 d-flex flex-wrap mb-3">
          {scratchCards.map((card) => (
            <div key={card.id} className="m-2" >
              {card.is_scratched ? (
                <Card className="shadow" style={{ display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center", width: "150px", height: "150px", borderRadius: "2rem" }}>
                  <div className="text-center">
                    <FaCoins size={36} className="text-warning" />
                    <h5>₹{card.amount}</h5>
                    <p>You won </p>
                  </div>
                </Card>
              ) : (
                <div style={{width: "150px", height: "150px", borderRadius: "2rem", overflow: "hidden"}}>
                <ScratchCards
                  onCompleteScratch={(value) => handleCompleteScratch(value, card.id)}
                  prizeValue={card.amount} // Use the amount from the backend
                />
                </div>
              )}
            </div>
          ))}
        </div>
      </section>
    </>
  );
};

export default Rewards;
