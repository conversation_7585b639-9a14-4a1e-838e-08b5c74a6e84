import React, { useState, useEffect } from "react";
import { Con<PERSON><PERSON>, <PERSON>, Col, Button, Modal, Dropdown } from "react-bootstrap";
import testData from "../../dummyData/testData";
import Results from "./Results"; // Import the Results component

const TestInterface = () => {
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [timer, setTimer] = useState(0);
  const [language, setLanguage] = useState("English");
  const [questionStatuses, setQuestionStatuses] = useState({
    answered: 0,
    notAttempted: 0,
    notVisited: 0
  });
  const [testComplete, setTestComplete] = useState(false); // New state to track test completion

  const currentSection = testData.sections[currentSectionIndex];
  const currentQuestion = currentSection.questions[currentQuestionIndex];

  useEffect(() => {
    setTimer(0); // Reset timer when question changes
    const interval = setInterval(() => {
      setTimer((prev) => prev + 1);
    }, 1000);
    return () => clearInterval(interval);
  }, [currentQuestionIndex]);

  const handleSelectAnswer = (qId, answer) => {
    setSelectedAnswers({ ...selectedAnswers, [qId]: answer });
  };

  const handleNext = () => {
    if (currentQuestionIndex < currentSection.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleSelectQuestion = (qIndex) => {
    setCurrentQuestionIndex(qIndex);
  };

  const handleSubmitSection = () => {
    let answeredCount = 0;
    let notAttemptedCount = 0;
    let notVisitedCount = 0;

    currentSection.questions.forEach((question, index) => {
      if (selectedAnswers[question.id]) {
        answeredCount++;
      } else if (selectedAnswers[question.id] === undefined && index < currentQuestionIndex) {
        notAttemptedCount++;
      } else {
        notVisitedCount++;
      }
    });

    setQuestionStatuses({
      answered: answeredCount,
      notAttempted: notAttemptedCount,
      notVisited: notVisitedCount
    });
    setShowModal(true);

    if (currentSectionIndex === testData.sections.length - 1) {
      // Test is complete, show Results after the modal
      setTestComplete(true);
    }
  };

  const confirmSubmitSection = () => {
    setShowModal(false);
    if (currentSectionIndex < testData.sections.length - 1) {
      setCurrentSectionIndex(currentSectionIndex + 1);
      setCurrentQuestionIndex(0);
    }
  };

  return (
    <Container fluid>
      {/* Test Interface Content */}
      {testComplete ? (
        <Results testData={testData} selectedAnswers={selectedAnswers} />
      ) : (
        <>
          {/* Test interface with question, sections, etc. */}
          <Row>
            {/* Left Column: Single Question Display */}
            <Col
              md={9}
              className="border-end d-flex flex-column"
              style={{ maxHeight: "calc(100vh - 50px)", overflowY: "auto" }}
            >
              {/* Top Section with Question Number, Marks, Timer, and Language */}
              <Row className="mb-3 d-flex justify-content-between align-items-center shadow-sm border-bottom border-2">
                <Col md="auto">
                  <h5 className="fw-semibold">
                    Question No. {currentQuestionIndex + 1}
                  </h5>
                </Col>

                <Col md="auto" className="d-flex align-items-center">
                  <div className="text-center me-3">
                    <small className="mb-1 small">Marks</small>
                    <p>
                      <span className="bg-success small text-white px-2 py-1 rounded-5 me-2">
                        +1
                      </span>
                      <span className="bg-danger small text-white px-2 py-1 rounded-5">
                        -0.25
                      </span>
                    </p>
                  </div>

                  <div className="text-center me-3">
                    <small className="mb-1">Time</small>
                    <p className="mb-0 small">
                      {String(Math.floor(timer / 60)).padStart(2, "0")}:
                      {String(timer % 60).padStart(2, "0")}
                    </p>
                  </div>

                  <Dropdown>
                    view in
                    <Dropdown.Toggle variant="secondary" size="sm">
                      {language}
                    </Dropdown.Toggle>
                    <Dropdown.Menu>
                      <Dropdown.Item onClick={() => setLanguage("English")}>
                        English
                      </Dropdown.Item>
                      <Dropdown.Item onClick={() => setLanguage("Hindi")}>
                        Hindi
                      </Dropdown.Item>
                    </Dropdown.Menu>
                  </Dropdown>
                </Col>
              </Row>

              {/* Question Display */}
              <div className="border p-3 mb-4">
                <h5>
                  <u>Question</u>
                </h5>
                {/* Check if question has sub-questions */}
                {currentQuestion.subQuestions ? (
                  <Row>
                    <Col md={6}>
                      <h5>{currentQuestion.text}</h5>
                      <div className="mt-3">{currentQuestion.description}</div>
                    </Col>

                    <Col md={6}>
                      <div className="mt-3">
                        {currentQuestion.subQuestions.map((subQuestion) => (
                          <div key={subQuestion.text} className="mb-3">
                            <h6>{subQuestion.text}</h6>
                            {subQuestion.options.map((option) => (
                              <div key={option} className="mb-2">
                                <input
                                  type="radio"
                                  id={option}
                                  name={`sub-question-${subQuestion.text}`}
                                  value={option}
                                  checked={selectedAnswers[subQuestion.text] === option}
                                  onChange={() =>
                                    handleSelectAnswer(subQuestion.text, option)
                                  }
                                />
                                <label htmlFor={option} className="ms-2">
                                  {option}
                                </label>
                              </div>
                            ))}
                          </div>
                        ))}
                      </div>
                    </Col>
                  </Row>
                ) : (
                  <div className="mt-3">
                    <Col md={6}>
                      <h5>{currentQuestion.text}</h5>
                      <div className="mt-3">{currentQuestion.description}</div>
                    </Col>
                    {/* Single question without sub-questions */}
                    {currentQuestion.options.map((option) => (
                      <div key={option} className="mb-2">
                        <input
                          type="radio"
                          id={option}
                          name={`question-${currentQuestion.id}`}
                          value={option}
                          checked={selectedAnswers[currentQuestion.id] === option}
                          onChange={() =>
                            handleSelectAnswer(currentQuestion.id, option)
                          }
                        />
                        <label htmlFor={option} className="ms-2">
                          {option}
                        </label>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Fixed Navigation Buttons */}
              <div
                className="d-flex justify-content-between"
                style={{
                  position: "sticky",
                  bottom: "0",
                  backgroundColor: "#fff",
                  zIndex: "10",
                  padding: "10px",
                }}
              >
                <Button
                  variant="secondary"
                  onClick={handlePrevious}
                  disabled={currentQuestionIndex === 0}
                >
                  Previous
                </Button>
                <Button
                  variant="success"
                  onClick={handleNext}
                  disabled={
                    currentQuestionIndex === currentSection.questions.length - 1
                  }
                >
                  Next
                </Button>
              </div>
            </Col>

            {/* Right Column: Section Navigation & Submit Buttons */}
            <Col
              md={3}
              className="py-4 d-flex flex-column justify-content-between bg-success bg-opacity-10"
              style={{ height: "92vh" }}
            >
              <div>
                {testData.sections.map((section, index) => (
                  <div key={index} className={`mb-3`}>
                    <div className="w-full bg-success bg-opacity-25 p-2">
                      <span className="fw-bold">Section: </span>
                      {section.name}{" "}
                      {index === currentSectionIndex ? "(Active)" : "(Locked)"}
                    </div>
                    {index === currentSectionIndex && (
                      <div className="d-flex flex-wrap mt-2 gap-3">
                        {section.questions.map((q, qIndex) => {
                          // Determine the button background color based on the question status
                          let buttonVariant = "outline-secondary"; // Default (Unvisited)
                          if (qIndex === currentQuestionIndex) {
                            buttonVariant = "secondary"; // Active question
                          } else if (selectedAnswers[q.id]) {
                            buttonVariant = "success"; // Answered question
                          } else if (qIndex < currentQuestionIndex) {
                            buttonVariant = "danger"; // Unanswered but visited question
                          }

                          return (
                            <Button
                              key={q.id}
                              variant={buttonVariant}
                              className="m-1"
                              onClick={() => handleSelectQuestion(qIndex)}
                            >
                              {q.id}
                            </Button>
                          );
                        })}
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <Button
                variant="success"
                className="w-100 mt-3"
                onClick={handleSubmitSection}
              >
                Submit Section
              </Button>
              <Button variant="dark" className="w-100 mt-3">
                Submit Test
              </Button>
            </Col>
          </Row>

          {/* Modal for Section Submission */}
          <Modal show={showModal} onHide={() => setShowModal(false)}>
            <Modal.Header closeButton>
              <Modal.Title>Submit {currentSection.name}?</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <div>
                <p>Questions Answered: {questionStatuses.answered}</p>
                <p>Questions Not Attempted: {questionStatuses.notAttempted}</p>
                <p>Questions Not Visited: {questionStatuses.notVisited}</p>
              </div>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={() => setShowModal(false)}>
                Cancel
              </Button>
              <Button variant="success" onClick={confirmSubmitSection}>
                Submit
              </Button>
            </Modal.Footer>
          </Modal>
        </>
      )}
    </Container>
  );
};

export default TestInterface;
