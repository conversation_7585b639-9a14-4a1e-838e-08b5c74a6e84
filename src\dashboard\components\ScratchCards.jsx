import React, { useEffect, useState } from "react";
import styles from "./ScratchCards.module.scss"; // Import SCSS module

const ScratchCards = ({ onCompleteScratch, prizeValue }) => {
    const [scratchedArea, setScratchedArea] = useState(0); // To track the scratched area
    const [isScratchComplete, setIsScratchComplete] = useState(false); // Track if scratch is complete

    useEffect(() => {
        const canvasElement = document.getElementById("scratch");
        const canvasContext = canvasElement.getContext("2d");
        let isDragging = false;

        // Create a new image object
        const image = new Image();
        image.src = '/scard.jpg'; // Use the path to your image

        image.onload = () => {
            // Once the image is loaded, initialize the canvas
            canvasContext.drawImage(image, 0, 0, 150, 150);
        };

        const scratch = (x, y) => {
            canvasContext.globalCompositeOperation = "destination-out";
            canvasContext.beginPath();
            canvasContext.arc(x, y, 12, 0, 2 * Math.PI);
            canvasContext.fill();

            // Increment scratched area after every scratch
            setScratchedArea((prev) => prev + Math.PI * 12 * 12); // Approximate scratched area based on the circle area formula
        };

        const getMouseCoordinates = (event) => {
            const rect = canvasElement.getBoundingClientRect();
            const x = (event.pageX || event.touches[0].pageX) - rect.left;
            const y = (event.pageY || event.touches[0].pageY) - rect.top;
            return { x, y };
        };

        const handleMouseDown = (event) => {
            isDragging = true;
            const { x, y } = getMouseCoordinates(event);
            scratch(x, y);
        };

        const handleMouseMove = (event) => {
            if (isDragging) {
                event.preventDefault();
                const { x, y } = getMouseCoordinates(event);
                scratch(x, y);
            }
        };

        const handleMouseUp = () => {
            isDragging = false;
        };

        const handleMouseLeave = () => {
            isDragging = false;
        };

        const isTouchDevice = 'ontouchstart' in window;

        canvasElement.addEventListener(isTouchDevice ? "touchstart" : "mousedown", handleMouseDown);
        canvasElement.addEventListener(isTouchDevice ? "touchmove" : "mousemove", handleMouseMove);
        canvasElement.addEventListener(isTouchDevice ? "touchend" : "mouseup", handleMouseUp);
        canvasElement.addEventListener("mouseleave", handleMouseLeave);

        return () => {
            canvasElement.removeEventListener(isTouchDevice ? "touchstart" : "mousedown", handleMouseDown);
            canvasElement.removeEventListener(isTouchDevice ? "touchmove" : "mousemove", handleMouseMove);
            canvasElement.removeEventListener(isTouchDevice ? "touchend" : "mouseup", handleMouseUp);
            canvasElement.removeEventListener("mouseleave", handleMouseLeave);
        };
    }, []);

    useEffect(() => {
        // Check if scratched area exceeds 50% of the total canvas area (200x200)
        const totalArea = 450 * 450;
        if (!isScratchComplete && scratchedArea / totalArea > 0.5) {
            setIsScratchComplete(true); // Mark as complete to prevent multiple calls
            onCompleteScratch(prizeValue); // Trigger the callback
        }
    }, [scratchedArea, onCompleteScratch, prizeValue, isScratchComplete]);

    return (
        <div>
        <div className={styles.card_container}>
            <div className={styles.base}>
                <h4>You Won</h4>
                <h3>{prizeValue}</h3>
            </div>
            <canvas
                id="scratch"
                className={styles.scratch}
                width="150"
                height="150"
            ></canvas>
        </div>
        </div>
    );
};

export default ScratchCards;
