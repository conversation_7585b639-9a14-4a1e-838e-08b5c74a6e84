import React from "react";
import { Card, Col, Row } from "react-bootstrap";

const ExamOverview = ({ data }) => {
  return (
    <>
      <Card className="shadow-sm border-0 bg-white my-4">
        <Card.Body className="text-muted">{data?.description}</Card.Body>
      </Card>

      <Card className="p-3 shadow-sm border-0">
        <Row>
          <Col md={12}>
            <h5 className="fw-medium border-bottom border-bottom-2 border-secondary pb-2">
              {data?.name} Overview
            </h5>
            <div className="d-flex flex-wrap">
              {data?.overview?.map((info, index) => (
                <Col md={4} xs={12} key={index} className="p-2 mt-2">
                  <Card className="shadow-sm rounded-3">
                    <Card.Body className="p-0">
                      <h6 className="bg-success text-white p-2 rounded-top-3">{info.name}</h6>
                      <div className="bg-success bg-opacity-10 p-2 rounded-bottom-3">
                        {info.value}
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              ))}
            </div>
          </Col>
        </Row>
      </Card>
    </>
  );
};

export default ExamOverview;
