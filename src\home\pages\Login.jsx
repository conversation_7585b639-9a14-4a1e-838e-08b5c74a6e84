import React, { useState, useEffect } from "react";
import { Container, Row, Col, Button, Form } from "react-bootstrap";
import { useNavigate, useLocation } from "react-router-dom";
import toast, { Toaster } from "react-hot-toast";
import { FaChalkboardTeacher } from "react-icons/fa";
import { GoogleLogin } from "@react-oauth/google";
import { jwtDecode } from "jwt-decode";
import NavBar from "../../commonCompoenents/NavBar";
import SideInfo from "../../commonCompoenents/SideInfo";
import { useDispatch, useSelector } from "react-redux";
import { googleLoginStudent, loginStudent } from "../../redux/slice/studentSlice";
import ForgetAuth from "../../components/ForgetAuth";
import { FcGoogle } from "react-icons/fc";
import { useTheme } from "../../context/ThemeContext";
import { DotLottieReact } from '@lottiefiles/dotlottie-react';

const Login = () => {
  const { isDarkMode, theme } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (location.hash) {
      const element = document.getElementById(location.hash.substring(1));
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      }
    }
  }, [location]);

  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [showForgetModal, setShowForgetModal] = useState(false);
  const [loadingGoogle, setLoadingGoogle] = useState(false);
  const [showStreakOverlay, setShowStreakOverlay] = useState(false);

  const [errors, setErrors] = useState({ username: "", password: "" });

  const baseUrl = import.meta.env.VITE_BASE_URI;
  const dispatch = useDispatch();
  const streak = useSelector((state) => state?.student?.student?.student?.streak);
  const streakReason = useSelector((state) => state?.student?.student?.student?.streak_reason);

  useEffect(() => {
    if (streak) {
      setShowStreakOverlay(true);
      const timer = setTimeout(() => setShowStreakOverlay(false), 5000); // Hide overlay after 5 seconds
      return () => clearTimeout(timer);
    }
  }, [streak]);

  const validateForm = () => {
    let valid = true;
    let errors = { username: "", password: "" };

    if (!username.trim()) {
      errors.username = "Username is required";
      valid = false;
    }

    if (!password.trim()) {
      errors.password = "Password is required";
      valid = false;
    } else if (password.length < 6) {
      errors.password = "Password must be at least 6 characters";
      valid = false;
    }

    setErrors(errors);
    return valid;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    const loginData = {
      username: username,
      password: password,
    };

    try {
      setLoading(true);
      const response = await dispatch(loginStudent({ loginData: loginData }));
      if (response.meta.requestStatus === "fulfilled") {
        setLoading(false);
        toast.success("Login successful!");
        setShowStreakOverlay(true); // Show streak overlay on successful login
        setTimeout(() => {
          navigate("/dashboard"); // Redirect after 5 seconds
        }, 5000);
      } else {
        toast.error("invalid username or password");
        setLoading(false);
      }
    } catch (error) {
      console.error("Error during login:", error);
      toast.error("Invalid username or password. Please try again.");
      setLoading(false);
    }
  };

  const handleGoogleLoginSuccess = async (response) => {
    try {
      setLoadingGoogle(true);
      const decode = jwtDecode(response?.credential);
      const loginData = {
        email: decode?.email,
        first_name: decode?.given_name,
        last_name: decode?.family_name || "",
        image: decode?.picture,
      };
      const res = await dispatch(
        googleLoginStudent({ googleloginData: loginData })
      );
      if (res?.meta?.requestStatus === "fulfilled") {
        toast.success("Login successful!");
        setLoadingGoogle(false);
        setShowStreakOverlay(true); // Show streak overlay on successful login
        setTimeout(() => {
          navigate("/dashboard"); // Redirect after 5 seconds
        }, 5000);
      } else if (res?.meta?.requestStatus === "rejected") {
        toast.error("Google Login failed. Please try again.");
        setLoadingGoogle(false);
      }
    } catch (error) {
      console.error("Error decoding the Google token:", error);
      toast.error("Google Login failed. Please try again.");
      setLoadingGoogle(false);
    }
  };

  const handleGoogleLoginError = () => {
    toast.error("Google Login failed. Please try again.");
  };

  return (
    <>
      <Toaster />
      {showStreakOverlay && streak && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            backgroundColor: "rgba(0, 0, 0, 0.7)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 1050,
          }}
        >
          <div
            style={{
              color: "white",
              fontSize: "2rem",
              fontWeight: "bold",
              textAlign: "center",
              padding: "20px",
              borderRadius: "10px",
            }}
          >
            <DotLottieReact
              src="/Animation.lottie"
              loop
              autoplay
            />
            Streak {streak}
            <br />
            {streakReason && <span style={{ fontSize: "1.5rem", fontWeight: "normal" }}>{streakReason}</span>}
          </div>
        </div>
      )}
      <NavBar />
      {loadingGoogle ? (
        <>
          <Container>
            <Row className="justify-content-center mt-5">
              <Col xs={12} md={12} className="mt-5">
                <div className="text-center mt-5">
                  <FcGoogle size={48} />
                  <p className="text-center">Please wait...</p>
                </div>
              </Col>
            </Row>
          </Container>
        </>
      ) : (
        <Container
          fluid
          className="d-flex flex-column justify-content-center align-items-center"
          style={{
            minHeight: "100vh",
            minWidth: "100vw",
            background: isDarkMode
              ? "linear-gradient(130deg, #1a1a1a, #2d4a3a), url('https://www.transparenttextures.com/patterns/gplay.png')"
              : "linear-gradient(130deg, white,rgb(129, 194, 141)), url('https://www.transparenttextures.com/patterns/gplay.png')",
            backgroundBlendMode: "overlay",
            opacity: 0.9,
            transition: "background 0.3s ease"
          }}
        >
          <Row className="justify-content-center align-items-center gap-5">
            <Col md={5} lg={5} xs={0} className="mt-5 d-md-block d-none">
              <SideInfo />
            </Col>
            <Col md={5} lg={3} xs={12} className="p-md-0 p-4 mt-5">
              <Row
                className="rounded-4 border-2 border-md border p-md-4 p-2"
                style={{
                  backgroundColor: theme.colors.cardBackground, // Always white (as per requirement)
                  borderColor: theme.colors.cardBorder,
                  color: theme.colors.cardText,
                  transition: "all 0.3s ease"
                }}
              >
                <Row className="mb-4">
                  <FaChalkboardTeacher size={60} color="#146c43" />
                  <h3
                    className="text-center mt-3"
                    style={{
                      color: "#146c43",
                    }}
                  >
                    Login
                  </h3>
                </Row>

                <Form onSubmit={handleSubmit} className="w-100">
                  <Form.Group className="mb-3">
                    <Form.Control
                      type="text"
                      placeholder="Username"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      isInvalid={!!errors.username}
                      style={{ backgroundColor: "#e4e4e7", color: "#3F3D56" }}
                    />
                    <Form.Control.Feedback type="invalid">
                      {errors.username}
                    </Form.Control.Feedback>
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Control
                      type="password"
                      placeholder="Password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      isInvalid={!!errors.password}
                      style={{ backgroundColor: "#e4e4e7", color: "#3F3D56" }}
                    />
                    <Form.Control.Feedback type="invalid">
                      {errors.password}
                    </Form.Control.Feedback>
                  </Form.Group>

                  <Row className="mb-3">
                    <Col className="text-end">
                      <Button
                        variant="link"
                        onClick={() => setShowForgetModal(true)}
                        className="primary"
                        style={{
                          textDecoration: "none",
                          color: isDarkMode ? '#888888' : '#6c757d',
                          fontSize: '0.9rem',
                          padding: '0',
                          border: 'none',
                          background: 'none',
                          transition: "color 0.3s ease"
                        }}
                      >
                        Forgot Password?
                      </Button>
                    </Col>
                  </Row>

                  <Button
                    type="submit"
                    variant="success"
                    className="w-100"
                    style={{ color: "#f3f4f6" }}
                    disabled={loading}
                  >
                    {loading ? "Logging.." : "Login"}
                  </Button>
                </Form>

                <Row className="mt-4">
                  <Col className="text-center">
                    <p
                      style={{
                        color: isDarkMode ? '#888888' : '#6c757d',
                        transition: "color 0.3s ease"
                      }}
                    >
                      Or Continue With
                    </p>
                  </Col>
                </Row>

                <Row className="d-flex justify-content-center">
                  <Col className="d-flex justify-content-center">
                    <GoogleLogin
                      onSuccess={handleGoogleLoginSuccess}
                      onError={handleGoogleLoginError}
                    />
                  </Col>
                </Row>

                <Row className="mt-3">
                  <Col className="d-flex justify-content-center align-items-center">
                    <p className="text-muted m-0">Don't have an account? </p>
                    <Button
                      variant="link"
                      onClick={() => navigate("/signup")}
                      className="text-muted"
                    >
                      Signup
                    </Button>
                  </Col>
                </Row>
              </Row>
            </Col>
          </Row>
          <ForgetAuth
            showForgetModal={showForgetModal}
            setShowForgetModal={setShowForgetModal}
          />
        </Container>
      )}
    </>
  );
};

export default Login;
