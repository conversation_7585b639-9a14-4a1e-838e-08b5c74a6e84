import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Function to get Auth Token from Redux store (student's access token)
const getAuthToken = (getState) => {
  const state = getState();
  return {
    accessToken: state?.student?.student["JWT_Token"]?.access,
    refreshToken: state?.student?.student["JWT_Token"]?.refresh,
  };
};


// Get customer profile
export const getCustomerCareList = createAsyncThunk(
  "customerCare/getCustomerCareList",
  async (_, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      if (!accessToken) {
        return rejectWithValue("Authentication token missing");
      }

      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CUSTOMER_CARE_PROFILES}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error fetching customer care profiles");
    }
  }
);

// Customer care slice
const customerCareSlice = createSlice({
  name: "customerCare",
  initialState: {
    customer: null,
    loading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Get customer profile
      .addCase(getCustomerCareList.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getCustomerCareList.fulfilled, (state, action) => {
        state.loading = false;
        state.customer = action.payload;
      })
      .addCase(getCustomerCareList.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default customerCareSlice.reducer;
