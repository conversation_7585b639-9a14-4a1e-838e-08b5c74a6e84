.dropdownContainer {
  position: relative; 
}

.dropdownContent {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  max-width: 1000px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 5px;
  z-index: 1000;
  display: flex;
  flex-direction: row;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  max-height: 85vh;
  overflow-y: auto;
}

.itemsContainer {
  flex: 1; /* Ensure equal space for topics */
  background-color: #fff; /* Always white background */
  border-right: 1px solid #ddd;
  overflow-y: auto;
  transition: background-color 0.3s ease;
}

.left-item:hover {
  background-color: #8f8787 !important; /* Set background color to white on hover */
}

.item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.item:hover {
  background-color: #f0f0f0; /* Add hover effect for subjects */
  cursor: pointer;
}

.activeItem {
  background-color: #d1e7dd; /* Highlight selected subject */
  font-weight: bold;
}

.item:hover,
.activeItem {
  background-color: #fff; /* Turn white on hover */
  color: #000; /* Optional: Change text color to black for better contrast */
}

.itemImage {
  margin-right: 10px;
  transition: transform 0.3s ease;
}

.itemImage:hover {
  transform: scale(1.1);
}

.arrowIcon {
  margin-left: auto;
  color: #28a745;
}

.subItemsContainer {
  flex: 1; /* Ensure equal space for subtopics */
  background-color: #f1f4f6;
  overflow-y: auto;
  padding: 10px;
}

.subItemCard {
  border: none;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  margin-bottom: 10px;
}

.subItemCard:hover {
  background-color: #f0f0f0; /* Add hover effect for topics */
  cursor: pointer;
}

.subItemCard.activeTopic {
  background-color: #d1e7dd; /* Highlight selected topic */
  font-weight: bold;
}

.subItemCard:hover,
.subItemCard.activeTopic {
  background-color: #d1e7dd; /* Highlight hovered or active topic */
  font-weight: bold;
}

.subItemCard:hover {
  box-shadow: 0px 2px 4px rgba(130, 237, 157, 0.1);
}

.subItemCardBody {
  display: flex;
  align-items: center;
  gap: 10px;
}

.subItemImage {
  transition: transform 0.3s ease;
}

.subItemImage:hover {
  transform: scale(1.1);
}

.subItemText {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.noItemsText {
  color: #dc3545;
  text-align: center;
}

.courseName {
  transition: background-color 0.3s ease;
}

.courseName:hover {
  background-color: #bae2cb; /* Change background color on hover */
}

.commonCard {
  margin: 8px 0; /* Consistent margin */
  padding: 0; /* Consistent padding */
  border-radius: 8px; /* Rounded corners */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Subtle shadow */
}

.commonCardBody {
  display: flex;
  align-items: center;
  gap: 10px; /* Space between elements */
}

.subItemCardBody,
.subTopicCardBody {
  padding: 5px; /* Ensure consistent padding */
}

.subTopicCard {
  padding: 0rem;
}

.subItemText,
.subTopicText {
  font-size: 14px; /* Consistent font size */
  color: #333; /* Text color */
}

.subTopicsContainer {
  margin: 1.5rem 0.5rem;
}

.responsiveDropdown {
  width: 100%;
}

.responsiveContent {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.responsiveItems,
.responsiveSubItems,
.responsiveSubTopics {
  flex: 1;
  min-width: 30%; /* Ensure equal division */
  padding: 5px;
  box-sizing: border-box;
}

.item:hover,
.subItemCard:hover,
.subTopicCard:hover {
  background-color: #e6f7ff; /* Hover background color */
  cursor: pointer;
}

.activeItem {
  background-color: #cceeff; /* Active background color */
}

/* Media Queries */

/* Small screens (max-width: 576px) */
@media (max-width: 767px) {
  .dropdownContent {
    flex-direction: column;
    max-height: 75vh;
  }

  .itemsContainer {
    max-height: 30vh;
  }

  .subItemsContainer {
    max-height: 30vh;
  }
}

/* Large screens (min-width: 769px) */
@media (min-width: 1021px) {
  .dropdownContent {
    position: absolute;
    top: 0;
    left: -250px;
    width: 1000px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 5px;
    z-index: 1000;
    display: flex;
    flex-direction: row;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  }

  .itemsContainer {
    /* Removed max-height restriction */
    overflow-y: auto; /* Ensure scrollable content */
  }

  .subItemsContainer {
    /* Removed max-height restriction */
    overflow-y: auto; /* Ensure scrollable content */
  }
}
