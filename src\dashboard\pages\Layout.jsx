import React, { useEffect } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import { Container, Row, Col } from "react-bootstrap";
import Sidebar from "../../commonCompoenents/Sidebar";
import CustomNavbar from "../../commonCompoenents/Nav";
import NavBar from "../../commonCompoenents/NavBar";
import { useSelector } from "react-redux";
import { Toaster, toast } from "react-hot-toast";

const Layout = () => {
  const navigate = useNavigate(); // Hook to handle redirection
  const accessToken = useSelector((state) => state?.student?.student?.JWT_Token?.access);

  // Check for access token on page load
  useEffect(() => {
    if (!accessToken) {
      navigate('/login'); // Immediately redirect to login
      return;
    }
  }, [accessToken, navigate]);

  return (
    <>
      <Container fluid>
        <Row>
          <Col md={3} lg={2} className="p-0">
            <Sidebar />
          </Col>
          <Col md={9} lg={10}>
            <CustomNavbar />
            <div
              style={{
                maxHeight: "90vh",
                overflowY: "scroll",
                scrollbarWidth: "none" /* Firefox */,
                msOverflowStyle: "none",
              }}
              className="overflow-y-auto"
            >
              <Outlet />
            </div>
          </Col>
        </Row>
        <Toaster />
      </Container>
    </>
  );
};

export default Layout;


