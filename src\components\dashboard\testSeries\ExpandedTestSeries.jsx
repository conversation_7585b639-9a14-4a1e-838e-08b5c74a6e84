import React, { useState, useEffect } from "react";
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Button, Tabs, Tab } from "react-bootstrap";
import { FaClip<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaRegClock } from "react-icons/fa";
import Skeleton from "react-loading-skeleton";
// import data from "../../../dummyData/testSeries";
import AsideInfo from "./AsideInfo";
import FreeTest from "./FreeTest";
import { useDispatch } from "react-redux";
import { createPaper } from "../../../redux/slice/paperSlice";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";

const ExpandedTestSeries = ({ data }) => {
  const [key, setKey] = useState(data?.Tiers[0]?.key);
  const [loading, setLoading] = useState(true); // Loading state
  const [loadingTest, setLoadingTest] = useState(false);

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const handleSelectTab = (selectedKey) => {
    setKey(selectedKey);
  };

  // const selectedCategory = data.testSeries.categories.find(
  //   (category) => category.key === key
  // );

  useEffect(() => {
    // Simulating data fetch
    setTimeout(() => {
      setLoading(false); // Set loading to false once data is "loaded"
    }, 3000); // Simulate loading for 2 seconds
  }, []);

  const handleStartTest = async(paper_id) => {
    setLoadingTest(true);
    try {
      const res = await dispatch(createPaper({entity_id: paper_id}));
      if(res?.meta?.requestStatus === 'fulfilled'){
        // navigate("/test-attempt");
        navigate("/exam-dashboard");
        console.log("Test started successfully");
      }else{
        console.log("Error in starting test");
      }
    } catch (error) {
      console.log("An error occured ", error);
      toast.error("An error occured!\nPlease try again later.");
    }finally{
      setLoadingTest(false);
    }
  }

  return (
    <Container className="my-4">
      <Row className="flex-wrap justify-content-center">
        <Col md={10}>
          <Row className="flex-wrap justify-content-between">
            {/* Left Column: Tabs and Test Cards */}
            <Col xs={12} md={8} className="mb-4">
              {loading ? (
                // Skeleton Loader for Tabs
                <Skeleton
                  count={1}
                  height={50}
                  baseColor="#e6ffe6"
                  highlightColor="#c4f7c4"
                />
              ) : (
                <Tabs
                  id="testSeriesTabs"
                  activeKey={key}
                  onSelect={handleSelectTab}
                  className="mb-4"
                >
                  {data?.Tiers?.map((category) => (
                    <Tab
                      eventKey={category?.key}
                      title={category?.Tier_name}
                      key={category?.key}
                    >
                      <Row className="mt-4">
                        <Col sm={12}>
                          {loading ? (
                            // Skeleton Loader for Test Cards
                            <Skeleton
                              count={3}
                              height={100}
                              baseColor="#e6ffe6"
                              highlightColor="#c4f7c4"
                            />
                          ) : category?.paper.length > 0 ? (
                            category?.paper.map((test, index) => (
                              <Card key={test?.paper_id} className="mb-3">
                                <Card.Body>
                                  <Card.Title>{test?.paper_name}</Card.Title>
                                  <Card.Text>
                                    {/* Use flexbox for layout */}
                                    <div className="d-flex flex-wrap justify-content-between align-items-center">
                                      {/* Left side content */}
                                      <div>
                                        <div
                                          className="d-flex flex-wrap align-items-center text-muted"
                                          style={{ fontSize: "0.85rem" }}
                                        >
                                          <FaClipboardList className="me-2" />{" "}
                                          {/* Icon for total questions */}
                                          <span>
                                            {test?.totalQuestions} Questions
                                          </span>
                                          <span className="mx-2">|</span>
                                          <FaPercent className="me-2" />{" "}
                                          {/* Icon for total marks */}
                                          <span>{test?.totalMarks} Marks</span>
                                          <span className="mx-2">|</span>
                                          <FaRegClock className="me-2" />{" "}
                                          {/* Icon for duration */}
                                          <span>
                                            {test?.durationMinutes} minutes
                                          </span>
                                        </div>
                                        <br />
                                        Expire on {test?.expiryDate}
                                      </div>

                                      {/* Right side: Button */}
                                      <Button variant="success" className="btn-sm" onClick={() => handleStartTest(test?.paper_id)}>
                                        {
                                          loadingTest ? "Loading..." : (test?.type === "paid"
                                          ? "Unlock"
                                          : "Start Now")
                                        }
                                      </Button>
                                    </div>
                                  </Card.Text>
                                </Card.Body>
                              </Card>
                            ))
                          ) : (
                            <p>No tests available in this category.</p>
                          )}
                        </Col>
                      </Row>
                    </Tab>
                  ))}
                </Tabs>
              )}
            </Col>

            {/* Right Column: AsideInfo Component */}
            <Col xs={12} md={4}>
              <AsideInfo />
            </Col>
          </Row>
          <Row className="justify-content-between">
            <Col xs={12} md={8} className="mb-4">
              <FreeTest data={data}/>
            </Col>
            <Col xs={12} md={4}></Col>
          </Row>
        </Col>
      </Row>
    </Container>
  );
};

export default ExpandedTestSeries;
