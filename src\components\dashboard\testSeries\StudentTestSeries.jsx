import React from "react";
import { useSelector } from "react-redux";
import { Container, <PERSON>, Col, Card, Button, Image } from "react-bootstrap";
import { <PERSON> } from "react-router-dom";
import MyTestSereis from "./MyTestSereis";
import RaiseQuestion from "../../../commonCompoenents/RaiseQuestion";

const StudentTestSeries = () => {
  const student = useSelector((state) => state.student);


  const popularTestSereis = [
    {
        id: 1,
        name: "Prelims Test Series",
        totalTests: 10,
        freeTests: 2,
        logo: "https://tse3.mm.bing.net/th?id=OIP.cpUx-frMDfaLW7PxllmcegHaEK&pid=Api&P=0&h=180",
      },
      {
        id: 2,
        name: "SSC Mains Test Series",
        totalTests: 8,
        freeTests: 1,
        logo: "https://tse3.mm.bing.net/th?id=OIP.cpUx-frMDfaLW7PxllmcegHaEK&pid=Api&P=0&h=180",
      },
      {
        id: 3,
        name: "Advanced Test Series",
        totalTests: 12,
        freeTests: 3,
        logo: "https://tse3.mm.bing.net/th?id=OIP.cpUx-frMDfaLW7PxllmcegHaEK&pid=Api&P=0&h=180",
      },
      {
        id: 1,
        name: "Prelims Test Series",
        totalTests: 10,
        freeTests: 2,
        logo: "https://tse3.mm.bing.net/th?id=OIP.cpUx-frMDfaLW7PxllmcegHaEK&pid=Api&P=0&h=180",
      },
      {
        id: 2,
        name: "SSC Mains Test Series",
        totalTests: 8,
        freeTests: 1,
        logo: "https://tse3.mm.bing.net/th?id=OIP.cpUx-frMDfaLW7PxllmcegHaEK&pid=Api&P=0&h=180",
      },
      {
        id: 3,
        name: "Advanced Test Series",
        totalTests: 12,
        freeTests: 3,
        logo: "https://tse3.mm.bing.net/th?id=OIP.cpUx-frMDfaLW7PxllmcegHaEK&pid=Api&P=0&h=180",
      },
  ]

  // Dummy data for test series
  const mytestSeriesData = [
    {
      id: 1,
      name: "Prelims Test Series",
      totalTests: 10,
      freeTests: 2,
      logo: "https://tse3.mm.bing.net/th?id=OIP.cpUx-frMDfaLW7PxllmcegHaEK&pid=Api&P=0&h=180",
    },
    {
      id: 2,
      name: "SSC Mains Test Series",
      totalTests: 8,
      freeTests: 1,
      logo: "https://tse3.mm.bing.net/th?id=OIP.cpUx-frMDfaLW7PxllmcegHaEK&pid=Api&P=0&h=180",
    },
    {
      id: 3,
      name: "Advanced Test Series",
      totalTests: 12,
      freeTests: 3,
      logo: "https://tse3.mm.bing.net/th?id=OIP.cpUx-frMDfaLW7PxllmcegHaEK&pid=Api&P=0&h=180",
    },
  ];

  return (
    <Container className="mt-5">
    <RaiseQuestion/>
    <Row className="justify-content-center">
        <Col md={10}>
            <h4 className="text-start mb-4">Your <span className=" text-success">Test-Series</span></h4>
        </Col>
    </Row>
      <Row className="justify-content-center mb-4">
      <Col md={10}>
      <MyTestSereis mytestSeriesData={mytestSeriesData}/>
        </Col>
      </Row>

      <hr></hr>

      <Row className="justify-content-center mt-5">
        <Col md={10}>
            <h4 className="text-start mb-4">More Popular <span className=" text-success">Test-Series</span></h4>
        </Col>
    </Row>

    <Row className="justify-content-center">
    <Col md={10}>
      <Row>
        {popularTestSereis.map((series) => (
          <Col key={series.id} md={6} lg={3} className="mb-3">
            <Card
              className="bg-light rounded-4 shadow-sm border-1"
              style={{minHeight: "200px"}}>
              <Card.Body>
                <div className="d-flex align-items-center gap-2 mb-3">
                <Image
                variant="top"
                src={series.logo}
                alt={series.name}
                style={{ width: "36px", height: "36px", objectFit: "cover" }}
              />
                <Card.Title>{series.name}</Card.Title>
                </div>
                <Card.Text>
                  <strong>Total Tests:</strong> {series.totalTests}
                  <br />
                  <strong>Free Tests:</strong> {series.freeTests}
                </Card.Text>
                  <Button variant="success" className="w-100">
                    Add Test series
                  </Button>
              </Card.Body>
            </Card>
          </Col>
        ))}
        </Row>
        </Col>
      </Row>
    </Container>
  );
};

export default StudentTestSeries;
