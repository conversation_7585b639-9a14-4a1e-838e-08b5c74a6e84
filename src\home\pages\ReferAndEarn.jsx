import React from "react";
import { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { FaGift, FaUser<PERSON>riends, FaWhatsapp, FaRegSmile } from "react-icons/fa";
import NavBar from '../../commonCompoenents/NavBar';
import Footer from '../components/Footer';

const ReferAndEarn = () => {
  return (
    <>
      <NavBar />
      <div style={{ minHeight: '80vh', background: '#f5f6fa', paddingTop: 60 }}>
        <Container style={{ maxWidth: 900, background: '#fff', borderRadius: 12, boxShadow: '0 2px 8px #0001', padding: 32 }} className="my-5">
          <Row className="align-items-center g-4">
            <Col xs={12} md={6}>
              <h2 className="mb-3" style={{ color: '#0078d4' }}><FaGift className="me-2" />Re<PERSON> & <PERSON>arn</h2>
              <p>Share Shashtrarth with your friends! Whenever your friend signs up using your referral, you get a <b>Scratch Card</b> with a surprise reward. You can avail the scratched amount anytime using the Refer & Earn tab after login.</p>
              <ul>
                <li>Share your referral using one of the following options after login:</li>
                <ul>
                  <li>Referral Code</li>
                  <li>QR Code</li>
                  <li>Referral URL</li>
                </ul>
                <li>Your friend must sign up using your referral.</li>
                <li>Once your friend signs up, you get a scratch card in your account.</li>
                <li>Scratch cards can be availed for real rewards anytime!</li>
              </ul>
              <div className="alert alert-info mt-3">
                <b>To use referral features, please <a href="/login">log in</a> and go to the Refer & Earn section in your dashboard.</b>
              </div>
            </Col>
            <Col xs={12} md={6} className="d-flex flex-column align-items-center">
              <FaUserFriends size={60} color="#0078d4" className="mb-3" />
              <p className="text-center">Invite your friends and start earning rewards!<br/>Login to get your referral code, QR, and link.</p>
            </Col>
          </Row>
        </Container>
      </div>
      <Footer />
    </>
  );
};

export default ReferAndEarn;
