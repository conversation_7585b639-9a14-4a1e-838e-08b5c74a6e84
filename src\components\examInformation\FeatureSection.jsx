import React, { useState } from "react";
import { Con<PERSON><PERSON>, <PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { FaId<PERSON>ard, FaTrophy, FaFileAlt, FaChart<PERSON>ie, FaBook<PERSON><PERSON>, <PERSON>a<PERSON>ser<PERSON><PERSON><PERSON>, FaMoneyBillWave } from "react-icons/fa";
import { Link } from "react-router-dom";

const FeatureSection = () => {
  // Track the active button by using state
  const [activeButton, setActiveButton] = useState('overview');

  // Function to handle active button click
  const handleButtonClick = (buttonName) => {
    setActiveButton(buttonName);
  };

  return (
    <Container fluid className="p-0 my-3 rounded-3">
      <Row className="no-gutters">
        <div className="d-flex justify-content-start align-items-md-start align-items-start gap-3 overflow-x-auto hide-scrollbar">
          <div className="col-md-2 col-5">
            <Button
              variant="transparent"
              as={Link}
              to="/dashboard/exam-info"
              className={`d-flex border-0 flex-column p-3 rounded-3 w-100 ${
                activeButton === "overview" ? "bg-success bg-opacity-10" : ""
              }`}
              onClick={() => handleButtonClick("overview")}
            >
              <div className="rounded-circle p-3 mb-3 mx-auto bg-primary bg-opacity-10">
                <FaChartPie size={28} className="text-primary" />
              </div>
              <p className="mb-0 text-center">Overview</p>
            </Button>
          </div>
          <div className="col-md-2 col-5">
            <Button
              variant="transparent"
              as={Link}
              to="/dashboard/exam-info/admit-card"
              className={`d-flex flex-column p-3 border-0 rounded-3 w-100 ${
                activeButton === "admitCard" ? "bg-success bg-opacity-10" : ""
              }`}
              onClick={() => handleButtonClick("admitCard")}
            >
              <div className="rounded-circle p-3 mb-3 mx-auto bg-danger bg-opacity-10">
                <FaIdCard size={28} className="text-danger" />
              </div>
              <p className="mb-0 text-center">Admit Card</p>
            </Button>
          </div>
          <div className="col-md-2 col-5">
            <Button
              variant="transparent"
              as={Link}
              to="/dashboard/exam-info/syllabus"
              className={`d-flex flex-column p-3 border-0 rounded-3 w-100 ${
                activeButton === "syllabus" ? "bg-success bg-opacity-10" : ""
              }`}
              onClick={() => handleButtonClick("syllabus")}
            >
              <div className="rounded-circle p-3 mb-3 mx-auto bg-success bg-opacity-10">
                <FaBookOpen size={28} className="text-success" />
              </div>
              <p className="mb-0 text-center">Syllabus</p>
            </Button>
          </div>
          <div className="col-md-2 col-5">
            <Button
              variant="transparent"
              as={Link}
              to="/dashboard/exam-info/eligibility-criteria"
              className={`d-flex flex-column p-3 border-0 rounded-3 ${
                activeButton === "eligibilityCriteria" ? "bg-success bg-opacity-10" : ""
              }`}
              onClick={() => handleButtonClick("eligibilityCriteria")}
            >
              <div className="rounded-circle p-3 mb-3 mx-auto bg-warning bg-opacity-10">
                <FaUserCheck size={28} className="text-warning" />
              </div>
              <p className="mb-0 text-center">Eligibility Criteria</p>
            </Button>
          </div>
          <div className="col-md-2 col-5">
            <Button
              variant="transparent"
              as={Link}
              to="/dashboard/exam-info/result"
              className={`d-flex flex-column p-3 border-0 rounded-3 ${
                activeButton === "result" ? "bg-success bg-opacity-10" : ""
              }`}
              onClick={() => handleButtonClick("result")}
            >
              <div className="rounded-circle p-3 mb-3 mx-auto bg-info bg-opacity-10">
                <FaTrophy size={28} className="text-info" />
              </div>
              <p className="mb-0 text-center">Result</p>
            </Button>
          </div>
          <div className="col-md-2 col-5">
            <Button
              variant="transparent"
              as={Link}
              to="/dashboard/exam-info/pyq"
              className={`d-flex flex-column p-3 border-0 rounded-3 ${
                activeButton === "pyq" ? "bg-success bg-opacity-10" : ""
              }`}
              onClick={() => handleButtonClick("pyq")}
            >
              <div className="rounded-circle p-3 mb-3 mx-auto bg-secondary bg-opacity-10">
                <FaFileAlt size={28} className="text-secondary" />
              </div>
              <p className="mb-0 text-center">Prev. Year Questions</p>
            </Button>
          </div>
          <div className="col-md-2 col-5">
            <Button
              variant="transparent"
              as={Link}
              to="/dashboard/exam-info/salary"
              className={`d-flex flex-column p-3 border-0 rounded-3 ${
                activeButton === "salary" ? "bg-success bg-opacity-10" : ""
              }`}
              onClick={() => handleButtonClick("salary")}
            >
              <div className="rounded-circle p-3 mb-3 mx-auto bg-dark bg-opacity-10">
                <FaMoneyBillWave size={28} className="text-dark" />
              </div>
              <p className="mb-0 text-center">Salary</p>
            </Button>
          </div>
        </div>
      </Row>
    </Container>
  );
};

export default FeatureSection;
