import React, { useEffect, useState } from "react";
import { But<PERSON>, Container, Image, Modal, Navbar } from "react-bootstrap";
import {
  FaCompress,
  FaExpand,
  FaInfoCircle,
  FaQuestionCircle,
} from "react-icons/fa";
import ExamInstruction from "./ExamInstruction";
import GeneralInstruction from "./GeneralInstruction";

const TestNavbar = ({timeLeft, setTimeLeft, autoSectionSubmit}) => {
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [showInstructionModal, setShownstructionModal] = useState(false); // State to control the modal visibility

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes}:${secs < 10 ? "0" : ""}${secs}`;
  };
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime > 0) {
          return prevTime - 1;
        } else {
          // clearInterval(timer);
          autoSectionSubmit(); // Call the submit function when timeLeft reaches 0
          return 0;
        }
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const toggleFullScreen = () => {
    if (!isFullScreen) {
      if (document.documentElement.requestFullscreen) {
        document.documentElement.requestFullscreen();
      } else if (document.documentElement.mozRequestFullScreen) {
        // Firefox
        document.documentElement.mozRequestFullScreen();
      } else if (document.documentElement.webkitRequestFullscreen) {
        // Chrome, Safari, Opera
        document.documentElement.webkitRequestFullscreen();
      } else if (document.documentElement.msRequestFullscreen) {
        // IE/Edge
        document.documentElement.msRequestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        // Firefox
        document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        // Chrome, Safari, Opera
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        // IE/Edge
        document.msExitFullscreen();
      }
    }
    setIsFullScreen(!isFullScreen);
  };

  const handleShowModal = () => setShownstructionModal(true);
  const handleCloseModal = () => setShownstructionModal(false);
  return (
    <>
      <Modal show={showInstructionModal} onHide={handleCloseModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Instructions</Modal.Title>
        </Modal.Header>
        <Modal.Body className="">
          <ExamInstruction />
          <GeneralInstruction />
        </Modal.Body>
      </Modal>
      <Navbar className="shadow-sm" bg="light" expand="lg">
        <Container
          fluid
          className="d-flex justify-content-between align-items-center flex-nowrap"
        >
          {/* Left - Logo */}
          <Navbar.Brand className="d-none d-md-block">Test Name</Navbar.Brand>
          <Navbar.Brand className="d-block d-md-none">
            <Image src="/logo.png" width={150} alt="logo" />
          </Navbar.Brand>

          {/* Center - Countdown Timer */}
          <div className="fw-bold fs-5 d-md-block d-none">
            Section Time: {formatTime(timeLeft)}
          </div>
          
          {/* Right - Buttons */}
          {/* For larger screens */}
          <div className="d-none d-sm-flex">
            <Button variant="outline-success" className="me-2">
              <FaQuestionCircle className="me-2" />
              Question Paper
            </Button>
            <Button
              variant="outline-success"
              className="me-2"
              onClick={handleShowModal}
            >
              <FaInfoCircle className="me-2" />
              Instruction
            </Button>
            {/* Full-Screen Button */}
            <Button variant="outline-success" onClick={toggleFullScreen}>
              {isFullScreen ? <FaCompress /> : <FaExpand />}{" "}
              {/* Show expand/compress icon */}
            </Button>
          </div>

          {/* For mobile view */}
          <div className="d-flex d-sm-none align-items-center gap-2">
            <div className="text-center fw-bold">{formatTime(timeLeft)}</div>
            <div className="text-center">
              <Button variant="outline-success" onClick={handleShowModal}>
                <FaInfoCircle />
              </Button>
            </div>
            <div className="text-center">
              <Button variant="outline-success">
                <FaQuestionCircle />
              </Button>
            </div>
            <div className="text-center">
              <Button variant="outline-success" onClick={toggleFullScreen}>
                {isFullScreen ? <FaCompress /> : <FaExpand />}
              </Button>
            </div>
          </div>
        </Container>
      </Navbar>
    </>
  );
};

export default TestNavbar;
