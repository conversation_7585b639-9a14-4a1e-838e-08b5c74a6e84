(function() {
  var scheme = "com.shashtrarth://";
  var userAgent = window.navigator.userAgent.toLowerCase();
  var isAndroid = userAgent.indexOf("android") > -1;
  var isMobile = /android|iphone|ipad|ipod/i.test(userAgent);

  if (!isMobile || !isAndroid) return;

  var didHide = false;
  var showPromptTimeout;

  // Detect if app opened
  document.addEventListener("visibilitychange", function () {
    if (document.hidden) {
      didHide = true;
      clearTimeout(showPromptTimeout);
    }
  });

  // Try to open the app
  window.location.href = scheme;

  // If app doesn't open, show prompt after 5 sec
  showPromptTimeout = setTimeout(function () {
    if (!didHide) showAppPrompt();
  }, 5000);

  function showAppPrompt() {
    // Create modal
    var modal = document.createElement("div");
    modal.innerHTML = `
      <div id="shashtrarth-app-modal" style="
        position: fixed;
        top: 0; left: 0; right: 0; bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex; align-items: center; justify-content: center;
        z-index: 9999;
      ">
        <div style="
          background: white;
          padding: 20px;
          border-radius: 12px;
          max-width: 300px;
          text-align: center;
          font-family: sans-serif;
          box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        ">
          <p style="margin-bottom: 20px;">Do you want to open the app?</p>
          <button id="open-app-btn" style="padding: 10px 15px; margin: 5px; background-color: #1976d2; color: white; border: none; border-radius: 6px;">Open App</button>
          <button id="continue-browser-btn" style="padding: 10px 15px; margin: 5px; background-color: #ccc; color: black; border: none; border-radius: 6px;">Continue in Browser</button>
        </div>
      </div>
    `;
    document.body.appendChild(modal);

    // Handle button clicks
    document.getElementById("open-app-btn").onclick = function () {
      window.location.href = scheme;
      closeModal();
    };
    document.getElementById("continue-browser-btn").onclick = closeModal;

    function closeModal() {
      var modalElement = document.getElementById("shashtrarth-app-modal");
      if (modalElement) modalElement.remove();
    }
  }
})();