// This script attempts to open the Shashtrarth app via deep link if installed, otherwise does nothing.
(function() {
  var scheme = "com.shashtrarth://"; // Custom scheme for the app
  var fallbackUrl = "https://play.google.com/store/apps/details?id=com.shashtrarth";
  var userAgent = window.navigator.userAgent.toLowerCase();
  var isAndroid = userAgent.indexOf("android") > -1;
  var isMobile = /android|iphone|ipad|ipod/i.test(userAgent);

  if (!isMobile) return; // Only run on mobile devices

  // Only run on Android for now
  if (isAndroid) {
    var now = Date.now();
    var timeout = setTimeout(function() {
      // If app not opened, redirect to Play Store
      window.location.href = fallbackUrl;
    }, 2000);
    // Try to open the app
    window.location.href = scheme;
    // If the app opens, the browser will pause JS and timeout won't fire
    window.onblur = function() {
      clearTimeout(timeout);
    };
  }
})();
