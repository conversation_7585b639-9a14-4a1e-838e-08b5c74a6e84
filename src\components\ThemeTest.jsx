import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Table, Badge, Accordion, Row, Col } from 'react-bootstrap';
import { useTheme } from '../context/ThemeContext';
import { BsSun, BsMoon, BsDisplay } from 'react-icons/bs';

const ThemeTest = () => {
  const { isDarkMode, themeMode, theme, setSystemTheme, setLightTheme, setDarkTheme } = useTheme();

  return (
    <Container className="my-5">
      <h1 className="mb-4">Theme Test Component</h1>
      <p className="lead">This component tests the theme implementation across various UI elements.</p>
      
      <Card className="p-4 mb-4">
        <Card.Body>
          <h2>Current Theme Settings</h2>
          <p>Current theme mode: <strong>{themeMode}</strong></p>
          <p>Is dark mode: <strong>{isDarkMode ? 'Yes' : 'No'}</strong></p>
          
          <div className="mb-3">
            <h4>Theme Controls:</h4>
            <Button 
              variant="outline-primary" 
              className="me-2 mb-2"
              onClick={setSystemTheme}
              active={themeMode === 'system'}
            >
              <BsDisplay className="me-2" />
              System Theme
            </Button>
            <Button 
              variant="outline-primary" 
              className="me-2 mb-2"
              onClick={setLightTheme}
              active={themeMode === 'light'}
            >
              <BsSun className="me-2" />
              Light Theme
            </Button>
            <Button 
              variant="outline-primary" 
              className="me-2 mb-2"
              onClick={setDarkTheme}
              active={themeMode === 'dark'}
            >
              <BsMoon className="me-2" />
              Dark Theme
            </Button>
          </div>
        </Card.Body>
      </Card>

      <h2 className="mt-5 mb-3">Alert Components</h2>
      <Alert variant="info">
        This is a test info alert to check theme styling.
      </Alert>

      <Alert variant="success">
        Success alert with theme styling.
      </Alert>

      <Alert variant="warning">
        Warning alert with theme styling.
      </Alert>

      <Alert variant="danger">
        Danger alert with theme styling.
      </Alert>

      <h2 className="mt-5 mb-3">Form Elements</h2>
      <Card className="mb-4">
        <Card.Body>
          <h3>Form Test</h3>
          <p>Card interiors should always remain light regardless of theme.</p>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Text Input</Form.Label>
              <Form.Control type="text" placeholder="Enter some text..." />
              <Form.Text>Helper text for the input field</Form.Text>
            </Form.Group>
            
            <Form.Group className="mb-3">
              <Form.Label>Textarea</Form.Label>
              <Form.Control as="textarea" rows={3} placeholder="Enter some text..." />
            </Form.Group>
            
            <Form.Group className="mb-3">
              <Form.Label>Select</Form.Label>
              <Form.Select>
                <option>Choose an option</option>
                <option value="1">Option 1</option>
                <option value="2">Option 2</option>
                <option value="3">Option 3</option>
              </Form.Select>
            </Form.Group>
            
            <Form.Group className="mb-3">
              <Form.Check type="checkbox" label="Check me out" />
            </Form.Group>
            
            <Button variant="primary" type="submit">
              Submit
            </Button>
          </Form>
        </Card.Body>
      </Card>

      <h2 className="mt-5 mb-3">Table Component</h2>
      <Table striped bordered hover>
        <thead>
          <tr>
            <th>#</th>
            <th>First Name</th>
            <th>Last Name</th>
            <th>Username</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>1</td>
            <td>Mark</td>
            <td>Otto</td>
            <td>@mdo</td>
          </tr>
          <tr>
            <td>2</td>
            <td>Jacob</td>
            <td>Thornton</td>
            <td>@fat</td>
          </tr>
          <tr>
            <td>3</td>
            <td>Larry</td>
            <td>Bird</td>
            <td>@twitter</td>
          </tr>
        </tbody>
      </Table>

      <h2 className="mt-5 mb-3">Accordion Component (FAQ Style)</h2>
      <Accordion defaultActiveKey="0" className="mb-4">
        <Accordion.Item eventKey="0" className="custom-accordion mb-3">
          <Accordion.Header>What is dark mode theming?</Accordion.Header>
          <Accordion.Body>
            Dark mode theming allows the interface to switch between light and dark color schemes
            for better user experience in different lighting conditions. This accordion demonstrates
            how FAQ sections adapt to the current theme.
          </Accordion.Body>
        </Accordion.Item>
        <Accordion.Item eventKey="1" className="custom-accordion mb-3">
          <Accordion.Header>How does the FAQ section work in dark mode?</Accordion.Header>
          <Accordion.Body>
            The FAQ section uses dynamic theming where the background, text colors, and borders
            automatically adjust based on the selected theme. All text remains clearly visible
            and the design maintains consistency with the overall application theme.
          </Accordion.Body>
        </Accordion.Item>
        <Accordion.Item eventKey="2" className="custom-accordion mb-3">
          <Accordion.Header>Are there smooth transitions between themes?</Accordion.Header>
          <Accordion.Body>
            Yes! All theme changes include smooth 0.3s ease transitions for a polished user experience.
            This includes background colors, text colors, borders, and hover effects.
          </Accordion.Body>
        </Accordion.Item>
      </Accordion>

      <h2 className="mt-5 mb-3">Button Variants</h2>
      <div className="mb-4">
        <Button variant="primary" className="me-2 mb-2">Primary</Button>
        <Button variant="secondary" className="me-2 mb-2">Secondary</Button>
        <Button variant="success" className="me-2 mb-2">Success</Button>
        <Button variant="danger" className="me-2 mb-2">Danger</Button>
        <Button variant="warning" className="me-2 mb-2">Warning</Button>
        <Button variant="info" className="me-2 mb-2">Info</Button>
        <Button variant="light" className="me-2 mb-2">Light</Button>
        <Button variant="dark" className="me-2 mb-2">Dark</Button>
        <Button variant="link" className="me-2 mb-2">Link</Button>
      </div>

      <h2 className="mt-5 mb-3">Outline Button Variants</h2>
      <div className="mb-4">
        <Button variant="outline-primary" className="me-2 mb-2">Primary</Button>
        <Button variant="outline-secondary" className="me-2 mb-2">Secondary</Button>
        <Button variant="outline-success" className="me-2 mb-2">Success</Button>
        <Button variant="outline-danger" className="me-2 mb-2">Danger</Button>
        <Button variant="outline-warning" className="me-2 mb-2">Warning</Button>
        <Button variant="outline-info" className="me-2 mb-2">Info</Button>
        <Button variant="outline-light" className="me-2 mb-2">Light</Button>
        <Button variant="outline-dark" className="me-2 mb-2">Dark</Button>
      </div>

      <h2 className="mt-5 mb-3">Theme Colors</h2>
      <Row className="mb-5">
        <Col md={4} className="mb-3">
          <div style={{ padding: '20px', backgroundColor: theme.colors.background, border: `1px solid ${theme.colors.border}`, borderRadius: '8px' }}>
            <h5>Background</h5>
            <code>{theme.colors.background}</code>
          </div>
        </Col>
        <Col md={4} className="mb-3">
          <div style={{ padding: '20px', backgroundColor: theme.colors.backgroundSecondary, border: `1px solid ${theme.colors.border}`, borderRadius: '8px' }}>
            <h5>Background Secondary</h5>
            <code>{theme.colors.backgroundSecondary}</code>
          </div>
        </Col>
        <Col md={4} className="mb-3">
          <div style={{ padding: '20px', backgroundColor: theme.colors.surface, border: `1px solid ${theme.colors.border}`, borderRadius: '8px' }}>
            <h5>Surface</h5>
            <code>{theme.colors.surface}</code>
          </div>
        </Col>
      </Row>

      <Row className="mb-5">
        <Col md={4} className="mb-3">
          <div style={{ padding: '20px', backgroundColor: theme.colors.cardBackground, border: `1px solid ${theme.colors.border}`, borderRadius: '8px' }}>
            <h5>Card Background (Always Light)</h5>
            <code>{theme.colors.cardBackground}</code>
          </div>
        </Col>
        <Col md={4} className="mb-3">
          <div style={{ padding: '20px', backgroundColor: theme.colors.sidebarBackground, border: `1px solid ${theme.colors.border}`, borderRadius: '8px' }}>
            <h5>Sidebar Background (Always Dark)</h5>
            <code>{theme.colors.sidebarBackground}</code>
          </div>
        </Col>
        <Col md={4} className="mb-3">
          <div style={{ padding: '20px', backgroundColor: theme.colors.primary, color: 'white', border: `1px solid ${theme.colors.border}`, borderRadius: '8px' }}>
            <h5>Primary</h5>
            <code>{theme.colors.primary}</code>
          </div>
        </Col>
      </Row>

      <h2 className="mt-5 mb-3">FAQ Section Testing</h2>
      <Card className="mb-4">
        <Card.Body>
          <h3>Dashboard FAQ Section</h3>
          <p>The FAQ section in the dashboard now supports full dark mode theming:</p>
          <ul>
            <li>✅ <strong>Container Background:</strong> Adapts to theme background</li>
            <li>✅ <strong>Accordion Cards:</strong> Use theme-responsive card colors</li>
            <li>✅ <strong>Text Visibility:</strong> All text is clearly visible in both modes</li>
            <li>✅ <strong>Hover Effects:</strong> Smooth transitions and proper contrast</li>
            <li>✅ <strong>Border Styling:</strong> Consistent with overall theme</li>
          </ul>
          <p className="mt-3">
            <strong>Test Instructions:</strong> Visit the dashboard and navigate to the FAQ section
            to see the accordion components properly themed for both light and dark modes.
          </p>
        </Card.Body>
      </Card>

      <h2 className="mt-5 mb-5">Implementation Notes</h2>
      <Card className="mb-5">
        <Card.Body>
          <h3>Special Requirements</h3>
          <ul>
            <li><strong>Card Interiors:</strong> Now support dark mode theming with proper text visibility</li>
            <li><strong>Card Borders:</strong> All cards have 1px white borders in dark mode for better definition</li>
            <li><strong>Sidebar:</strong> Always remains dark regardless of theme</li>
            <li><strong>FAQ Sections:</strong> Fully themed accordions with smooth transitions (no white borders)</li>
            <li><strong>Theme Toggle:</strong> Available in both main navbar and dashboard navbar</li>
            <li><strong>System Theme Detection:</strong> Automatically follows system preference when in System mode</li>
            <li><strong>Theme Persistence:</strong> Theme preference is saved in localStorage</li>
          </ul>
        </Card.Body>
      </Card>
    </Container>
  );
};

export default ThemeTest;
