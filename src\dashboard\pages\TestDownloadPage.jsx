import React from 'react';
import { Contain<PERSON>, <PERSON>, <PERSON>, Card, Button, Alert } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { FaDownload, FaFilePdf, FaArrowRight, FaInfoCircle } from 'react-icons/fa';
import { useTheme } from '../../context/ThemeContext';

const TestDownloadPage = () => {
  const { isDarkMode, theme } = useTheme();

  return (
    <Container className="py-5">
      <Row className="justify-content-center">
        <Col lg={10}>
          <div className="text-center mb-5">
            <h1 style={{ color: theme.colors.text }}>
              Offline Test Download Center
            </h1>
            <p className="lead" style={{ color: theme.colors.textSecondary }}>
              Download practice tests in PDF format for offline preparation
            </p>
          </div>

          <Row className="g-4">
            {/* Download Test Card */}
            <Col md={6}>
              <Card 
                className="h-100 shadow-sm"
                style={{
                  backgroundColor: theme.colors.cardBackground,
                  borderColor: theme.colors.cardBorder,
                  color: theme.colors.cardText
                }}
              >
                <Card.Body className="text-center">
                  <div className="mb-3">
                    <FaFilePdf size={48} className="text-danger" />
                  </div>
                  <Card.Title style={{ color: theme.colors.cardText }}>
                    SSC CGL Mock Test 2024
                  </Card.Title>
                  <Card.Text style={{ color: theme.colors.cardText }}>
                    Complete offline test with 25 questions covering all sections.
                    Includes OMR-style answer sheet and answer key.
                  </Card.Text>
                  <ul className="list-unstyled text-start mb-4">
                    <li>• 25 Multiple Choice Questions</li>
                    <li>• 4 Sections: Reasoning, GK, Quantitative, English</li>
                    <li>• OMR Format Answer Sheet</li>
                    <li>• Complete Answer Key</li>
                    <li>• 60 Minutes Duration</li>
                  </ul>
                  <Link to="/download-test">
                    <Button variant="success" size="lg" className="w-100">
                      <FaDownload className="me-2" />
                      Download Test PDF
                      <FaArrowRight className="ms-2" />
                    </Button>
                  </Link>
                </Card.Body>
              </Card>
            </Col>

            {/* Instructions Card */}
            <Col md={6}>
              <Card 
                className="h-100 shadow-sm"
                style={{
                  backgroundColor: theme.colors.cardBackground,
                  borderColor: theme.colors.cardBorder,
                  color: theme.colors.cardText
                }}
              >
                <Card.Body>
                  <Card.Title style={{ color: theme.colors.cardText }}>
                    <FaInfoCircle className="me-2 text-info" />
                    How to Use Offline Tests
                  </Card.Title>
                  <div className="mb-4">
                    <h6 style={{ color: theme.colors.cardText }}>Step 1: Download</h6>
                    <p className="small" style={{ color: theme.colors.cardText }}>
                      Click the download button to get the PDF test file.
                    </p>
                    
                    <h6 style={{ color: theme.colors.cardText }}>Step 2: Print</h6>
                    <p className="small" style={{ color: theme.colors.cardText }}>
                      Print the PDF on A4 size paper for best results.
                    </p>
                    
                    <h6 style={{ color: theme.colors.cardText }}>Step 3: Attempt</h6>
                    <p className="small" style={{ color: theme.colors.cardText }}>
                      Fill the OMR circles completely with a black/blue pen.
                    </p>
                    
                    <h6 style={{ color: theme.colors.cardText }}>Step 4: Check</h6>
                    <p className="small" style={{ color: theme.colors.cardText }}>
                      Use the answer key on the last page to evaluate your performance.
                    </p>
                  </div>
                  
                  <Alert variant="warning" className="small">
                    <strong>Note:</strong> This is a demo implementation. In production, 
                    you would integrate with your actual test database and user authentication.
                  </Alert>
                </Card.Body>
              </Card>
            </Col>
          </Row>

          {/* Features Section */}
          <Row className="mt-5">
            <Col>
              <Card 
                style={{
                  backgroundColor: theme.colors.cardBackground,
                  borderColor: theme.colors.cardBorder,
                  color: theme.colors.cardText
                }}
              >
                <Card.Body>
                  <h4 className="text-center mb-4" style={{ color: theme.colors.cardText }}>
                    Features of Offline Test PDF
                  </h4>
                  <Row>
                    <Col md={3} className="text-center mb-3">
                      <div className="mb-2">
                        <div 
                          className="rounded-circle d-inline-flex align-items-center justify-content-center"
                          style={{
                            width: '60px',
                            height: '60px',
                            backgroundColor: theme.colors.primary,
                            color: 'white'
                          }}
                        >
                          <FaFilePdf size={24} />
                        </div>
                      </div>
                      <h6 style={{ color: theme.colors.cardText }}>PDF Format</h6>
                      <small style={{ color: theme.colors.cardText }}>
                        High-quality PDF that prints perfectly on any printer
                      </small>
                    </Col>
                    <Col md={3} className="text-center mb-3">
                      <div className="mb-2">
                        <div 
                          className="rounded-circle d-inline-flex align-items-center justify-content-center"
                          style={{
                            width: '60px',
                            height: '60px',
                            backgroundColor: theme.colors.success,
                            color: 'white'
                          }}
                        >
                          ⭕
                        </div>
                      </div>
                      <h6 style={{ color: theme.colors.cardText }}>OMR Style</h6>
                      <small style={{ color: theme.colors.cardText }}>
                        Authentic OMR format with circles to fill
                      </small>
                    </Col>
                    <Col md={3} className="text-center mb-3">
                      <div className="mb-2">
                        <div 
                          className="rounded-circle d-inline-flex align-items-center justify-content-center"
                          style={{
                            width: '60px',
                            height: '60px',
                            backgroundColor: theme.colors.info,
                            color: 'white'
                          }}
                        >
                          🔑
                        </div>
                      </div>
                      <h6 style={{ color: theme.colors.cardText }}>Answer Key</h6>
                      <small style={{ color: theme.colors.cardText }}>
                        Complete answer key included on the last page
                      </small>
                    </Col>
                    <Col md={3} className="text-center mb-3">
                      <div className="mb-2">
                        <div 
                          className="rounded-circle d-inline-flex align-items-center justify-content-center"
                          style={{
                            width: '60px',
                            height: '60px',
                            backgroundColor: theme.colors.warning,
                            color: 'white'
                          }}
                        >
                          📝
                        </div>
                      </div>
                      <h6 style={{ color: theme.colors.cardText }}>Practice Ready</h6>
                      <small style={{ color: theme.colors.cardText }}>
                        Ready-to-use format for immediate practice
                      </small>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>
            </Col>
          </Row>

          {/* Navigation */}
          <div className="text-center mt-5">
            <Link to="/dashboard" className="btn btn-outline-primary me-3">
              ← Back to Dashboard
            </Link>
            <Link to="/download-test" className="btn btn-success">
              Go to Download Page →
            </Link>
          </div>
        </Col>
      </Row>
    </Container>
  );
};

export default TestDownloadPage;
