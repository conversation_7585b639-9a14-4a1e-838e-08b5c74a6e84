import React, { useState, useEffect, useRef } from 'react';
import { Card, Row, Col } from 'react-bootstrap';
import { motion, useAnimation } from 'framer-motion';
import './ProgressMeter.css';

// Performance levels configuration (BMI-style meter)
// Color progression: Grey (lowest) → Yellow → Orange → Green (highest)
const levels = [
  {
    name: 'EXPERT',
    color: '#28a745', // Green (highest level)
    range: '90 - 100',
    minScore: 90,
    maxScore: 100,
    description: 'Ready for competitive exams'
  },
  {
    name: 'ADVANCED',
    color: '#fd7e14', // Orange
    range: '75 - 89.9',
    minScore: 75,
    maxScore: 89.9,
    description: 'Strong preparation level'
  },
  {
    name: 'INTERMEDIATE',
    color: '#ffc107', // Yellow
    range: '60 - 74.9',
    minScore: 60,
    maxScore: 74.9,
    description: 'Good foundation built'
  },
  {
    name: 'BEGINNER',
    color: '#ffd700', // Lighter Yellow (distinct from intermediate)
    range: '40 - 59.9',
    minScore: 40,
    maxScore: 59.9,
    description: 'Learning in progress'
  },
  {
    name: 'STARTER',
    color: '#0099ff', // Blue (lowest level)
    range: '0 - 39.9',
    minScore: 0,
    maxScore: 39.9,
    description: 'Just getting started'
  },
];

// Dummy user progress data
const userProgress = {
  currentLevel: 'ADVANCED', // Updated to match 76.5 score
  progressPercent: 76,
  nextLevel: 'EXPERT',
  neededPercent: 14, // Need 90 - 76.5 = 13.5% more to reach Expert
  studyHoursThisWeek: 12,
  averagePerDay: 1.7,
  currentScore: 76.5 // User's current performance score
};

const ProgressMeter = () => {
  const [animationStarted, setAnimationStarted] = useState(false);
  const meterFillControls = useAnimation();
  const pointerControls = useAnimation();
  const pulseControls = useAnimation();

  // Get current level based on score
  const getCurrentLevel = () => {
    return levels.find(level =>
      userProgress.currentScore >= level.minScore &&
      userProgress.currentScore <= level.maxScore
    ) || levels[levels.length - 1];
  };

  const currentLevel = getCurrentLevel();

  // Calculate meter fill percentage (0-1)
  const getMeterFillPercentage = () => {
    return userProgress.currentScore / 100;
  };

  // Start meter animation
  const startMeterAnimation = async () => {
    setAnimationStarted(true);

    // Check if mobile screen
    const isMobile = window.innerWidth <= 768;
    const meterHeight = isMobile ? 280 : 300;
    // Since top: 0, we need to position relative to meter top
    // Bottom of meter = meterHeight, Top of meter = 0
    const bottomPosition = meterHeight;
    const topPosition = 0;

    // Reset animations
    await meterFillControls.set({ height: '0%' });
    await pointerControls.set({ y: bottomPosition });
    await pulseControls.set({ scale: 1 });

    // Animation sequence: 100% → 0% → 100% → 0% → Actual Value
    // First: Animate to 100%
    await Promise.all([
      meterFillControls.start({
        height: '100%',
        transition: { duration: 1, ease: 'easeInOut' }
      }),
      pointerControls.start({
        y: topPosition,
        transition: { duration: 1, ease: 'easeInOut' }
      })
    ]);

    // Reset to 0
    await Promise.all([
      meterFillControls.start({
        height: '0%',
        transition: { duration: 0.3, ease: 'easeInOut' }
      }),
      pointerControls.start({
        y: bottomPosition,
        transition: { duration: 0.3, ease: 'easeInOut' }
      })
    ]);

    // Second: Animate to 100% again
    await Promise.all([
      meterFillControls.start({
        height: '100%',
        transition: { duration: 1, ease: 'easeInOut' }
      }),
      pointerControls.start({
        y: topPosition,
        transition: { duration: 1, ease: 'easeInOut' }
      })
    ]);

    // Reset to 0
    await Promise.all([
      meterFillControls.start({
        height: '0%',
        transition: { duration: 0.3, ease: 'easeInOut' }
      }),
      pointerControls.start({
        y: bottomPosition,
        transition: { duration: 0.3, ease: 'easeInOut' }
      })
    ]);

    // Third: Animate to actual value
    // For 76.5% score: meterHeight - (76.5/100) * meterHeight = position from top
    const actualPosition = meterHeight - (userProgress.currentScore / 100) * meterHeight;
    console.log('Pointer position for', userProgress.currentScore + '%:', actualPosition, 'meterHeight:', meterHeight);
    await Promise.all([
      meterFillControls.start({
        height: `${getMeterFillPercentage() * 100}%`,
        transition: { duration: 1.5, ease: 'easeInOut' }
      }),
      pointerControls.start({
        y: actualPosition,
        transition: { duration: 1.5, ease: 'easeInOut' }
      })
    ]);

    // Finally: Pulse animation
    pulseControls.start({
      scale: [1, 1.2, 1, 1.2, 1, 1.2, 1],
      transition: { duration: 1.8, ease: 'easeInOut' }
    });
  };

  // Start animation on component mount
  useEffect(() => {
    const timer = setTimeout(() => {
      startMeterAnimation();
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div>
      {/* Progress Meter Section */}
      <Card className="shadow-sm border-0 mb-4">
        <Card.Body>
          <h4 className="text-success mb-4">Your Performance Level</h4>

          <Row>
            <Col lg={6} className="d-flex justify-content-center">
              <div
                className="progress-meter-container"
                onClick={startMeterAnimation}
                style={{ cursor: 'pointer' }}
              >
                {/* Meter Background */}
                <div className="meter-background">
                  {/* Level Sections */}
                  {levels.map((level, index) => {
                    const sectionHeight = (level.maxScore - level.minScore) / 100 * 300;
                    return (
                      <div
                        key={level.name}
                        className="meter-section"
                        style={{
                          height: `${sectionHeight}px`,
                          backgroundColor: level.color,
                        }}
                      >
                        <div className="level-labels">
                          <span className="level-name">{level.name}</span>
                          <span className="level-range">{level.range}</span>
                        </div>
                      </div>
                    );
                  })}

                  {/* Animated Fill */}
                  <motion.div
                    className="meter-fill"
                    animate={meterFillControls}
                    initial={{ height: '0%' }}
                  />
                </div>

                {/* Current Level Pointer */}
                <motion.div
                  className="pointer-container"
                  animate={pointerControls}
                  initial={{ y: window.innerWidth <= 768 ? 280 : 300 }}
                >
                  <motion.div
                    className="pointer-wrapper"
                    animate={pulseControls}
                    initial={{ scale: 1 }}
                  >
                    <div
                      className="pointer"
                      style={{ borderLeftColor: currentLevel.color }}
                    />
                    <div
                      className="pointer-dot"
                      style={{ backgroundColor: currentLevel.color }}
                    />
                  </motion.div>
                </motion.div>

                {/* Score Display */}
                <div className="score-display">
                  <motion.div
                    className="current-score"
                    style={{ color: currentLevel.color }}
                    animate={pulseControls}
                    initial={{ scale: 1 }}
                  >
                    {userProgress.currentScore}
                  </motion.div>
                  <div
                    className="score-label"
                    style={{ color: currentLevel.color }}
                  >
                    PERFORMANCE SCORE
                  </div>
                </div>
              </div>
            </Col>

            <Col lg={6}>
              {/* Current Level Info */}
              <div className="current-level-info">
                <div
                  className="level-indicator"
                  style={{ backgroundColor: currentLevel.color }}
                />
                <div className="level-details">
                  <h5
                    className="current-level-name"
                    style={{ color: currentLevel.color }}
                  >
                    {currentLevel.name}
                  </h5>
                  <p className="current-level-desc">
                    {currentLevel.description}
                  </p>
                  <p className="next-level-text">
                    {userProgress.nextLevel &&
                      `${userProgress.neededPercent}% more to reach ${userProgress.nextLevel}`
                    }
                  </p>
                </div>
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Study Statistics Section */}
      <Card className="shadow-sm border-0">
        <Card.Body>
          <h4 className="text-success mb-4">Study Statistics</h4>

          <Row>
            <Col xs={6}>
              <div className="stat-item text-center">
                <div className="stat-value text-success">
                  {userProgress.studyHoursThisWeek} hrs
                </div>
                <div className="stat-label">This Week</div>
              </div>
            </Col>
            <Col xs={6}>
              <div className="stat-item text-center">
                <div className="stat-value text-primary">
                  {userProgress.averagePerDay} hrs
                </div>
                <div className="stat-label">Daily Average</div>
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>
    </div>
  );
};

export default ProgressMeter;
