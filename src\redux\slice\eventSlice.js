import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the access token from Redux state
const getAuthToken = (getState) => {
  const state = getState();
  return state.student?.student?.JWT_Token?.access || null;
};

// Subscribe to Events Thunk
export const subscribeEvents = createAsyncThunk(
  'events/subscribeEvents',
  async ({ username, password }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SUBSCRIBE_EVENTS}`,
        {
          username,
          password
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Subscribe events error:', error);
      return rejectWithValue(error.response?.data || 'Error subscribing to events');
    }
  }
);

// See Subscribed Events Thunk
export const seeSubscribedEvents = createAsyncThunk(
  'events/seeSubscribedEvents',
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SEE_SUBSCRIBE_EVENTS}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('See subscribed events error:', error);
      return rejectWithValue(error.response?.data || 'Error fetching subscribed events');
    }
  }
);

// Unsubscribe Events Thunk
export const unsubscribeEvents = createAsyncThunk(
  'events/unsubscribeEvents',
  async ({ eventId }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_UNSUBSCRIBE_EVENTS}`,
        {
          event_id: eventId
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Unsubscribe events error:', error);
      return rejectWithValue(error.response?.data || 'Error unsubscribing from events');
    }
  }
);

// Event Slice
const eventSlice = createSlice({
  name: 'events',
  initialState: {
    subscribedEvents: [],
    subscriptionStatus: null,
    loading: false,
    subscribeLoading: false,
    unsubscribeLoading: false,
    error: null,
    subscribeError: null,
    unsubscribeError: null,
  },
  reducers: {
    clearErrors: (state) => {
      state.error = null;
      state.subscribeError = null;
      state.unsubscribeError = null;
    },
    clearSubscriptionStatus: (state) => {
      state.subscriptionStatus = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle subscribeEvents actions
      .addCase(subscribeEvents.pending, (state) => {
        state.subscribeLoading = true;
        state.subscribeError = null;
      })
      .addCase(subscribeEvents.fulfilled, (state, action) => {
        state.subscribeLoading = false;
        state.subscriptionStatus = action.payload;
      })
      .addCase(subscribeEvents.rejected, (state, action) => {
        state.subscribeLoading = false;
        state.subscribeError = action.payload;
      })

      // Handle seeSubscribedEvents actions
      .addCase(seeSubscribedEvents.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(seeSubscribedEvents.fulfilled, (state, action) => {
        state.loading = false;
        state.subscribedEvents = action.payload;
      })
      .addCase(seeSubscribedEvents.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle unsubscribeEvents actions
      .addCase(unsubscribeEvents.pending, (state) => {
        state.unsubscribeLoading = true;
        state.unsubscribeError = null;
      })
      .addCase(unsubscribeEvents.fulfilled, (state, action) => {
        state.unsubscribeLoading = false;
        // Remove the unsubscribed event from the list
        const eventId = action.meta.arg.eventId;
        state.subscribedEvents = state.subscribedEvents.filter(
          event => event.id !== eventId
        );
      })
      .addCase(unsubscribeEvents.rejected, (state, action) => {
        state.unsubscribeLoading = false;
        state.unsubscribeError = action.payload;
      });
  },
});

export const { clearErrors, clearSubscriptionStatus } = eventSlice.actions;
export default eventSlice.reducer;
