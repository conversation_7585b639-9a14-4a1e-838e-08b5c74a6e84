import React from "react";
import { Col, Dropdown, Row } from "react-bootstrap";

const QuestionHeader = ({
  timer,
  language,
  setLanguage,
  currentQuestionIndex,
  sectionMarks,
}) => {
  return (
    <>
      <Row className="mb-3 px-md-0 px-2 d-flex justify-content-md-between justify-content-center align-items-center shadow-sm border-bottom border-2">
        <Col md="auto" xs={3}>
          <h5 className="fw-semibold d-md-block d-none">
            Question No {currentQuestionIndex + 1}.
          </h5>
          <h5 className="fw-semibold d-block d-md-none">
            {currentQuestionIndex + 1}.
          </h5>
        </Col>
        <Col
          md="auto"
          xs={9}
          className="d-flex justify-content-end align-items-center"
        >
          <div className="text-center me-3">
            <small className="mb-1 small">Marks</small>
            <p>
              <span className="bg-success small text-white px-2 py-1 rounded-5 me-2">
                +{sectionMarks?.rightMarks}
              </span>
              <span className="bg-danger small text-white px-2 py-1 rounded-5">
                {sectionMarks?.negativeMarks}
              </span>
            </p>
          </div>

          <div className="text-center me-3">
            <small className="mb-1">Time</small>
            <p className="mb-0 small">
              {String(Math.floor(timer / 60)).padStart(2, "0")}:
              {String(timer % 60).padStart(2, "0")}
            </p>
          </div>

          <Dropdown>
            <span className="d-none d-md-block">view in</span>
            <Dropdown.Toggle variant="secondary" size="sm">
              {language}
            </Dropdown.Toggle>
            <Dropdown.Menu>
              <Dropdown.Item onClick={() => setLanguage("English")}>
                English
              </Dropdown.Item>
              <Dropdown.Item onClick={() => setLanguage("Hindi")}>
                Hindi
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </Col>
      </Row>
    </>
  );
};

export default QuestionHeader;
