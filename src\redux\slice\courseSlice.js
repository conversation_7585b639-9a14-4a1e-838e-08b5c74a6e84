import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';


// Get Courses Thunk
export const getCourses = createAsyncThunk(
  'course/getCourses',
  async (_, { getState, rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_COURSE}`,       
      );
      return response.data; // Handle the courses data at the component level
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching courses');
    }
  }
);

// Get Single Course Thunk
export const getCourse = createAsyncThunk(
  'course/getCourse',
  async (courseSlug, { getState, rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_COURSE}${courseSlug}/`,
        
      );
      return response.data; // Handle course data at the component level
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching course');
    }
  }
);


const courseSlice = createSlice({
  name: 'course',
  initialState: {
    isLoading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getCourses.fulfilled, (state, action) => {
        state.isLoading = false;
        state.error = null; // Handle courses data at the component level
      })
      .addCase(getCourses.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(getCourse.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getCourse.fulfilled, (state, action) => {
        state.isLoading = false;
        state.error = null; 
      })
      .addCase(getCourse.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
  },
});

export default courseSlice.reducer;
