import React from 'react';
import { Container, <PERSON>, <PERSON>, Button } from 'react-bootstrap';
import { FaUniversity, FaUserTie, FaLaptop, FaGraduationCap, FaBook, FaChalkboardTeacher } from 'react-icons/fa';
import { FaChevronRight } from 'react-icons/fa'; // Importing ChevronRight icon

const StudentInterestedExams = () => {
  const exams = [
    {
      icon: <FaUniversity className="text-success" size={24} />,
      title: "NIACL Assistant"
    },
    {
      icon: <FaUserTie className="text-success" size={24} />,
      title: "RRB Office Assistant"
    },
    {
      icon: <FaLaptop className="text-success" size={24} />,
      title: "SBI PO"
    },
    {
      icon: <FaGraduationCap className="text-success" size={24} />,
      title: "RRB Officer Scale I"
    },
    {
      icon: <FaBook className="text-success" size={24} />,
      title: "S<PERSON> Clerk"
    },
    {
      icon: <FaChalkboardTeacher className="text-success" size={24} />,
      title: "SBI CBO"
    }
  ];

  return (
    <Container className="mt-5">
      <Row className="justify-content-center">
        <Col md={10}>
          <div className="d-flex justify-content-between align-items-center mb-3">
            <div className='d-flex gap-2 align-items-center'>
            <h5>Your Exams</h5>
              <Button variant="success" size="sm">
                + Add More Exams
              </Button>
            </div>
            <div>
              <Button variant="info" size="sm" className='rounded-5 text-white bg-opacity-50'>
                View All
              </Button>
            </div>
          </div>

          <div className="d-flex flex-wrap">
            {exams.map((exam, index) => (
              <Button 
                key={index} 
                variant=''
                className="border px-4 py-3 m-2 d-flex justify-content-between align-items-center gap-3 rounded shadow-sm bg-white" 
                style={{ minWidth: '120px', height: 'auto'}}
              >
                <div className="d-flex justify-content-center align-items-center">
                  {exam.icon}
                </div>
                <p className="m-0 mt-2 text-center">{exam.title}</p>
                <div>
                  <FaChevronRight className="text-success" />
                </div>
              </Button>
            ))}
          </div>
        </Col>
      </Row>
    </Container>
  );
};

export default StudentInterestedExams;
