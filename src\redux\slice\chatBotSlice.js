import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper to get token
const getAuthToken = (getState) => {
  const state = getState();
  return state?.student?.student?.JWT_Token?.access;
};

// POST chat message
export const sendChatMessage = createAsyncThunk(
  "chatBot/sendMessage",
  async (messageData, { getState, rejectWithValue }) => {
    try {
      const accessToken = getAuthToken(getState);
      if (!accessToken) {
        throw new Error("No access token found");
      }
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CHAT_SEND}`,
        messageData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message || "Failed to send message");
    }
  }
);

// GET chat history
export const getChatHistory = createAsyncThunk(
  "chatBot/getHistory",
  async (_, { getState, rejectWithValue }) => {
    try {
      const accessToken = getAuthToken(getState);
      if (!accessToken) {
        throw new Error("No access token found");
      }
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CHAT_HISTORY}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message || "Failed to fetch chat history");
    }
  }
);

// Slice
const chatBotSlice = createSlice({
  name: "chatBot",
  initialState: {
    loading: false,
    error: null,
    messages: [],
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Send Message
      .addCase(sendChatMessage.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(sendChatMessage.fulfilled, (state, action) => {
        state.loading = false;
        state.messages.push(action.payload); // Add the new message to the state
      })
      .addCase(sendChatMessage.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get Chat History
      .addCase(getChatHistory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getChatHistory.fulfilled, (state, action) => {
        state.loading = false;
        state.messages = action.payload; // Set the fetched chat history
      })
      .addCase(getChatHistory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default chatBotSlice.reducer;
