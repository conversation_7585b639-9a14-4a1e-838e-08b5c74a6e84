import React from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> } from "react-bootstrap";
import { FaGooglePlay } from "react-icons/fa";

const PlayStoreDownload = () => {
  return (
    <Card className="my-4 shadow-sm border-0" style={{ background: "#f8fff8", borderRadius: 16 }}>
      <Row className="align-items-center g-0 p-3">
        <Col xs={12} md={8} className="text-center text-md-start mb-2 mb-md-0">
          <h5 className="fw-bold mb-1">Download our app from Play Store</h5>
          <p className="mb-2" style={{ fontSize: "1.1rem" }}>
            Get the <span className="text-success fw-semibold">Shashtrarth</span> app for the best experience!
          </p>
          <small className="text-muted">Developed by Pinak Venture</small>
        </Col>
        <Col xs={12} md={4} className="text-center">
          <Button
            variant="success"
            href="https://play.google.com/store/apps/details?id=com.shashtrarth"
            target="_blank"
            rel="noopener noreferrer"
            className="d-flex align-items-center justify-content-center gap-2 px-4 py-2"
            style={{ fontSize: "1.1rem", borderRadius: 8 }}
          >
            <FaGooglePlay size={22} />
            <span>Get it on Play Store</span>
          </Button>
        </Col>
      </Row>
    </Card>
  );
};

export default PlayStoreDownload;
