import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Container, Image, Row, Modal } from 'react-bootstrap';
import { FaGetPocket } from "react-icons/fa";
import { getSinglePackage } from '../../redux/slice/packageSlice';
import Checkout from '../pages/Checkout'; // Import the Checkout component

const SelectedPackage = () => {
    const dispatch = useDispatch();
    const [singlePackage, setSinglePackage] = useState(null);
    const [showModal, setShowModal] = useState(false);
    const [checkoutDetails, setCheckoutDetails] = useState(null);

    const accessToken = useSelector(state => state?.student?.student?.JWT_Token?.access);
    const selectedPackageId = useSelector(state => state?.packages?.selectedPackageId);
    const isLoggedIn = !!accessToken;

    useEffect(() => {
        const fetchPackage = async () => {
            if (isLoggedIn && selectedPackageId) {
                try {
                    const response = await dispatch(getSinglePackage(selectedPackageId)).unwrap();
                    setSinglePackage(response);
                    setShowModal(true);
                } catch (err) {
                    console.error("Failed to load selected package", err);
                }
            }
        };

        fetchPackage();
    }, [dispatch, isLoggedIn, selectedPackageId]);

    const handleBuyNow = () => {
        setCheckoutDetails({
            packageName: singlePackage?.name,
            originalPrice: singlePackage?.discount_price,
            packageId: selectedPackageId,
            descriptions: [
                singlePackage?.description_line_01,
                singlePackage?.description_line_02,
                singlePackage?.description_line_03,
                singlePackage?.description_line_04,
                singlePackage?.description_line_05,
            ].filter(Boolean),
        });
    };

    if (!selectedPackageId || !singlePackage) return null;

    return (
        <>
            {!checkoutDetails ? (
                <Modal
                    show={showModal}
                    onHide={() => setShowModal(false)}
                    size="xl"
                    centered
                >
                    <Modal.Header closeButton>
                        <Modal.Title>Continue buying your package</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Container id="packages">
                            <Row className="justify-content-center">
                                <Col md={12}>
                                    <Row className="justify-content-evenly align-items-center g-md-5">
                                        <Col md={5}>
                                            <Image src="/package.svg" fluid />
                                        </Col>
                                        <Col md={7}>
                                            <h3 className="my-2 fs-1 fw-bold text-success">
                                                {singlePackage?.name}
                                            </h3>
                                            <h6 className="mt-1">₹ {singlePackage?.discount_price}</h6>
                                            <h5 className="my-3 fw-semibold">What you get on packages</h5>
                                            <Row>
                                                {[
                                                    singlePackage?.description_line_01,
                                                    singlePackage?.description_line_02,
                                                    singlePackage?.description_line_03,
                                                    singlePackage?.description_line_04,
                                                    singlePackage?.description_line_05,
                                                ]
                                                    .filter(Boolean)
                                                    .map((desc, index) => (
                                                        <Col xs={6} key={index} className="text-center">
                                                            <FaGetPocket className="mb-2 text-success" />
                                                            <p className="text-success">{desc}</p>
                                                        </Col>
                                                    ))}
                                            </Row>
                                            <div className="d-flex justify-content-start mt-2">
                                                <button
                                                    className="btn btn-success"
                                                    onClick={handleBuyNow}
                                                >
                                                    Buy Now!
                                                </button>
                                            </div>
                                        </Col>
                                    </Row>
                                </Col>
                            </Row>
                        </Container>
                    </Modal.Body>
                </Modal>
            ) : (
                <Checkout checkoutDetails={checkoutDetails} setCheckoutDetails={setCheckoutDetails} />
            )}
        </>
    );
};

export default SelectedPackage;
