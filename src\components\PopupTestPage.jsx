import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Container, Row, Col, Card, Button, Alert } from 'react-bootstrap';
import { resetShownPopups, selectPopupForPage, clearCurrentPopup } from '../redux/slice/viewPopupSlice';
import { useLocation } from 'react-router-dom';

const PopupTestPage = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const { popups, currentPopup, shownPopups, isLoading, error } = useSelector((state) => state.viewPopup);

  const handleResetShownPopups = () => {
    dispatch(resetShownPopups());
  };

  const handleTriggerPopup = () => {
    dispatch(selectPopupForPage({ currentPath: location.pathname }));
  };

  const handleClearCurrentPopup = () => {
    dispatch(clearCurrentPopup());
  };

  return (
    <Container className="py-4">
      <Row>
        <Col>
          <h2>Popup System Test Page</h2>
          <p>Current Path: <code>{location.pathname}</code></p>
          
          {error && (
            <Alert variant="danger">
              <strong>Error:</strong> {error}
            </Alert>
          )}

          <Card className="mb-4">
            <Card.Header>
              <h5>Popup Controls</h5>
            </Card.Header>
            <Card.Body>
              <div className="d-flex gap-2 mb-3">
                <Button 
                  variant="primary" 
                  onClick={handleTriggerPopup}
                  disabled={isLoading}
                >
                  {isLoading ? 'Loading...' : 'Trigger Popup for Current Page'}
                </Button>
                <Button 
                  variant="warning" 
                  onClick={handleResetShownPopups}
                >
                  Reset Shown Popups
                </Button>
                <Button 
                  variant="secondary" 
                  onClick={handleClearCurrentPopup}
                >
                  Clear Current Popup
                </Button>
              </div>
              
              <div className="mb-3">
                <strong>Shown Popups:</strong> {shownPopups.length > 0 ? shownPopups.join(', ') : 'None'}
              </div>
              
              {currentPopup && (
                <Alert variant="info">
                  <strong>Current Popup:</strong> {currentPopup.title} (ID: {currentPopup.id})
                  <br />
                  <strong>Type:</strong> {currentPopup.content_type}
                  <br />
                  <strong>Target:</strong> {currentPopup.page_target || 'All pages'}
                  <br />
                  <strong>Delay:</strong> {currentPopup.delay_ms}ms
                  <br />
                  <strong>Duration:</strong> {currentPopup.display_duration}ms
                </Alert>
              )}
            </Card.Body>
          </Card>

          <Card>
            <Card.Header>
              <h5>Available Popups ({popups.length})</h5>
            </Card.Header>
            <Card.Body>
              {popups.length === 0 ? (
                <p>No popups loaded. Click "Trigger Popup" to fetch popups.</p>
              ) : (
                <Row>
                  {popups.map((popup) => (
                    <Col md={6} lg={4} key={popup.id} className="mb-3">
                      <Card className="h-100">
                        <Card.Body>
                          <Card.Title className="h6">{popup.title}</Card.Title>
                          <Card.Text className="small">
                            <strong>Type:</strong> {popup.content_type}<br />
                            <strong>Target:</strong> {popup.page_target || 'All pages'}<br />
                            <strong>Delay:</strong> {popup.delay_ms}ms<br />
                            <strong>Duration:</strong> {popup.display_duration}ms<br />
                            <strong>Priority:</strong> {popup.priority}
                          </Card.Text>
                          {popup.text_content && (
                            <Card.Text className="small text-muted">
                              {popup.text_content.substring(0, 100)}
                              {popup.text_content.length > 100 ? '...' : ''}
                            </Card.Text>
                          )}
                          {shownPopups.includes(popup.id) && (
                            <small className="text-success">✓ Already shown</small>
                          )}
                        </Card.Body>
                      </Card>
                    </Col>
                  ))}
                </Row>
              )}
            </Card.Body>
          </Card>

          <Card className="mt-4">
            <Card.Header>
              <h5>Instructions</h5>
            </Card.Header>
            <Card.Body>
              <ol>
                <li>Click "Trigger Popup for Current Page" to fetch and display popups for this page</li>
                <li>Popups will automatically show based on their delay and duration settings</li>
                <li>Once a popup is shown, it won't show again until you reset</li>
                <li>Use "Reset Shown Popups" to clear the shown popups list for testing</li>
                <li>Navigate to different pages to test page-specific popups</li>
              </ol>
              
              <Alert variant="info" className="mt-3">
                <strong>Note:</strong> Popups are filtered based on:
                <ul className="mb-0 mt-2">
                  <li>Valid display_duration (not null)</li>
                  <li>Valid delay_ms (not null)</li>
                  <li>Page target matching current path (or null for all pages)</li>
                  <li>Not already shown in current session</li>
                </ul>
              </Alert>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default PopupTestPage;
