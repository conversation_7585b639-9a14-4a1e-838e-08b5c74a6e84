import React from "react";
import { Container, Table, Row, <PERSON>, Card, But<PERSON>, ProgressBar } from "react-bootstrap";
import { FaTrophy, FaCheckCircle, FaExclamationCircle } from "react-icons/fa";
import ResultNavbar from "../components/ResultNavbar";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

const Results = () => {
  const navigate = useNavigate();
  const testResult = useSelector((state) => state.paper.testResult);

  const totalQuestions = testResult?.total_questions;
  const answeredQuestions = totalQuestions - testResult?.not_attempted;
  const accuracy = testResult?.accuracy_percentage.toFixed(2);
  const score = testResult?.total_score.toFixed(2);
  const percentile = accuracy > 50 ? "Above Average" : "Below Average";

  return (
    <>
      <ResultNavbar testData={testResult} />
      <Container style={{ marginTop: "80px" }} className="">
        {/* Overall Performance Summary */}
        <Card className="mb-4">
          <Card.Body>
            <Card.Title className="h4">Overall Performance Summary</Card.Title>
            <Row className="d-flex justify-content-between align-items-center">
              {/* Rank */}
              {/* <Col className="d-flex align-items-center">
                <FaTrophy color="#FFD700" size={24} />
                <span className="ms-2 fw-bold">
                  {score}/{totalQuestions}
                </span>
                <span className="ms-1 text-muted">Rank</span>
              </Col> */}

              {/* Score */}
              <Col className="d-flex align-items-center">
                <FaTrophy color="#FFD700" size={24} />
                <span className="ms-2 fw-bold">{score}</span>
                <span className="ms-1 text-muted">Score</span>
              </Col>

              {/* Attempted */}
              <Col className="d-flex align-items-center">
                <FaCheckCircle color="#28a745" size={24} />
                <span className="ms-2 fw-bold">{answeredQuestions}</span>
                <span className="ms-1 text-muted">Attempted</span>
              </Col>

              {/* Not Attempted */}
              <Col className="d-flex align-items-center">
                <FaExclamationCircle color="#dc3545" size={24} />
                <span className="ms-2 fw-bold">
                  {testResult?.not_attempted}
                </span>
                <span className="ms-1 text-muted">Not Attempted</span>
              </Col>

              {/* Accuracy */}
              <Col className="d-flex align-items-center">
                <FaCheckCircle color="#28a745" size={24} />
                <span className="ms-2 fw-bold">{accuracy}%</span>
                <span className="ms-1 text-muted">Accuracy</span>
              </Col>

              {/* Percentile */}
              <Col className="d-flex align-items-center">
                <FaTrophy color="#FFD700" size={24} />
                <span className="ms-2 fw-bold">{percentile}</span>
                <span className="ms-1 text-muted">Percentile</span>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Weakness and Strengths */}
        <Row className="justify-content-center mt-5">
          <Col md={12}>
            <h4>Your Weakness and Strengths</h4>
          </Col>
        </Row>
        <Row>
          {Object.keys(testResult?.section_accuracy_percentage || {}).map((section, index) => {
            const sectionAccuracy = testResult?.section_accuracy_percentage[section];
            const total = testResult?.section_questions[section];
            const correct = testResult?.section_correct_questions[section];

            return (
              <Col md={12} lg={12} key={section} className="mb-3">
                <Card className="p-3 shadow-sm">
                  <Card.Body className="d-flex justify-content-start gap-5">
                    <div className="">
                      <Card.Title className="h6">{section}</Card.Title>
                      <div className="d-flex flex-column">
                        <div>Accuracy %</div>
                        <ProgressBar
                          now={sectionAccuracy}
                          label={`${sectionAccuracy}%`}
                          variant="success"
                          className="mt-3"
                        />
                      </div>
                    </div>

                    {/* Question Status Display */}
                    <div className="mt-3 d-flex flex-wrap gap-2">
                      {Array.from({ length: total }).map((_, idx) => {
                        const isAttempted = idx < correct;
                        return (
                          <div
                            key={idx}
                            style={{
                              width: "32px",
                              height: "32px",
                              borderRadius: "50%",
                              backgroundColor: isAttempted ? "#28a745" : "#6c757d",
                              color: "white",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              fontSize: "0.9rem",
                              fontWeight: "bold",
                            }}>
                            {idx + 1}
                          </div>
                        );
                      })}
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            );
          })}
        </Row>
        {/* ...existing code... */}
      </Container>
    </>
  );
};

export default Results;
