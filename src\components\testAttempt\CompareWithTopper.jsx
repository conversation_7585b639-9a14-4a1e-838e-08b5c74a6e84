import React from "react";
import { Table } from "react-bootstrap";

const CompareWithTopper = () => {
  // Dummy data for You, Topper, and Average
  const data = {
    labels: ["Score", "Accuracy", "Correct", "Wrong", "Time (mins)"],
    you: ["2/20", "10%", "2/20", "10/20", "05:20"],
    topper: ["18/20", "90%", "18/20", "2/20", "03:30"],
    average: ["10/20", "50%", "10/20", "10/20", "04:45"],
  };

  // Function to determine the background style for a value (percentage-based)
  const getCellBackgroundStyle = (value, label) => {
    let percentage = 0;
    let colorStart = "";
    let colorEnd = "white"; // Default white background for the remaining portion

    // For percentage-based values (Accuracy, Score, Correct, Time)
    if (label === "Accuracy" || label === "Score" || label === "Correct" || label === "Time (mins)" || label === "Wrong") {
      if (value.includes("%")) {
        percentage = parseInt(value.replace("%", ""));
      } else if (value.includes("/")) {
        const scoreYou = parseInt(data.you[0].split("/")[0]);
        const scoreTopper = parseInt(data.topper[0].split("/")[0]);
        const scoreAverage = parseInt(data.average[0].split("/")[0]);
        percentage = (scoreYou / 20) * 100; // Assuming max score is 20
      }

      // Set colors based on the label
      switch (label) {
        case "Accuracy":
          colorStart = "info"; // Bootstrap info color for Accuracy
          break;
        case "Score":
          colorStart = "warning"; // Bootstrap warning color for Score
          break;
        case "Correct":
          colorStart = "success"; // Bootstrap success color for Correct
          break;
        case "Wrong":
          colorStart = "danger"; // Bootstrap success color for Correct
          break;
        case "Time (mins)":
          colorStart = "secondary"; // Bootstrap danger color for Time
          break;
        default:
          colorStart = "success"; // Default color
          break;
      }

      // Return the linear gradient style for the background
      return {
        background: `linear-gradient(to right, var(--bs-${colorStart}) ${percentage}%, ${colorEnd} ${percentage}%)`,
        color: "black", // You can also change the text color if you want to make it more readable
      };
    }

    return {}; // Default no background style for non-percentage labels
  };

  return (
    <Table bordered hover responsive className="text-center">
      <thead className="thead-dark">
        <tr>
          <th></th>
          {data.labels.map((label, idx) => (
            <th key={idx}>{label}</th>
          ))}
        </tr>
      </thead>
      <tbody>
        {["you", "topper", "average"].map((category, rowIndex) => (
          <tr key={rowIndex}>
            <td className="fw-bold text-capitalize p-3">{category}</td>
            {data[category].map((value, colIndex) => (
              <td
                key={colIndex}
                className="p-3"
                style={getCellBackgroundStyle(value, data.labels[colIndex])}
              >
                {value}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </Table>
  );
};

export default CompareWithTopper;
