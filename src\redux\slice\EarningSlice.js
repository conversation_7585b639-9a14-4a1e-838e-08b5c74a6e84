// src/slices/earningSlice.js

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { store } from '../store'; // Import the store to access the token from the state

// Helper function to get the token (either from the state or localStorage)
const getAuthToken = () => {
  const token = store.getState().auth.token; // Get token from auth state in Redux store
  return token ? `Bearer ${token}` : ''; // If token exists, return in Bearer format
};

// Async thunks for the functions (with token in the header)
export const getAllEarnings = createAsyncThunk('earning/getAllEarnings', async () => {
  const token = getAuthToken();
  const response = await fetch('/api/earnings', {
    method: 'GET',
    headers: {
      'Authorization': token, // Add the token here in the header
    },
  });
  const data = await response.json();
  return data;
});

export const viewEarning = createAsyncThunk('earning/viewEarning', async (earningId) => {
  const token = getAuthToken();
  const response = await fetch(`/api/earnings/${earningId}`, {
    method: 'GET',
    headers: {
      'Authorization': token, // Add the token here in the header
    },
  });
  const data = await response.json();
  return data;
});

export const requestWithdrawal = createAsyncThunk('earning/requestWithdrawal', async (withdrawalData) => {
  const token = getAuthToken();
  const response = await fetch('/api/withdrawals', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token, // Add the token here in the header
    },
    body: JSON.stringify(withdrawalData),
  });
  const data = await response.json();
  return data;
});

export const updateWithdrawalRequest = createAsyncThunk('earning/updateWithdrawalRequest', async (withdrawalId, updateData) => {
  const token = getAuthToken();
  const response = await fetch(`/api/withdrawals/${withdrawalId}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token, // Add the token here in the header
    },
    body: JSON.stringify(updateData),
  });
  const data = await response.json();
  return data;
});

// Initial state
const initialState = {
  earnings: [],
  earningDetails: null,
  withdrawalStatus: null,
  status: 'idle', // 'idle' | 'loading' | 'succeeded' | 'failed'
  error: null,
};

const earningSlice = createSlice({
  name: 'earning',
  initialState,
  reducers: {
    resetEarningsState(state) {
      state.earnings = [];
      state.earningDetails = null;
      state.withdrawalStatus = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all earnings
      .addCase(getAllEarnings.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getAllEarnings.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.earnings = action.payload;
      })
      .addCase(getAllEarnings.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })

      // View specific earning
      .addCase(viewEarning.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(viewEarning.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.earningDetails = action.payload;
      })
      .addCase(viewEarning.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })

      // Request withdrawal
      .addCase(requestWithdrawal.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(requestWithdrawal.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.withdrawalStatus = action.payload;
      })
      .addCase(requestWithdrawal.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })

      // Update withdrawal request
      .addCase(updateWithdrawalRequest.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(updateWithdrawalRequest.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.withdrawalStatus = action.payload;
      })
      .addCase(updateWithdrawalRequest.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      });
  },
});

export const { resetEarningsState } = earningSlice.actions;

export default earningSlice.reducer;
