import React from 'react'

const ExamInstruction = () => {
  return (
    <>
        <div className="flex-grow-1 overflow-auto px-3">
          <h1 className="text-center">Test Name</h1>
          <div className="d-flex justify-content-between px-2">
            <p>
              <strong>Duration: 90 Mins</strong>
            </p>
            <p>
              <strong>Maximum Marks: 100</strong>
            </p>
          </div>
          <p className="fw-bold ms-2">
            Read the following instructions carefully.
          </p>
          <ol className="ms-3">
            <li className="mb-1">
              The test contains 3 sections with 100 questions.
            </li>
            <li className="mb-1">
              Each question has 5 options, only one is correct.
            </li>
            <li className="mb-1">You have 60 minutes to finish the test.</li>
            <li className="mb-1">
              1 mark for each correct answer, 0.25 deduction for wrong answers.
            </li>
            <li className="mb-1">
              No negative marking for unattempted questions.
            </li>
            <li className="mb-1">
              You can attempt this test only once, ensure submission before
              closing.
            </li>
          </ol>
        </div>
    </>
  )
}

export default ExamInstruction
