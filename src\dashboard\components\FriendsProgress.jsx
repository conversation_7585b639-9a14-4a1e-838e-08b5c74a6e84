import React, { useState } from 'react';
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { FaUsers, FaMobileAlt, FaDownload } from 'react-icons/fa';
import { useDispatch, useSelector } from 'react-redux';
import { getMatchedContacts } from '../../redux/slice/contactSlice';

// Performance levels configuration
const levels = [
  { name: 'EXPERT', color: '#28a745' },
  { name: 'ADVANCED', color: '#fd7e14' },
  { name: 'INTERMEDIATE', color: '#ffc107' },
  { name: 'BEGINNER', color: '#ffd700' },
  { name: 'STARTER', color: '#0099ff' }
];

// Friends progress with comparative analysis (dummy data)
const friendsProgress = [
  {
    name: '<PERSON><PERSON><PERSON>',
    currentLevel: 'ADVANCED',
    examType: 'Banking',
    progressPercent: 85,
    userComparison: 'You need 9% more practice to reach <PERSON><PERSON><PERSON>\'s level',
    friendStatus: '<PERSON><PERSON><PERSON> is ahead of you by 9%'
  },
  {
    name: '<PERSON>',
    currentLevel: 'INTERMEDIATE',
    examType: 'SSC',
    progressPercent: 68,
    userComparison: 'You are ahead of Ravi by 8%',
    friendStatus: 'Ravi needs 8% more to reach your level'
  },
  {
    name: 'Sneha Verma',
    currentLevel: 'BEGINNER',
    examType: 'UPSC',
    progressPercent: 45,
    userComparison: 'You are ahead of Sneha by 31%',
    friendStatus: 'Sneha needs 31% more to reach your level'
  },
  {
    name: 'Arjun Singh',
    currentLevel: 'EXPERT',
    examType: 'Banking',
    progressPercent: 92,
    userComparison: 'You need 16% more practice to reach Arjun\'s level',
    friendStatus: 'Arjun is ahead of you by 16%'
  },
  {
    name: 'Priya Patel',
    currentLevel: 'ADVANCED',
    examType: 'Railway',
    progressPercent: 78,
    userComparison: 'You are ahead of Priya by 2%',
    friendStatus: 'Priya needs 2% more to reach your level'
  },
  {
    name: 'Rohit Kumar',
    currentLevel: 'STARTER',
    examType: 'Banking',
    progressPercent: 25,
    userComparison: 'You are ahead of Rohit by 51%',
    friendStatus: 'Rohit needs 51% more to reach your level'
  }
];

const FriendsProgress = () => {
  const [showFriends, setShowFriends] = useState(false);
  const dispatch = useDispatch();
  const { matchedContacts, loading, error } = useSelector(state => state.contacts);

  // Get level color helper
  const getLevelColor = (levelName) => {
    const level = levels.find(l => l.name === levelName);
    return level ? level.color : '#9E9E9E';
  };

  const handleShowFriends = () => {
    setShowFriends(true);
    dispatch(getMatchedContacts());
  };

  // Normalize matched contacts to the expected display format
  const normalizedFriends = (matchedContacts && matchedContacts.length > 0)
    ? matchedContacts.map(friend => ({
        name: friend.name || friend.related_user_info?.username || 'Unknown',
        currentLevel: friend.current_level || 'STARTER',
        examType: friend.exam_type || '',
        progressPercent: friend.progress_percent || 0,
        userComparison: friend.user_comparison || '',
        friendStatus: friend.friend_status || '',
        contact_number: friend.contact_number,
        is_matched: friend.is_matched,
      }))
    : friendsProgress;

  return (
    <Card className="shadow-sm border-0">
      <Card.Body>
        <div className="d-flex align-items-center justify-content-between mb-4">
          <h4 className="text-success mb-0">
            <FaUsers className="me-2" />
            Friends Progress
          </h4>
        </div>

        {!showFriends ? (
          <div className="text-center py-5">
            <div className="mb-4">
              <FaMobileAlt size={60} className="text-muted mb-3" />
              <h5 className="text-muted">Want to see your friends' progress?</h5>
              <p className="text-muted mb-4">
                Open our mobile app to connect with friends and compare your progress!
              </p>
              <div className="d-flex flex-column align-items-center gap-3">
                <p className="text-muted mb-2">
                  <FaDownload className="me-2" />
                  You can download our app from Play Store
                </p>
                <Button 
                  variant="outline-success" 
                  onClick={handleShowFriends}
                  className="px-4"
                >
                  See Performance of Your Friends
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div>
            {loading && <div className="text-center my-4"><Spinner animation="border" variant="success" /></div>}
            {error && <Alert variant="danger">{error}</Alert>}
            <div className="alert alert-info mb-4">
              <small>
                <FaMobileAlt className="me-2" />
                {matchedContacts && matchedContacts.length > 0
                  ? 'These are your matched friends from your account.'
                  : 'This is demo data. To see real friends\' progress, download our mobile app and give contact access.'}
              </small>
            </div>

            <Row className="g-3">
              {normalizedFriends.map((friend, index) => (
                <Col key={index} md={6} lg={4}>
                  <Card className="h-100 border-0 shadow-sm">
                    <Card.Body>
                      <div className="d-flex justify-content-between align-items-start mb-3">
                        <div>
                          <h6 className="mb-1 fw-bold">{friend.name}</h6>
                          <small className="text-muted">
                            {friend.examType || friend.contact_number || ''} Preparation
                          </small>
                        </div>
                        <Badge 
                          style={{ 
                            backgroundColor: getLevelColor(friend.currentLevel),
                            fontSize: '0.7rem'
                          }}
                        >
                          {friend.currentLevel}
                        </Badge>
                      </div>

                      <div className="mb-3">
                        <div className="d-flex justify-content-between align-items-center mb-1">
                          <small className="text-muted">Progress</small>
                          <small className="fw-bold">{friend.progressPercent}%</small>
                        </div>
                        <div className="progress" style={{ height: '8px' }}>
                          <div 
                            className="progress-bar" 
                            style={{ 
                              width: `${friend.progressPercent}%`,
                              backgroundColor: getLevelColor(friend.currentLevel)
                            }}
                          />
                        </div>
                      </div>

                      <div className="border-top pt-3">
                        <div className="mb-2">
                          <small className="text-muted d-block">
                            📊 <strong>Comparison:</strong>
                          </small>
                          <small className="text-dark">
                            {friend.userComparison || (friend.is_matched !== undefined ? `Matched: ${friend.is_matched ? 'Yes' : 'No'}` : '')}
                          </small>
                        </div>
                        <div>
                          <small className="text-muted d-block">
                            👤 <strong>Status:</strong>
                          </small>
                          <small className="text-dark">
                            {friend.friendStatus || ''}
                          </small>
                        </div>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              ))}
            </Row>

            <div className="text-center mt-4">
              <small className="text-muted">
                Want to add more friends? Download our mobile app and sync your contacts!
              </small>
            </div>
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

export default FriendsProgress;
