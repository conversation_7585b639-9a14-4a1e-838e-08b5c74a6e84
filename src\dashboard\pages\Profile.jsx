import React, { useEffect, useState } from "react";
import {
  Container,
  Row,
  Col,
  Card,
  Button,
  Modal,
  Form,
  Image,
  Badge,
} from "react-bootstrap";
import {
  Fa<PERSON>ser,
  FaUserGraduate,
  FaPhone,
  FaMapMarkerAlt,
  FaBook,
  FaCheckCircle,
  FaLanguage,
  FaEdit,
  FaCalendarDay,
  FaCalendarWeek,
  FaCalendarAlt,
  FaInfinity,

} from "react-icons/fa";
import { useDispatch, useSelector } from "react-redux";
// import MyTestSereis from "../../components/dashboard/testSeries/MyTestSereis";
// import MyAttmptedTests from "../../components/dashboard/attemptedTest/MyAttmptedTests";
import ReferralData from "../../commonCompoenents/ReferralData";
import EditStudentProfile from "../components/EditStudentProfile";
import ProgressMeter from "../components/ProgressMeter";
import FriendsProgress from "../components/FriendsProgress";
import { getStudentProfile } from "../../redux/slice/studentSlice";
import Skeleton from "react-loading-skeleton";
import TicketCard from "../components/TicketCard";
import TestSeriesCard from "../../commonCompoenents/TestSeriesCard";
import SubscribedEvents from "../../commonCompoenents/SubscribedEvents";
import { useNavigate, useLocation } from "react-router-dom";
import { toast } from "react-hot-toast";
import YourContacts from "../components/YourContacts";

const Profile = () => {
  const studentData = useSelector((state) => state?.student);
  const navigate = useNavigate();
  const [show, setShow] = useState(false);
  const [formData, setFormData] = useState();
  const [loading, setLoading] = useState(false);
  // const [student, setStudent] = useState();

  const dispatch = useDispatch();

  const { hash } = useLocation(); // Get the hash from URL

  const getStudentData = async () => {
    setLoading(true);
    try {
      const res = await dispatch(
        getStudentProfile({ id: studentData?.student?.student?.id })
      );
      if (res?.meta?.requestStatus === "fulfilled") {
        setFormData(res?.payload);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    const scrollToHash = async () => {
      await getStudentData(); // Wait for getStudentData to fulfill
      if (hash) {
        const element = document.querySelector(hash);
        if (element) {
          element.scrollIntoView({ behavior: "smooth" });
        }
      }
    };
    scrollToHash();
  }, [hash]); // Run effect when hash changes

  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);


  return (
    <Container className="d-flex align-items-center justify-content-center">
      <Row className="justify-content-center w-100">
        <Col md={10} lg={7} xs={12} className="p-0">
          {/* Profile Details */}
          <Card className="mt-5 p-md-4 p-0 shadow border-success">
            {/* Card Header */}
            <Card.Header className="bg-gradient bg-success text-white d-md-flex justify-content-between align-items-center d-none">
              <h4 className="mb-0">
                <FaUser className="me-2 text-light" /> Your Profile
              </h4>
              <Button variant="light" onClick={handleShow}>
                <FaEdit className="text-success me-1" /> Edit Profile
              </Button>
            </Card.Header>

            <Card
              className="my-md-4 w-100 shadow-sm border-0 d-flex justify-content-between"
              style={{
                background:
                  "linear-gradient(to right,rgb(194, 220, 248), #ffffff)",
              }}
            >
              <div className="d-flex flex-lg-row flex-column align-items-center justify-content-lg-between justify-content-center w-100">
                <Card.Body className="d-flex align-items-center p-md-4 flex-column justify-content-center flex-sm-row">
                  <div
                    className="me-3"
                    style={{
                      width: "100px",
                      height: "100px",
                      borderRadius: "50%",
                      overflow: "hidden",
                      border: "2px solid black",
                      backgroundColor: "white",
                      flexShrink: 0,
                    }}
                  >
                    <img
                      src={formData?.student?.image_url || "/avatar.png"}
                      alt="Profile"
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                        display: "block",
                      }}
                    />
                  </div>

                  <div className="text-md-start text-center">
                    <h4 className="text-dark d-flex gap-3">
                      Hi,
                      {loading ? (
                        <Skeleton
                          width={100}
                          baseColor="#e6ffe6"
                          highlightColor="#c4f7c4"
                        />
                      ) : (
                        <span className="fw-bold text-success">
                          {formData?.student?.user?.username || "User_name"}!
                        </span>
                      )}
                    </h4>
                    <p className="mb-0 text-success">
                      {loading ? (
                        <Skeleton
                          width={180}
                          baseColor="#e6ffe6"
                          highlightColor="#c4f7c4"
                        />
                      ) : (
                        "Welcome to your profile"
                      )}
                    </p>
                  </div>
                </Card.Body>
                <ReferralData profile={true} />
              </div>
            </Card>

            {/* Card Body */}
            <Card.Body>
              <Row className="mb-md-3 mb-2">
                <Col xs={12} md={6} className="mb-md-0 mb-2">
                  <strong>
                    <FaUserGraduate className="text-primary me-2 fs-4" /> Name:
                  </strong>{" "}
                  <span className="fs-6">
                    {loading ? (
                      <Skeleton
                        width={150}
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                      />
                    ) : (
                      <>
                        {formData?.student?.user?.first_name || "Your"}{" "}
                        {formData?.student?.user?.last_name || "name"}
                      </>
                    )}
                  </span>
                </Col>
                <Col xs={12} md={6} className="mb-md-0 mb-2">
                  <strong>
                    <FaPhone className="text-info me-2 fs-4" /> Phone:
                  </strong>{" "}
                  <span className="fs-6">
                    {loading ? (
                      <Skeleton
                        width={120}
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                      />
                    ) : (
                      formData?.student?.phone || "phone"
                    )}
                  </span>
                </Col>
              </Row>

              <Row className="mb-md-3 mb-2">
                <Col xs={12} md={6} className="mb-md-0 mb-2">
                  <strong>
                    <FaCheckCircle className="text-success me-2 fs-4" />{" "}
                    Subscription:
                  </strong>{" "}
                  <span className="fs-6">
                    {loading ? (
                      <Skeleton
                        width={120}
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                      />
                    ) : (
                      formData?.student?.subscription_type || "Subscription type"
                    )}
                  </span>
                </Col>
                <Col xs={12} md={6} className="mb-md-0 mb-2">
                  <strong>
                    <FaBook className="text-warning me-2 fs-4" /> Course:
                  </strong>{" "}
                  <span className="fs-6">
                    {loading ? (
                      <Skeleton
                        width={140}
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                      />
                    ) : (
                      formData?.student?.course || "Coursename"
                    )}
                  </span>
                </Col>
              </Row>

              <Row className="mb-md-3 mb-2 d-md-none">
                <Col className="mb-md-0">
                  {loading ? (
                    <Skeleton
                      height={40}
                      baseColor="#e6ffe6"
                      highlightColor="#c4f7c4"
                    />
                  ) : (
                    <Button
                      variant="success"
                      onClick={handleShow}
                      className="w-100 d-flex justify-content-center align-items-center gap-1"
                    >
                      <FaEdit className="text-white me-1" /> Edit Profile
                    </Button>
                  )}
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>

        {/* Progress Meter Section */}
        <Col lg={10} className="mt-5">
          <ProgressMeter />
        </Col>

        {/* Practice Data Section */}
        <Col lg={10} className="mt-5">
          <Card className="shadow-sm border-0">
            <Card.Body>
              <h4 className="text-success mb-4">Practice Statistics</h4>
              <Row className="g-4">
                <Col xs={12} md={6} lg={3}>
                  <Card className="text-center border border-primary shadow-sm">
                    <Card.Body>
                      <FaCalendarDay className="text-primary fs-1 mb-2" />
                      <Card.Title className="fs-6">Daily Practice</Card.Title>
                      <Card.Text className="fs-5 fw-bold text-dark">
                        {formData?.daily_practice || 0}
                      </Card.Text>
                    </Card.Body>
                  </Card>
                </Col>
                <Col xs={12} md={6} lg={3}>
                  <Card className="text-center border border-success shadow-sm">
                    <Card.Body>
                      <FaCalendarWeek className="text-success fs-1 mb-2" />
                      <Card.Title className="fs-6">Weekly Practice</Card.Title>
                      <Card.Text className="fs-5 fw-bold text-dark">
                        {formData?.weekly_practice || 0}
                      </Card.Text>
                    </Card.Body>
                  </Card>
                </Col>
                <Col xs={12} md={6} lg={3}>
                  <Card className="text-center border border-warning shadow-sm">
                    <Card.Body>
                      <FaCalendarAlt className="text-warning fs-1 mb-2" />
                      <Card.Title className="fs-6">Monthly Practice</Card.Title>
                      <Card.Text className="fs-5 fw-bold text-dark">
                        {formData?.monthly_practice || 0}
                      </Card.Text>
                    </Card.Body>
                  </Card>
                </Col>
                <Col xs={12} md={6} lg={3}>
                  <Card className="text-center border border-danger shadow-sm ">
                    <Card.Body>
                      <FaInfinity className="text-danger fs-1 mb-2" />
                      <Card.Title className="fs-6">Continuous Practice</Card.Title>
                      <Card.Text className="fs-5 fw-bold text-dark">
                        {formData?.continuous_practice || 0}
                      </Card.Text>
                    </Card.Body>
                  </Card>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>

        {/* Friends Progress Section */}
        <Col lg={10} className="mt-5">
          <FriendsProgress />
        </Col>
        <Col lg={10} className="mt-4">
          <YourContacts />
        </Col>

        <Col lg={10} className="mt-5">
          <h2 className="text-start mb-4 text-success" id="attempted-tests">My Attempted Tests</h2>

          <div className="d-flex flex-wrap justify-content-start">
            {formData?.card_data?.length > 0 ? (
              formData.card_data.map((test, index) => (
                <TestSeriesCard
                  key={index}
                  test={test}
                  addTestLoading={false} // Example: set a state for loading if needed
                  handleAddTestSeries={(slug) => console.log("Adding:", slug)}
                  navigate={navigate}
                />
              ))
            ) : (
              <p>No attempted tests found.</p>
            )}
          </div>
        </Col>
        <Col lg={10} className="mt-5" id="queries">
          <h2 className="text-start mb-4 text-success">My Raised Queries</h2>
          {formData?.tickets?.length > 0 && formData?.tickets?.map((ticket) => (
            <Col md={6} className="mt-5" key={ticket?.id}>
              <TicketCard ticket={ticket} />
            </Col>
          ))}
        </Col>

        {/* Subscribed Events Section */}
        <Col lg={10} className="mt-5" id="subscribed-events">
          <SubscribedEvents showTitle={true} />
        </Col>
      </Row>

      {/* Edit Profile Modal */}
      <Modal show={show} onHide={handleClose} size="lg" centered>
        <Modal.Header closeButton>
          <Modal.Title>Edit Profile</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <EditStudentProfile
            data={formData?.student}
            handleClose={handleClose}
            refetch={getStudentData}
          />
        </Modal.Body>
      </Modal>
    </Container>
  );
};

export default Profile;
