.progress-meter-container {
  position: relative;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.meter-background {
  position: relative;
  width: 60px;
  height: 300px;
  background-color: #f0f0f0;
  border-radius: 30px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-right: 20px;
  display: flex;
  flex-direction: column;
}

.meter-section {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.meter-section:first-child {
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
  overflow: hidden;
}

.meter-section:last-child {
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;
  border-bottom: none;
  overflow: hidden;
}

.level-labels {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.level-name {
  font-size: 8px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 2px;
}

.level-range {
  font-size: 6px;
  text-align: center;
  margin-top: 2px;
}

.meter-fill {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.3);
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;
  z-index: 5;
}

.pointer-container {
  position: absolute;
  left: -8px;
  top: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  transform-origin: center;
}

.pointer-wrapper {
  display: flex;
  align-items: center;
}

.pointer {
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-left: 15px solid #ffc107;
}

.pointer-dot {
  width: 12px;
  height: 12px;
  border-radius: 6px;
  background-color: #ffc107;
  margin-left: 2px;
  border: 2px solid #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.score-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 20px;
}

.current-score {
  font-size: 36px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 5px;
}

.score-label {
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 1px;
  color: #666;
}

.current-level-info {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: center;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.level-indicator {
  width: 12px;
  height: 12px;
  border-radius: 6px;
  margin-right: 12px;
  flex-shrink: 0;
}

.level-details {
  flex: 1;
}

.current-level-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
}

.current-level-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
  line-height: 1.4;
}

.next-level-text {
  font-size: 12px;
  color: #888;
  font-style: italic;
  margin-bottom: 0;
}

.study-stats {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-item {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
  margin: 5px;
}

.stat-value {
  margin-bottom: 8px;
  font-size: 20px;
  font-weight: bold;
}

.stat-label {
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .progress-meter-container {
    margin-bottom: 30px;
    flex-direction: column;
    align-items: center;
  }

  .meter-background {
    margin-right: 0;
    margin-bottom: 20px;
  }

  .score-display {
    position: static;
    margin-left: 0;
    margin-top: 20px;
  }

  .current-score {
    font-size: 2rem;
  }

  .pointer-container {
    left: -8px;
    top: 0;
  }
}

@media (max-width: 768px) {
  .progress-meter-container {
    padding: 15px;
    flex-direction: column;
    align-items: center;
  }

  .meter-background {
    width: 60px;
    height: 280px;
    border-radius: 30px;
    margin-right: 0;
    margin-bottom: 20px;
  }

  .meter-section:first-child {
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
    overflow: hidden;
  }

  .meter-section:last-child {
    border-bottom-left-radius: 30px;
    border-bottom-right-radius: 30px;
    overflow: hidden;
  }

  .meter-fill {
    border-bottom-left-radius: 30px;
    border-bottom-right-radius: 30px;
  }

  .level-name {
    font-size: 8px;
    font-weight: bold;
  }

  .level-range {
    font-size: 6px;
  }

  .current-score {
    font-size: 1.8rem;
  }

  .score-display {
    position: static;
    margin-left: 0;
    margin-top: 15px;
  }

  .score-label {
    font-size: 10px;
  }

  .pointer-container {
    left: 3.7rem;
    top: 0;
    transform: translateX(0);
    transform-origin: center;
  }

  .pointer {
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 15px solid #ffc107;
  }

  .pointer-dot {
    width: 12px;
    height: 12px;
    border-radius: 6px;
  }

  .current-level-name {
    font-size: 16px;
  }

  .current-level-desc {
    font-size: 12px;
  }

  .next-level-text {
    font-size: 11px;
  }
}

/* Animation classes */
.meter-fill-animate {
  animation: fillMeter 2s ease-in-out;
}

@keyframes fillMeter {
  0% { height: 0%; }
  100% { height: var(--fill-height); }
}

.pointer-animate {
  animation: movePointer 2s ease-in-out;
}

@keyframes movePointer {
  0% { transform: translateY(150px); }
  100% { transform: translateY(var(--pointer-position)); }
}

.pulse-animate {
  animation: pulse 1.8s ease-in-out;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  16.67%, 50%, 83.33% { transform: scale(1.2); }
  33.33%, 66.67% { transform: scale(1); }
}
