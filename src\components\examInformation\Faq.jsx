import React from "react";
import { Accordion, Col, Container, Row } from "react-bootstrap";
import { useTheme } from "../../context/ThemeContext";

const Faq = ({ faqData, name }) => {
  const { isDarkMode, theme } = useTheme();

  return (
    <>
      <h4
        className="mt-5 mb-4"
        style={{ color: theme.colors.text }}
      >
        {name}
        <span className="text-success"> - FAQs</span>
      </h4>
      {/* <h4 className="mb-4">All Railway Exams 2025 - <span className='text-success'>FAQs</span></h4> */}
      <Accordion defaultActiveKey="0">
        {faqData.map((faq, index) => (
          <Accordion.Item
            eventKey={index.toString()}
            key={index}
            className="custom-accordion mb-3"
            style={{
              backgroundColor: theme.colors.cardBackground,
              borderColor: isDarkMode ? '#333333' : theme.colors.cardBorder,
              border: `1px solid ${isDarkMode ? '#333333' : theme.colors.cardBorder}`,
              borderRadius: '0.5rem',
              overflow: 'hidden',
              transition: 'all 0.3s ease'
            }}
          >
            <Accordion.Header
              style={{
                backgroundColor: theme.colors.cardBackground,
                color: theme.colors.cardText,
                borderBottom: `1px solid ${isDarkMode ? '#333333' : theme.colors.cardBorder}`
              }}
            >
              <span style={{ color: theme.colors.cardText, fontWeight: '500' }}>
                {faq.question}
              </span>
            </Accordion.Header>
            <Accordion.Body
              style={{
                backgroundColor: theme.colors.cardBackground,
                color: theme.colors.cardText,
                borderTop: `1px solid ${isDarkMode ? '#333333' : theme.colors.cardBorder}`
              }}
            >
              {faq.answer}
            </Accordion.Body>
          </Accordion.Item>
        ))}
      </Accordion>
    </>
  );
};

export default Faq;
