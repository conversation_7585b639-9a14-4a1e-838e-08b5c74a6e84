import React from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> } from "react-bootstrap";
import { <PERSON> } from "react-router-dom";

const ExamOverview2 = ({ data }) => {
  return (
    <>
      <Card className="shadow-sm border-0 bg-white my-4">
        <Card.Body className="text-muted">{data?.description}</Card.Body>
      </Card>

      <Card className="p-3">
        <Row>
          <Col md={7}>
            <h5 className="fw-medium border-bottom border-bottom-2 border-seondary pb-2">{data?.name} Overview</h5>
            <div className="d-flex flex-wrap">
              {data?.overview?.map((info, index) => (
                <Col md={6} key={index} className="p-1  mt-2">
                  <div className="bg-success text-white p-1 px-2 rounded-3">{info.name}</div>
                  <div className="bg-success bg-opacity-10 p-1 px-2 mt-1 rounded-3">{info.value}</div>
                </Col>
              ))}
            </div>
          </Col>
          <Col md={5}>
          <h5 className="fw-medium border-bottom border-bottom-2 border-seondary pb-2">{name} Latest Update</h5>
          <div className="d-flex flex-wrap">
              {data?.latest_points.map((info, index) => (
                <Col md={12} key={index} className="p-1  mt-2">
                  <Button as={Link} to={info?.link} className="bg-primary w-100 text-start bg-opacity-10 text-primary p-1 px-2 rounded-3">{info.name}</Button>
                </Col>
              ))}
            </div>
          </Col>
        </Row>
      </Card>
    </>
  );
};

export default ExamOverview2;


