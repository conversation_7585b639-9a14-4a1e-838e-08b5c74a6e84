import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>ton, <PERSON>, <PERSON>, <PERSON>, Spin<PERSON>, Card, Image } from "react-bootstrap";
import axios from "axios";
import NavBar from "../../commonCompoenents/NavBar";
import { useNavigate, useLocation } from "react-router-dom";
import TestSeriesComponent from "../components/TestSeriesComponent";
import Achievement from "../components/Achievement";
import Packages from "../../dashboard/pages/Packages";
import WhyShashtrarth from "../components/WhyShashtrarth";
import BlogSection from "../components/BlogSection";
import Footer from "../components/Footer";
import AboutSection from "../components/AboutSection";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { getCourses } from "../../redux/slice/courseSlice";
import { useDispatch, useSelector } from "react-redux";
import { MdOutlineKeyboardArrowRight } from "react-icons/md";
import LeftFLoatingIcons from "../../commonCompoenents/LeftFLoatingIcons";
import RightFloatingIcons from "../../commonCompoenents/RightFloatingIcons";
import Banner from "../../commonCompoenents/Banner";
import { Toaster, toast } from "react-hot-toast";
import ExamInfoSection from "../components/ExamInfoSection";
import { trackPageView } from "../../redux/slice/TrackPageViewSlice";
import { Helmet } from "react-helmet-async";
import PlayStoreDownload from "../components/PlayStoreDownload";
import { useTheme } from "../../context/ThemeContext";

const HomePage = () => {
  const { isDarkMode, theme } = useTheme();
  const [courses, setCourses] = useState([]);
  const [subCourses, setSubCourses] = useState([]);
  const [activeCourseId, setActiveCourseId] = useState(null);
  const [activeCourseImage, setActiveCourseImage] = useState(null);
  const [loadingCourses, setLoadingCourses] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(true); // Define loading state
  const baseUrl = import.meta.env.VITE_BASE_URL;
  const token = import.meta.env.VITE_CUSTOMERCARE_TOKEN;

  const navigate = useNavigate(); // Hook to handle redirection
  const location = useLocation();
  const dispatch = useDispatch();

  const accessToken = useSelector((state) => state?.student?.student?.JWT_Token?.access);

  // Check for access token on page load
  useEffect(() => {
    if (accessToken) {
      toast.success("Welcome to Sashtrarth");
      const timer = setTimeout(() => {
        navigate('/dashboard'); // Redirect to dashboard after 2 seconds
      }, 2000);
      return () => clearTimeout(timer); // Cleanup timer
    }
  
    // Handle scrolling to a section when hash is present in the URL
    if (location.hash) {
      const element = document.getElementById(location.hash.substring(1));
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      }
    }
  }, [accessToken, navigate, location.hash]);
  

  useEffect(() => {
    setLoading(false);
  }, []);

  const fetchCourses = async () => {
    try {
      setLoadingCourses(true);
      const response = await dispatch(getCourses());
      setCourses(response?.payload || []);
      if (response?.payload?.length > 0) {
        setSubCourses(response?.payload[0]?.sub_courses || []);
        setActiveCourseId(response?.payload[0]?.course_id);
        setActiveCourseImage(response?.payload[0]?.attachments);
      }
    } catch (error) {
      console.error("Error fetching courses:", error);
    } finally {
      setLoadingCourses(false);
    }
  };

  const navigateToSignup = () => {
    // Redirect to the signup page when a subcourse is clicked
    navigate(`/register`);
  };

  const handleSubCourseClick = (courseId) => {
    localStorage.setItem("selectedCourseId", courseId); // Store course ID in local storage
    navigate("/signup");
  };

  useEffect(() => {
    fetchCourses();
  }, []);

  useEffect(() => {
    const currentPath = window.location.pathname;
    const pageViews = JSON.parse(localStorage.getItem("pageViews")) || {};
    pageViews[currentPath] = (pageViews[currentPath] || 0) + 1;
    localStorage.setItem("pageViews", JSON.stringify(pageViews));

    // Dispatch the trackPageView action with the updated pageViews
    dispatch(trackPageView(pageViews));
  }, [dispatch]);

  return (
    <>
      <Helmet>
        <title>Shashtrarth Mock Test Platform | Free Mock Tests, Rank Analysis & Exam Info</title>
        <meta name="description" content="Practice free mock tests, get detailed rank analysis, and stay updated with the latest exam information on Shashtrarth. Join thousands of toppers who trust our platform for their exam preparation." />
        {/* Main WebPage Schema */}
        <script type="application/ld+json">
          {`
          {
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "Shashtrarth",
            "url": "https://shashtrarth.com/",
            "description": "Practice free mock tests, get detailed rank analysis, and stay updated with the latest exam information on Shashtrarth.",
            "publisher": {
              "@type": "Organization",
              "name": "Shashtrarth",
              "url": "https://shashtrarth.com/",
              "logo": {
                "@type": "ImageObject",
                "url": "https://shashtrarth.com/logo.png"
              }
            },
            "mainEntity": {
              "@type": "WebSite",
              "name": "Shashtrarth",
              "url": "https://shashtrarth.com/",
              "potentialAction": {
                "@type": "SearchAction",
                "target": "https://shashtrarth.com/search?q={search_term_string}",
                "query-input": "required name=search_term_string"
              }
            },
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": "https://shashtrarth.com/"
                }
              ]
            }
          }
          `}
        </script>
        {/* Packages Schema */}
        <script type="application/ld+json">
          {`
          {
            "@context": "https://schema.org",
            "@type": "ItemList",
            "name": "Packages",
            "itemListElement": [
              {
                "@type": "Product",
                "position": 1,
                "name": "Comprehensive Mock Test Package",
                "url": "https://shashtrarth.com/home#packages"
              },
            ]
          }
          `}
        </script>
        {/* Blogs Schema */}
        <script type="application/ld+json">
          {`
          {
            "@context": "https://schema.org",
            "@type": "Blog",
            "name": "Shashtrarth Blog",
            "url": "https://blog.shashtrarth.com/",
          }
          `}
        </script>


      </Helmet>
      {/* navbar */}
      <NavBar />

      {/* hero section */}
      <Container
        fluid
        className="my-3 rounded-4 bg-light min-vh-100 d-flex flex-column justify-content-center align-items-center"
      >
        <Row className="justify-content-center align-items-center gap-5">
          <Col xs={12} md={6} lg={4}>
            {/* Title Skeleton */}
            {loading ? (
              <Skeleton width={200} height={40} className="mb-3 text-start me-5" />
            ) : (
              <h1 className="fw-medium fs-1 text-center text-md-start cabin-sketch-bold">
                90% of Toppers Practice with Mock Tests Do You?
              </h1>
            )}

            {/* Description Skeleton */}
            {loading ? (
              <Skeleton height={20} width={400} className="mb-3 me-5" />
            ) : (
              <p className="lead text-muted text-center text-md-start">
                Stay Ahead with Shastrarth Get the Latest Mock Tests & Rank Analysis
              </p>
            )}

            {/* Button Skeleton */}
            {loading ? (
              <Skeleton
                height={50}
                width={200}
                className="mx-md-0 mx-auto"
                style={{ display: "block" }}
              />
            ) : (
              <Button
                variant="success"
                size="lg"
                onClick={() => setShowModal(true)}
                className="d-flex justify-content-center align-items-center gap-2 mx-md-0 mx-auto"
              >
                Take a Free Mock Test Now
                <MdOutlineKeyboardArrowRight size={24} className="mt-1" />
              </Button>
            )}
          </Col>

          {/* Image Skeleton */}
          <Col xs={12} md={6} lg={4}>
            {loading ? (
              <Skeleton
                height={300}
                width="100%"
                className=""
                style={{ minHeight: "300px", maxWidth: "600px" }}
              />
            ) : (
              <Image
                src="/hero.webp"
                fluid
                style={{
                  width: "100%",
                  height: "auto",
                  maxHeight: "300px",
                  minHeight: "300px",
                }}
              />
            )}
          </Col>
        </Row>

        {/* Play Store Download Component */}
        
      </Container>

        <Row className="justify-content-center">
          <Col xs={12} md={10} lg={10}>
            <PlayStoreDownload />
          </Col>
        </Row>
      {/* explore by test series */}
      <TestSeriesComponent />

      <Banner />

      {/* Why choose shahstrath */}
      <WhyShashtrarth />

      {/* Achievements */}
      <Achievement />

      {/* Packages */}
      <Packages />

      {/* Exam and Results */}

      <ExamInfoSection />

      {/* Blogs */}
      <BlogSection />

      {/* footer */}
      <AboutSection />
      <Footer />

      {/* Floating Chat Icon */}

      <RightFloatingIcons />


      {/* Floating Contact Icons */}
      <LeftFLoatingIcons />

      <Modal
        show={showModal}
        onHide={() => setShowModal(false)}
        size="lg"
        centered
        style={{ minHeight: "600px" }} // Reserve space for modal
      >
        <Modal.Header closeButton>
          <Modal.Title>Select a Course</Modal.Title>
        </Modal.Header>
        <Modal.Body
          className="p-0 overflow-x-hidden"
          style={{
            height: "600px", // Adjust height for modal header
            overflowY: "auto", // Allow scrolling in the body
          }}
        >
          <Row className="h-100">
            {loadingCourses ? (
              <Row className="h-100">
                <Col
                  md={4}
                  className="bg-white"
                  style={{
                    borderRight: "1px solid #ddd",
                    overflowY: "auto",
                    height: window.innerWidth < 768 ? "50vh" : "100vh", // Adjust height based on screen size
                  }}
                >
                  {[...Array(5)].map((_, index) => (
                    <div key={index} className="p-3 mb-2">
                      <Skeleton
                        circle
                        height={30}
                        width={30}
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                        className="me-2"
                      />
                      <Skeleton
                        height={20}
                        width="70%"
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                      />
                    </div>
                  ))}
                </Col>
                <Col
                  md={8}
                  className="p-2"
                  style={{
                    backgroundColor: "#F1F4F6",
                    overflowY: "auto",
                    height: window.innerWidth < 768 ? "50vh" : "100vh", // Adjust height based on screen size
                  }}
                >
                  <Row className="g-3">
                    {[...Array(6)].map((_, index) => (
                      <Col md={4} key={index}>
                        <Skeleton
                          height={150}
                          baseColor="#e6ffe6"
                          highlightColor="#c4f7c4"
                          className="w-100"
                        />
                      </Col>
                    ))}
                  </Row>
                </Col>
              </Row>
            ) : (
              <Row className="h-100">
                <Col
                  md={4}
                  className="bg-white"
                  style={{
                    borderRight: "1px solid #ddd",
                    overflowY: "auto",
                    height: window.innerWidth < 768 ? "50vh" : "100vh", // Adjust height based on screen size
                  }}
                >
                  {courses?.map((course) => (
                    <div
                      key={course?.course_id}
                      className={`d-flex align-items-center w-100 p-3 mb-2`}
                      style={{
                        cursor: "pointer",
                        backgroundColor:
                          activeCourseId === course?.course_id
                            ? "#F1F4F6"
                            : "#fff",
                        transition: "all 0.3s ease",
                      }}
                      onMouseEnter={(e) => {
                        if (activeCourseId !== course?.course_id) {
                          setActiveCourseId(course?.course_id);
                          setSubCourses(course?.sub_courses);
                          setActiveCourseImage(course?.attachments);
                        }
                        e.currentTarget.style.backgroundColor = "#F1F4F6";
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor =
                          activeCourseId === course?.course_id
                            ? "#F1F4F6"
                            : "#fff";
                      }}
                    >
                      <Image
                        src={`${baseUrl}${course?.attachments}`}
                        width="30px"
                        roundedCircle
                        className="me-2"
                      />
                      <span>{course?.name}</span>
                      <span className="ms-auto text-success">
                        <MdOutlineKeyboardArrowRight size={16} />
                      </span>
                    </div>
                  ))}
                </Col>

                {/* Right Column: Sub-Courses */}
                <Col
                  md={8}
                  className="p-2"
                  style={{
                    backgroundColor: "#F1F4F6",
                    overflowY: "auto",
                    height: window.innerWidth < 768 ? "50vh" : "100vh", // Adjust height based on screen size
                  }}
                >
                  <Row className="g-3">
                    {subCourses?.length > 0 ? (
                      subCourses.map((subCourse) => (
                        <Col md={4} key={subCourse?.subcourse_id} className="">
                          <Card
                            className="border-0 bg-white rounded shadow-sm mb-2 h-100"
                            style={{
                              cursor: "pointer",
                              transition: "transform 0.3s ease",
                            }}
                            onMouseEnter={(e) =>
                              (e.currentTarget.style.transform = "scale(1.05)")
                            }
                            onMouseLeave={(e) =>
                              (e.currentTarget.style.transform = "scale(1)")
                            }
                            onClick={() => handleSubCourseClick(activeCourseId)} // Use the function here
                          >
                            <Card.Body className="d-flex justify-content-start align-items-center gap-2 p-3">
                              {/* Image */}
                              <Image
                                src={`${baseUrl}${activeCourseImage}`}
                                width="30px"
                                height="auto"
                                className="align-self-center"
                              />

                              {/* Sub-Course Name */}
                              <Card.Text className="mb-0 text-truncate">
                                {subCourse?.name}
                              </Card.Text>

                              {/* Arrow SVG */}
                              <span className="ms-auto text-success align-self-center">
                                <MdOutlineKeyboardArrowRight size={16} />
                              </span>
                            </Card.Body>
                          </Card>
                        </Col>
                      ))
                    ) : (
                      <p className="text-danger text-center">
                        No sub-courses available
                      </p>
                    )}
                  </Row>
                </Col>
              </Row>
            )}
          </Row>
        </Modal.Body>
      </Modal>
      <Toaster />
    </>
  );
};

export default HomePage;

