import React, { useState } from "react";
import { Form } from "react-bootstrap";
import ExamInstruction from "./ExamInstruction";

const TestInstruction = ({ isChecked, setIsChecked }) => {
  return (
    <>
      <div className="d-flex flex-column" style={{ height: "80vh" }}>
        <ExamInstruction/>

        {/* Fixed Form Above Navigation */}
        <div className="bg-light p-3 border-top">
          <Form className="ms-2">
            <Form.Group
              controlId="languageSelect"
              className="d-flex align-items-center"
            >
              <Form.Label className="me-2">
                <strong>Choose your default language:</strong>
              </Form.Label>
              <select style={{ width: "200px" }}>
                <option disabled>--select--</option>
                <option>English</option>
                <option>Hindi</option>
              </select>
            </Form.Group>

            <p className="text-danger my-3">
              Please note: All questions will appear in your default language.
              This can be changed for each question later.
            </p>

            {/* Declaration */}
            <p>
              <strong>Declaration:</strong>
            </p>
            <Form.Check
              type="checkbox"
              label={
                <span className="fw-medium">
                  I have read all the instructions carefully and have understood
                  them. I agree not to cheat or use unfair means in this
                  examination. I understand that using unfair means of any sort
                  for my own or someone else's advantage will lead to my
                  immediate disqualification. The decision of Shashtrarth.com will
                  be final in these matters and cannot be appealed.
                </span>
              }
              onChange={() => setIsChecked(!isChecked)}
            />
          </Form>
        </div>
      </div>
    </>
  );
};

export default TestInstruction;
