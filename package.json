{"name": "student-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@lottiefiles/dotlottie-react": "^0.13.3", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.5.0", "@use-gesture/react": "^10.3.1", "axios": "^1.7.9", "bootstrap": "^5.3.3", "browser-image-compression": "^2.0.2", "chart.js": "^4.4.7", "firebase": "^11.1.0", "framer-motion": "^11.15.0", "html-react-parser": "^5.2.2", "jodit-react": "^4.1.2", "jspdf": "^2.5.2", "jwt-decode": "^4.0.0", "react": "^18.3.1", "react-bootstrap": "^2.10.7", "react-chartjs-2": "^5.3.0", "react-confetti": "^6.2.2", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.4.1", "react-icons": "^5.4.0", "react-loading-skeleton": "^3.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.1.1", "react-swipeable": "^7.0.2", "redux": "^5.0.1", "redux-persist": "^6.0.0", "sass": "^1.83.4", "sweetalert2": "^11.15.6"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "vite": "^6.0.5"}}