import React, { createContext, useState, useEffect, useContext } from 'react';

export const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [themeMode, setThemeMode] = useState('system'); // 'system', 'light', 'dark'

  // Function to get system theme preference
  const getSystemTheme = () => {
    return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
  };

  // Load theme preference from localStorage and sync with DOM
  const loadThemePreference = () => {
    try {
      // First, check if main.jsx has already applied a theme
      const initialThemeData = sessionStorage.getItem('initialThemeApplied');

      if (initialThemeData) {
        const { mode, isDark } = JSON.parse(initialThemeData);
        console.log('Using initial theme data from main.jsx:', { mode, isDark });
        setThemeMode(mode);
        setIsDarkMode(isDark);
        // Clear the session storage data as it's been consumed
        sessionStorage.removeItem('initialThemeApplied');
        return;
      }

      // Fallback to previous logic if no initial data
      const isDOMDark = document.documentElement.classList.contains('dark-theme');
      const savedThemeMode = localStorage.getItem('themeMode');
      const savedIsDarkMode = localStorage.getItem('isDarkMode');

      // Sync React state with actual DOM state
      setIsDarkMode(isDOMDark);

      if (savedThemeMode !== null) {
        const mode = savedThemeMode;
        setThemeMode(mode);

        // Verify the saved mode matches the DOM state
        let expectedDark = false;
        if (mode === 'system') {
          expectedDark = getSystemTheme();
        } else if (mode === 'dark') {
          expectedDark = true;
        } else {
          expectedDark = false;
        }

        // If DOM state doesn't match expected state, sync it
        if (isDOMDark !== expectedDark) {
          setIsDarkMode(expectedDark);
        }
      } else if (savedIsDarkMode !== null) {
        // Migration: if old format exists, convert to new format
        const oldIsDark = JSON.parse(savedIsDarkMode);
        const newMode = oldIsDark ? 'dark' : 'light';
        setThemeMode(newMode);
        setIsDarkMode(oldIsDark);
        // Save in new format
        localStorage.setItem('themeMode', newMode);
        localStorage.removeItem('isDarkMode'); // Remove old format
      } else {
        // If no saved preference, check DOM state or use system preference
        if (isDOMDark) {
          // DOM is dark, so set to dark mode
          setThemeMode('dark');
          localStorage.setItem('themeMode', 'dark');
        } else {
          // Use system preference
          const systemDark = getSystemTheme();
          setThemeMode('system');
          setIsDarkMode(systemDark);
          localStorage.setItem('themeMode', 'system');
        }
      }
    } catch (error) {
      console.error('Error loading theme preference:', error);
      // If error loading preference, check DOM state or use system preference
      const isDOMDark = document.documentElement.classList.contains('dark-theme');
      if (isDOMDark) {
        setThemeMode('dark');
        setIsDarkMode(true);
      } else {
        const systemDark = getSystemTheme();
        setThemeMode('system');
        setIsDarkMode(systemDark);
      }
    }
  };

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleSystemThemeChange = (e) => {
      if (themeMode === 'system') {
        setIsDarkMode(e.matches);
      }
    };

    // Add listener for system theme changes
    mediaQuery.addEventListener('change', handleSystemThemeChange);

    // Load initial theme preference
    loadThemePreference();

    // Debug: Log the initial state after loading
    setTimeout(() => {
      const isDOMDark = document.documentElement.classList.contains('dark-theme');
      const savedMode = localStorage.getItem('themeMode');
      console.log('Theme Debug - DOM Dark:', isDOMDark, 'Saved Mode:', savedMode, 'React State:', themeMode, isDarkMode);
    }, 100);

    // Cleanup listener on unmount
    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, []);

  // Update theme when themeMode changes
  useEffect(() => {
    if (themeMode === 'system') {
      setIsDarkMode(getSystemTheme());
    }
  }, [themeMode]);

  // Additional sync check after component mounts
  useEffect(() => {
    // Double-check synchronization after a short delay
    const syncTimer = setTimeout(() => {
      const isDOMDark = document.documentElement.classList.contains('dark-theme');
      const savedMode = localStorage.getItem('themeMode');

      // If there's a mismatch, fix it
      if (savedMode && ((savedMode === 'dark' && !isDOMDark) || (savedMode === 'light' && isDOMDark))) {
        console.log('Theme sync mismatch detected, fixing...');
        if (savedMode === 'dark') {
          setThemeMode('dark');
          setIsDarkMode(true);
          applyThemeToDOM(true);
        } else if (savedMode === 'light') {
          setThemeMode('light');
          setIsDarkMode(false);
          applyThemeToDOM(false);
        } else if (savedMode === 'system') {
          const systemDark = getSystemTheme();
          setThemeMode('system');
          setIsDarkMode(systemDark);
          applyThemeToDOM(systemDark);
        }
      }

      // If no saved mode but DOM has a theme, sync React state
      if (!savedMode && isDOMDark !== isDarkMode) {
        console.log('Syncing React state with DOM state...');
        setIsDarkMode(isDOMDark);
        if (isDOMDark) {
          setThemeMode('dark');
          localStorage.setItem('themeMode', 'dark');
        } else {
          setThemeMode('light');
          localStorage.setItem('themeMode', 'light');
        }
      }
    }, 200);

    return () => clearTimeout(syncTimer);
  }, [isDarkMode, themeMode]);

  // Apply theme to document root and body
  useEffect(() => {
    const root = document.documentElement;
    const body = document.body;
    
    if (isDarkMode) {
      root.classList.add('dark-theme');
      root.classList.remove('light-theme');
      body.classList.add('dark-theme');
      body.classList.remove('light-theme');
      
      // Set data attribute for CSS targeting
      root.setAttribute('data-theme', 'dark');
    } else {
      root.classList.add('light-theme');
      root.classList.remove('dark-theme');
      body.classList.add('light-theme');
      body.classList.remove('dark-theme');
      
      // Set data attribute for CSS targeting
      root.setAttribute('data-theme', 'light');
    }
  }, [isDarkMode]);

  const toggleTheme = () => {
    try {
      let newMode;
      if (themeMode === 'system') {
        // If currently system, toggle to opposite of current system theme
        newMode = getSystemTheme() ? 'light' : 'dark';
      } else if (themeMode === 'light') {
        newMode = 'dark';
      } else {
        newMode = 'light';
      }

      setThemeMode(newMode);
      setIsDarkMode(newMode === 'dark');
      localStorage.setItem('themeMode', newMode);
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  const setSystemTheme = () => {
    try {
      setThemeMode('system');
      const systemDark = getSystemTheme();
      setIsDarkMode(systemDark);
      localStorage.setItem('themeMode', 'system');

      // Immediately apply to DOM
      applyThemeToDOM(systemDark);
    } catch (error) {
      console.error('Error setting system theme:', error);
    }
  };

  const setLightTheme = () => {
    try {
      setThemeMode('light');
      setIsDarkMode(false);
      localStorage.setItem('themeMode', 'light');

      // Immediately apply to DOM
      applyThemeToDOM(false);
    } catch (error) {
      console.error('Error setting light theme:', error);
    }
  };

  const setDarkTheme = () => {
    try {
      setThemeMode('dark');
      setIsDarkMode(true);
      localStorage.setItem('themeMode', 'dark');

      // Immediately apply to DOM
      applyThemeToDOM(true);
    } catch (error) {
      console.error('Error setting dark theme:', error);
    }
  };

  // Helper function to apply theme to DOM immediately
  const applyThemeToDOM = (isDark) => {
    const root = document.documentElement;
    const body = document.body;

    if (isDark) {
      root.classList.add('dark-theme');
      root.classList.remove('light-theme');
      body.classList.add('dark-theme');
      body.classList.remove('light-theme');
      root.setAttribute('data-theme', 'dark');
    } else {
      root.classList.add('light-theme');
      root.classList.remove('dark-theme');
      body.classList.add('light-theme');
      body.classList.remove('dark-theme');
      root.setAttribute('data-theme', 'light');
    }
  };

  // Theme colors object
  const theme = {
    colors: {
      // Background colors
      background: isDarkMode ? '#121212' : '#ffffff',
      backgroundSecondary: isDarkMode ? '#1e1e1e' : '#f8f9fa',
      surface: isDarkMode ? '#1e1e1e' : '#ffffff',
      
      // Text colors
      text: isDarkMode ? '#ffffff' : '#000000',
      textSecondary: isDarkMode ? '#a0a0a0' : '#666666',
      textMuted: isDarkMode ? '#888888' : '#6c757d',
      
      // Border colors
      border: isDarkMode ? '#333333' : '#dee2e6',
      borderLight: isDarkMode ? '#404040' : '#e9ecef',
      
      // Brand colors (consistent across themes)
      primary: '#198754',
      secondary: '#6c757d',
      success: '#198754',
      danger: '#dc3545',
      warning: '#ffc107',
      info: '#0dcaf0',
      
      // Navigation colors
      navBackground: isDarkMode ? '#1a1a1a' : '#ffffff',
      navText: isDarkMode ? '#ffffff' : '#000000',
      navHover: isDarkMode ? '#333333' : '#f8f9fa',
      
      // Card colors - Theme responsive
      cardBackground: isDarkMode ? '#1e1e1e' : '#ffffff',
      cardBorder: isDarkMode ? '#ffffff' : '#dee2e6',
      cardText: isDarkMode ? '#ffffff' : '#000000',
      
      // Sidebar colors - ALWAYS DARK (as per requirement)
      sidebarBackground: '#343a40', // Always dark
      sidebarText: '#ffffff', // Always white text
      sidebarHover: '#495057', // Always dark hover
      
      // Input colors
      inputBackground: isDarkMode ? '#2d2d2d' : '#ffffff',
      inputBorder: isDarkMode ? '#404040' : '#ced4da',
      inputText: isDarkMode ? '#ffffff' : '#495057',
      
      // Shadow
      shadow: isDarkMode ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.1)',
    }
  };

  const getThemeIcon = (mode) => {
    switch (mode) {
      case 'system':
        return '🖥️';
      case 'light':
        return '☀️';
      case 'dark':
        return '🌙';
      default:
        return '🖥️';
    }
  };

  const getThemeLabel = (mode) => {
    switch (mode) {
      case 'system':
        return 'System';
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      default:
        return 'System';
    }
  };

  // Force sync function that can be called manually
  const forceSync = () => {
    const isDOMDark = document.documentElement.classList.contains('dark-theme');
    const savedMode = localStorage.getItem('themeMode');

    console.log('Force sync - DOM:', isDOMDark, 'Saved:', savedMode, 'Current:', themeMode);

    if (savedMode) {
      if (savedMode === 'dark' && !isDarkMode) {
        setThemeMode('dark');
        setIsDarkMode(true);
      } else if (savedMode === 'light' && isDarkMode) {
        setThemeMode('light');
        setIsDarkMode(false);
      } else if (savedMode === 'system') {
        const systemDark = getSystemTheme();
        setThemeMode('system');
        setIsDarkMode(systemDark);
      }
    } else {
      // No saved preference, sync with DOM or use system
      if (isDOMDark) {
        setThemeMode('dark');
        setIsDarkMode(true);
        localStorage.setItem('themeMode', 'dark');
      } else {
        setThemeMode('system');
        setIsDarkMode(getSystemTheme());
        localStorage.setItem('themeMode', 'system');
      }
    }
  };

  return (
    <ThemeContext.Provider value={{
      isDarkMode,
      themeMode,
      toggleTheme,
      setSystemTheme,
      setLightTheme,
      setDarkTheme,
      theme,
      getThemeIcon,
      getThemeLabel,
      forceSync
    }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use theme context
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
