import React, { useState, useEffect } from "react";
import {
  Con<PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  Badge,
  Button,
  Modal,
} from "react-bootstrap";
import {
  FaCalendarAlt,
  FaClock,
  FaMapMarkerAlt,
  FaUsers,
  FaTimes,
  FaEye,
} from "react-icons/fa";
import { useDispatch, useSelector } from "react-redux";
import { unsubscribeEvents } from "../redux/slice/eventSlice";
import { toast } from "react-hot-toast";
import { useNavigate } from "react-router-dom";

const SubscribedEvents = ({ showTitle = true, maxEvents = null }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { unsubscribeLoading } = useSelector((state) => state.events);
  
  const [subscribedEvents, setSubscribedEvents] = useState([]);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [showModal, setShowModal] = useState(false);

  // Dummy subscribed events data
  const dummySubscribedEvents = [
    {
      id: 1,
      title: "Annual Science Fair 2024",
      description: "Join us for an exciting showcase of innovative science projects and experiments from students across all departments.",
      date: "2024-03-15",
      time: "10:00 AM",
      location: "Main Auditorium",
      category: "academic",
      attendees: 250,
      image: "https://via.placeholder.com/400x200/007bff/ffffff?text=Science+Fair",
      status: "upcoming",
      organizer: "Science Department",
      registrationRequired: true,
      maxAttendees: 300,
      tags: ["science", "innovation", "students"]
    },
    {
      id: 4,
      title: "Sports Championship",
      description: "Annual inter-department sports competition featuring cricket, football, basketball, and athletics.",
      date: "2024-04-05",
      time: "8:00 AM",
      location: "Sports Complex",
      category: "sports",
      attendees: 320,
      image: "https://via.placeholder.com/400x200/ffc107/000000?text=Sports+Championship",
      status: "upcoming",
      organizer: "Sports Committee",
      registrationRequired: true,
      maxAttendees: 400,
      tags: ["sports", "competition", "athletics"]
    },
    {
      id: 5,
      title: "Career Guidance Workshop",
      description: "Professional development workshop with industry experts sharing insights on career opportunities and skill development.",
      date: "2024-03-10",
      time: "2:00 PM",
      location: "Seminar Hall",
      category: "professional",
      attendees: 95,
      image: "https://via.placeholder.com/400x200/6f42c1/ffffff?text=Career+Workshop",
      status: "upcoming",
      organizer: "Placement Cell",
      registrationRequired: true,
      maxAttendees: 100,
      tags: ["career", "professional", "skills"]
    },
    {
      id: 7,
      title: "Mathematics Olympiad 2024",
      description: "Test your mathematical skills in this challenging competition featuring problems from various mathematical domains.",
      date: "2024-04-12",
      time: "9:00 AM",
      location: "Mathematics Department",
      category: "academic",
      attendees: 120,
      image: "https://via.placeholder.com/400x200/007bff/ffffff?text=Math+Olympiad",
      status: "upcoming",
      organizer: "Mathematics Department",
      registrationRequired: true,
      maxAttendees: 150,
      tags: ["mathematics", "competition", "academic"]
    },
    {
      id: 11,
      title: "Entrepreneurship Summit",
      description: "Learn from successful entrepreneurs and startup founders about building and scaling businesses.",
      date: "2024-03-05",
      time: "10:00 AM",
      location: "Business Center",
      category: "professional",
      attendees: 180,
      image: "https://via.placeholder.com/400x200/6f42c1/ffffff?text=Entrepreneurship+Summit",
      status: "upcoming",
      organizer: "Business Department",
      registrationRequired: true,
      maxAttendees: 200,
      tags: ["entrepreneurship", "business", "startup"]
    }
  ];

  useEffect(() => {
    // Load dummy subscribed events
    const eventsToShow = maxEvents ? dummySubscribedEvents.slice(0, maxEvents) : dummySubscribedEvents;
    setSubscribedEvents(eventsToShow);
  }, [maxEvents]);

  const handleUnsubscribe = async (eventId) => {
    try {
      const result = await dispatch(unsubscribeEvents({ eventId }));
      if (result.meta.requestStatus === 'fulfilled') {
        // Remove from local state since we're using dummy data
        setSubscribedEvents(prev => prev.filter(event => event.id !== eventId));
        toast.success('Successfully unsubscribed from event!');
      } else {
        toast.error('Failed to unsubscribe from event');
      }
    } catch (error) {
      toast.error('An error occurred while unsubscribing');
    }
  };

  const handleShowModal = (event) => {
    setSelectedEvent(event);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedEvent(null);
  };

  const getCategoryColor = (category) => {
    const colors = {
      academic: "primary",
      cultural: "success", 
      technology: "danger",
      sports: "warning",
      professional: "info",
      networking: "dark"
    };
    return colors[category] || "secondary";
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (subscribedEvents.length === 0) {
    return (
      <Card className="text-center py-5">
        <Card.Body>
          <FaCalendarAlt size={64} className="text-muted mb-3" />
          <h5 className="text-muted">No Subscribed Events</h5>
          <p className="text-muted">
            You haven't subscribed to any events yet. Visit the Events page to subscribe to upcoming events.
          </p>
          <Button 
            variant="success" 
            onClick={() => navigate('/events')}
            className="mt-2"
          >
            Browse Events
          </Button>
        </Card.Body>
      </Card>
    );
  }

  return (
    <>
      {showTitle && (
        <h2 className="text-start mb-4 text-success">
          <FaCalendarAlt className="me-2" />
          My Subscribed Events
        </h2>
      )}
      
      <Row>
        {subscribedEvents.map((event) => (
          <Col md={6} lg={4} key={event.id} className="mb-4">
            <Card className="h-100 shadow-sm border-0">
              <Card.Img
                variant="top"
                src={event.image}
                style={{ height: "200px", objectFit: "cover" }}
              />
              <Card.Body className="d-flex flex-column">
                <div className="d-flex justify-content-between align-items-start mb-2">
                  <Badge bg={getCategoryColor(event.category)} className="mb-2">
                    {event.category.toUpperCase()}
                  </Badge>
                  <Badge bg="success">SUBSCRIBED</Badge>
                </div>

                <Card.Title className="h6 mb-2">{event.title}</Card.Title>
                <Card.Text className="text-muted small flex-grow-1">
                  {event.description.length > 80
                    ? `${event.description.substring(0, 80)}...`
                    : event.description}
                </Card.Text>

                <div className="mb-3">
                  <div className="d-flex align-items-center mb-1">
                    <FaCalendarAlt className="text-muted me-2" size={12} />
                    <small>{formatDate(event.date)}</small>
                  </div>
                  <div className="d-flex align-items-center mb-1">
                    <FaClock className="text-muted me-2" size={12} />
                    <small>{event.time}</small>
                  </div>
                  <div className="d-flex align-items-center mb-1">
                    <FaMapMarkerAlt className="text-muted me-2" size={12} />
                    <small>{event.location}</small>
                  </div>
                  <div className="d-flex align-items-center">
                    <FaUsers className="text-muted me-2" size={12} />
                    <small>{event.attendees}/{event.maxAttendees} attendees</small>
                  </div>
                </div>

                <div className="d-flex gap-2 mt-auto">
                  <Button
                    variant="outline-primary"
                    size="sm"
                    className="flex-fill"
                    onClick={() => handleShowModal(event)}
                  >
                    <FaEye className="me-1" />
                    View
                  </Button>
                  <Button
                    variant="outline-danger"
                    size="sm"
                    className="flex-fill"
                    onClick={() => handleUnsubscribe(event.id)}
                    disabled={unsubscribeLoading}
                  >
                    <FaTimes className="me-1" />
                    {unsubscribeLoading ? 'Removing...' : 'Unsubscribe'}
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Event Details Modal */}
      <Modal show={showModal} onHide={handleCloseModal} size="lg" centered>
        <Modal.Header closeButton>
          <Modal.Title>{selectedEvent?.title}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedEvent && (
            <>
              <img
                src={selectedEvent.image}
                className="img-fluid mb-3 rounded"
                style={{ width: "100%", height: "250px", objectFit: "cover" }}
                alt={selectedEvent.title}
              />

              <div className="d-flex justify-content-between align-items-center mb-3">
                <Badge bg={getCategoryColor(selectedEvent.category)} className="fs-6">
                  {selectedEvent.category.toUpperCase()}
                </Badge>
                <Badge bg="success" className="fs-6">SUBSCRIBED</Badge>
              </div>

              <p className="mb-3">{selectedEvent.description}</p>

              <Row className="mb-3">
                <Col md={6}>
                  <div className="d-flex align-items-center mb-2">
                    <FaCalendarAlt className="me-2 text-success" />
                    <strong>Date:</strong>
                    <span className="ms-2">{formatDate(selectedEvent.date)}</span>
                  </div>
                  <div className="d-flex align-items-center mb-2">
                    <FaClock className="me-2 text-success" />
                    <strong>Time:</strong>
                    <span className="ms-2">{selectedEvent.time}</span>
                  </div>
                </Col>
                <Col md={6}>
                  <div className="d-flex align-items-center mb-2">
                    <FaMapMarkerAlt className="me-2 text-success" />
                    <strong>Location:</strong>
                    <span className="ms-2">{selectedEvent.location}</span>
                  </div>
                  <div className="d-flex align-items-center mb-2">
                    <FaUsers className="me-2 text-success" />
                    <strong>Attendees:</strong>
                    <span className="ms-2">{selectedEvent.attendees}/{selectedEvent.maxAttendees}</span>
                  </div>
                </Col>
              </Row>

              <div className="mb-3">
                <strong>Organizer:</strong>
                <span className="ms-2">{selectedEvent.organizer}</span>
              </div>

              <div className="mb-3">
                <strong>Tags:</strong>
                <div className="mt-2">
                  {selectedEvent.tags?.map((tag, index) => (
                    <Badge key={index} bg="light" text="dark" className="me-2 mb-1">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseModal}>
            Close
          </Button>
          <Button 
            variant="outline-danger" 
            onClick={() => {
              handleUnsubscribe(selectedEvent.id);
              handleCloseModal();
            }}
            disabled={unsubscribeLoading}
          >
            <FaTimes className="me-2" />
            Unsubscribe from Event
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default SubscribedEvents;
