/* Theme CSS Variables */

/* Light Theme (Default) */
:root,
.light-theme {
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --bg-surface: #ffffff;
  --bg-overlay: rgba(0, 0, 0, 0.1);

  /* Text Colors */
  --text-primary: #000000;
  --text-secondary: #666666;
  --text-muted: #6c757d;
  --text-inverse: #ffffff;

  /* Border Colors */
  --border-primary: #dee2e6;
  --border-secondary: #e9ecef;
  --border-light: #f8f9fa;
  --border-dark: #adb5bd;

  /* Brand Colors */
  --color-primary: #198754;
  --color-primary-hover: #157347;
  --color-secondary: #6c757d;
  --color-success: #198754;
  --color-danger: #dc3545;
  --color-warning: #ffc107;
  --color-info: #0dcaf0;

  /* Navigation Colors */
  --nav-bg: #ffffff;
  --nav-text: #000000;
  --nav-hover: #f8f9fa;
  --nav-active: #e9ecef;
  --nav-border: #dee2e6;

  /* Card Colors */
  --card-bg: #ffffff;
  --card-border: #dee2e6;
  --card-text: #000000;
  --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);

  /* Sidebar Colors - ALWAYS DARK */
  --sidebar-bg: #343a40;
  --sidebar-text: #ffffff;
  --sidebar-hover: #495057;
  --sidebar-border: #495057;

  /* Input Colors */
  --input-bg: #ffffff;
  --input-border: #ced4da;
  --input-text: #495057;
  --input-placeholder: #6c757d;

  /* Button Colors */
  --btn-primary-bg: #198754;
  --btn-primary-border: #198754;

  /* Dropdown Colors */
  --dropdown-bg: #ffffff;
  --dropdown-border: #dee2e6;
  --dropdown-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
  --dropdown-item-hover: #f8f9fa;

  /* Modal Colors */
  --modal-bg: #ffffff;
  --modal-backdrop: rgba(0, 0, 0, 0.5);
}

/* Dark Theme */
.dark-theme {
  /* Background Colors */
  --bg-primary: #121212;
  --bg-secondary: #1e1e1e;
  --bg-tertiary: #2d2d2d;
  --bg-surface: #1e1e1e;
  --bg-overlay: rgba(255, 255, 255, 0.1);

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #a0a0a0;
  --text-muted: #888888;
  --text-inverse: #000000;

  /* Border Colors */
  --border-primary: #333333;
  --border-secondary: #404040;
  --border-light: #555555;
  --border-dark: #222222;

  /* Brand Colors (keeping consistent) */
  --color-primary: #198754;
  --color-primary-hover: #20c997;
  --color-secondary: #6c757d;
  --color-success: #198754;
  --color-danger: #dc3545;
  --color-warning: #ffc107;
  --color-info: #0dcaf0;

  /* Navigation Colors */
  --nav-bg: #1a1a1a;
  --nav-text: #ffffff;
  --nav-hover: #333333;
  --nav-active: #404040;
  --nav-border: #333333;

  /* Card Colors - Dark Theme */
  --card-bg: #1e1e1e;
  --card-border: #ffffff;
  --card-text: #ffffff;
  --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);

  /* Sidebar Colors - ALWAYS DARK (OVERRIDE) */
  --sidebar-bg: #343a40 !important;
  --sidebar-text: #ffffff !important;
  --sidebar-hover: #495057 !important;
  --sidebar-border: #495057 !important;

  /* Input Colors */
  --input-bg: #2d2d2d;
  --input-border: #404040;
  --input-text: #ffffff;
  --input-placeholder: #888888;

  /* Button Colors */
  --btn-primary-bg: #198754;
  --btn-primary-border: #198754;

  /* Dropdown Colors */
  --dropdown-bg: #1e1e1e;
  --dropdown-border: #333333;
  --dropdown-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4);
  --dropdown-item-hover: #333333;

  /* Modal Colors */
  --modal-bg: #1e1e1e;
  --modal-backdrop: rgba(0, 0, 0, 0.7);
}

/* Base styles using CSS variables */
html {
  background-color: var(--bg-primary);
  transition: background-color 0.3s ease;
}

body {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Global container and layout styles */
#root {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  min-height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Ensure all major layout elements inherit theme */
.container,
.container-fluid,
.row,
.col {
  color: var(--text-primary);
}

/* Global text elements */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary) !important;
}

p, span, div {
  color: inherit;
}

/* Navigation styles */
.navbar {
  background-color: var(--nav-bg) !important;
  border-bottom: 1px solid var(--nav-border);
  transition: background-color 0.3s ease;
}

.navbar .navbar-brand,
.navbar .nav-link {
  color: var(--nav-text) !important;
  transition: color 0.3s ease;
}

.navbar .nav-link:hover {
  background-color: var(--nav-hover);
  border-radius: 0.375rem;
}

/* CARD STYLES - THEME RESPONSIVE */
.card {
  background-color: var(--card-bg) !important;
  border: 1px solid var(--card-border) !important;
  color: var(--card-text) !important;
  box-shadow: var(--card-shadow) !important;
  transition: all 0.3s ease;
}

.card .card-body,
.card .card-header,
.card .card-footer {
  background-color: var(--card-bg) !important;
  color: var(--card-text) !important;
}

.card h1, .card h2, .card h3, .card h4, .card h5, .card h6 {
  color: var(--card-text) !important;
}

.card p, .card span, .card div {
  color: var(--card-text) !important;
}

.card .text-muted {
  color: var(--text-muted) !important;
}

.card .text-secondary {
  color: var(--text-secondary) !important;
}

/* Ensure card links are visible */
.card a {
  color: var(--color-primary) !important;
}

.card a:hover {
  color: var(--color-primary-hover) !important;
}

/* Dark mode specific card styling with white borders */
.dark-theme .card {
  border: 1px solid #ffffff !important;
  background-color: #1e1e1e !important;
  color: #ffffff !important;
}

.dark-theme .card .card-body,
.dark-theme .card .card-header,
.dark-theme .card .card-footer {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
  border-color: #ffffff !important;
}

/* Ensure all card elements have white borders in dark mode */
.dark-theme .card-header {
  border-bottom: 1px solid #ffffff !important;
}

.dark-theme .card-footer {
  border-top: 1px solid #ffffff !important;
}

/* Override any existing border styles for cards in dark mode */
.dark-theme .card.border-0 {
  border: 1px solid #ffffff !important;
}

.dark-theme .card.border-1 {
  border: 1px solid #ffffff !important;
}

.dark-theme .card.border-2 {
  border: 1px solid #ffffff !important;
}

/* Specific styling for custom card classes - NO white borders for accordions */
.dark-theme .custom-accordion {
  border: 1px solid #333333 !important;
}

.dark-theme .shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(255, 255, 255, 0.1) !important;
}

/* SIDEBAR STYLES - ALWAYS DARK MODE */
.sidebar,
.custom-offcanvas,
.offcanvas {
  background-color: var(--sidebar-bg) !important;
  color: var(--sidebar-text) !important;
  border-color: var(--sidebar-border) !important;
}

.sidebar .nav-link,
.custom-offcanvas .nav-link,
.offcanvas .nav-link {
  color: var(--sidebar-text) !important;
}

.sidebar .nav-link:hover,
.custom-offcanvas .nav-link:hover,
.offcanvas .nav-link:hover {
  background-color: var(--sidebar-hover) !important;
  color: var(--sidebar-text) !important;
}

.sidebar h1, .sidebar h2, .sidebar h3, .sidebar h4, .sidebar h5, .sidebar h6,
.custom-offcanvas h1, .custom-offcanvas h2, .custom-offcanvas h3, .custom-offcanvas h4, .custom-offcanvas h5, .custom-offcanvas h6,
.offcanvas h1, .offcanvas h2, .offcanvas h3, .offcanvas h4, .offcanvas h5, .offcanvas h6 {
  color: var(--sidebar-text) !important;
}

.sidebar p, .sidebar span, .sidebar div,
.custom-offcanvas p, .custom-offcanvas span, .custom-offcanvas div,
.offcanvas p, .offcanvas span, .offcanvas div {
  color: var(--sidebar-text) !important;
}

/* Button styles */
.btn-primary {
  background-color: var(--btn-primary-bg);
  border-color: var(--btn-primary-border);
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

/* Input styles */
.form-control {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--input-text);
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

.form-control::placeholder {
  color: var(--input-placeholder);
}

.form-control:focus {
  background-color: var(--input-bg);
  border-color: var(--color-primary);
  color: var(--input-text);
}

/* Dropdown styles */
.dropdown-menu {
  background-color: var(--dropdown-bg);
  border-color: var(--dropdown-border);
  box-shadow: var(--dropdown-shadow);
}

.dropdown-item {
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.dropdown-item:hover {
  background-color: var(--dropdown-item-hover);
  color: var(--text-primary);
}

/* Dark mode dropdown overrides */
.dark-theme .dropdown-menu {
  background-color: #1e1e1e !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4) !important;
}

.dark-theme .dropdown-item {
  color: #ffffff !important;
}

.dark-theme .dropdown-item:hover,
.dark-theme .dropdown-item:focus {
  background-color: #333333 !important;
  color: #ffffff !important;
}

.dark-theme .dropdown-item.active {
  background-color: #198754 !important;
  color: #ffffff !important;
}

/* Profile icon border theming */
.dark-theme .rounded-circle.border {
  border-color: #ffffff !important;
}

/* Navbar dropdown toggle styling */
.dark-theme .dropdown-toggle {
  color: #ffffff !important;
  border: none !important;
}

.dark-theme .dropdown-toggle:focus {
  box-shadow: none !important;
}

/* Ensure navbar dropdowns work properly in dark mode */
.dark-theme .navbar .dropdown-menu {
  background-color: #1e1e1e !important;
  border: 1px solid #333333 !important;
}

.dark-theme .navbar .dropdown-item {
  color: #ffffff !important;
}

.dark-theme .navbar .dropdown-item:hover {
  background-color: #333333 !important;
  color: #ffffff !important;
}

.dark-theme .navbar .dropdown-divider {
  border-color: #333333 !important;
}

.dark-theme .navbar .dropdown-header {
  color: #a0a0a0 !important;
}

/* Specific heading that should remain dark in all themes */
.dark-theme #exams h2 {
  color: #000000 !important;
}

/* Exam info section styling - keep background light and text dark */
.dark-theme #exams {
  color: #000000 !important;
}

.dark-theme #exams .container {
  color: #000000 !important;
}

.dark-theme #exams h2,
.dark-theme #exams h3,
.dark-theme #exams h4,
.dark-theme #exams h5,
.dark-theme #exams h6 {
  color: #000000 !important;
}

/* Modal styles */
.modal-content {
  background-color: var(--modal-bg);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

.modal-backdrop {
  background-color: var(--modal-backdrop);
}

/* Dark mode modal overrides */
.dark-theme .modal-content {
  background-color: #1e1e1e !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

.dark-theme .modal-header {
  background-color: #1e1e1e !important;
  border-bottom: 1px solid #333333 !important;
  color: #ffffff !important;
}

.dark-theme .modal-body {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
}

.dark-theme .modal-footer {
  background-color: #1e1e1e !important;
  border-top: 1px solid #333333 !important;
  color: #ffffff !important;
}

.dark-theme .modal-title {
  color: #ffffff !important;
}

.dark-theme .btn-close {
  filter: invert(1) !important;
}

/* Forgot password link styling */
.dark-theme .btn-link {
  color: #888888 !important;
}

.dark-theme .btn-link:hover {
  color: #aaaaaa !important;
}

/* Theme transition animations */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Utility classes */
.theme-bg-primary {
  background-color: var(--bg-primary);
}

.theme-bg-secondary {
  background-color: var(--bg-secondary);
}

.theme-text-primary {
  color: var(--text-primary);
}

.theme-text-secondary {
  color: var(--text-secondary);
}

.theme-border {
  border-color: var(--border-primary);
}

/* Theme toggle button styles */
.theme-toggle-btn {
  background: none;
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.theme-toggle-btn:hover {
  background-color: var(--nav-hover);
  border-color: var(--border-dark);
}

.theme-dropdown-menu {
  background-color: var(--dropdown-bg);
  border: 1px solid var(--dropdown-border);
  box-shadow: var(--dropdown-shadow);
  border-radius: 0.375rem;
  padding: 0.5rem 0;
  min-width: 150px;
}

.theme-dropdown-item {
  padding: 0.5rem 1rem;
  color: var(--text-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.3s ease;
}

.theme-dropdown-item:hover {
  background-color: var(--dropdown-item-hover);
}

.theme-dropdown-item.active {
  background-color: var(--color-primary);
  color: white;
}

.theme-dropdown-item .checkmark {
  margin-left: auto;
  color: var(--color-success);
}

/* Bootstrap component overrides for dark theme */
.dark-theme .navbar-light .navbar-nav .nav-link {
  color: var(--nav-text) !important;
}

.dark-theme .navbar-light .navbar-nav .nav-link:hover {
  color: var(--nav-text) !important;
  background-color: var(--nav-hover);
}

.dark-theme .navbar-light .navbar-brand {
  color: var(--nav-text) !important;
}

.dark-theme .btn-outline-success {
  color: var(--color-success);
  border-color: var(--color-success);
}

.dark-theme .btn-outline-success:hover {
  background-color: var(--color-success);
  border-color: var(--color-success);
  color: white;
}

.dark-theme .btn-outline-primary {
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.dark-theme .btn-outline-primary:hover {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
}

.dark-theme .btn-outline-secondary {
  color: var(--text-secondary);
  border-color: var(--border-primary);
}

.dark-theme .btn-outline-secondary:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--border-secondary);
  color: var(--text-primary);
}

/* Alert component overrides */
.dark-theme .alert {
  background-color: var(--bg-tertiary);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

.dark-theme .alert-info {
  background-color: #1e3a5f;
  border-color: #2e5984;
  color: #b8daff;
}

/* Table theming */
.dark-theme table {
  background-color: var(--bg-surface);
  color: var(--text-primary);
}

.dark-theme th,
.dark-theme td {
  border-color: var(--border-primary);
  color: inherit;
}

.dark-theme .table-striped tbody tr:nth-of-type(odd) {
  background-color: var(--bg-tertiary);
}

/* SweetAlert2 theming */
.dark-theme .swal2-popup {
  background-color: var(--bg-surface) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border-primary) !important;
}

.dark-theme .swal2-title {
  color: var(--text-primary) !important;
}

.dark-theme .swal2-content {
  color: var(--text-secondary) !important;
}

.dark-theme .swal2-confirm {
  background-color: var(--color-danger) !important;
  border-color: var(--color-danger) !important;
}

.dark-theme .swal2-cancel {
  background-color: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
}

.dark-theme .swal2-backdrop {
  background-color: rgba(0, 0, 0, 0.7) !important;
}

/* Google Login button theming */
.dark-theme [data-testid="google-login-button"] {
  filter: brightness(0.8);
}

.dark-theme .google-login-button,
.dark-theme [role="button"][aria-label*="Google"] {
  filter: brightness(0.9) !important;
}

/* Footer theming */
.dark-theme footer {
  background-color: #1a1a1a !important;
  color: #ffffff !important;
}

.dark-theme footer .list-group-item {
  background-color: transparent !important;
  color: #ffffff !important;
  border: none !important;
}

.dark-theme footer .footer-link {
  background-color: transparent !important;
  color: #ffffff !important;
  border: none !important;
}

.dark-theme footer .footer-link:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
}

.dark-theme footer h5 {
  color: #ffffff !important;
}

/* Override any remaining white backgrounds */
.dark-theme .bg-white:not(.sidebar):not(.custom-offcanvas):not(.offcanvas) {
  background-color: var(--bg-surface) !important;
  color: var(--text-primary) !important;
}

.dark-theme .bg-light:not(.sidebar):not(.custom-offcanvas):not(.offcanvas) {
  background-color: var(--bg-secondary) !important;
}

.dark-theme .text-dark:not(.sidebar *):not(.custom-offcanvas *):not(.offcanvas *) {
  color: var(--text-primary) !important;
}

.dark-theme .text-muted:not(.sidebar *):not(.custom-offcanvas *):not(.offcanvas *) {
  color: var(--text-muted) !important;
}

/* Ensure all text in cards is visible */
.dark-theme .card .text-dark {
  color: var(--card-text) !important;
}

.dark-theme .card .text-muted {
  color: var(--text-muted) !important;
}

/* Skeleton loading theming for cards */
.dark-theme .card .react-loading-skeleton {
  background-color: var(--bg-tertiary) !important;
}

/* Ensure card titles and text are properly themed */
.card .card-title {
  color: var(--card-text) !important;
}

.card .card-text {
  color: var(--card-text) !important;
}

/* Achievement and feature cards */
.dark-theme .bg-info.bg-opacity-10 {
  background-color: rgba(13, 202, 240, 0.2) !important;
}

.dark-theme .bg-success.bg-opacity-10 {
  background-color: rgba(25, 135, 84, 0.2) !important;
}

.dark-theme .bg-danger.bg-opacity-10 {
  background-color: rgba(220, 53, 69, 0.2) !important;
}

.dark-theme .bg-primary.bg-opacity-10 {
  background-color: rgba(13, 110, 253, 0.2) !important;
}

.dark-theme .bg-warning.bg-opacity-10 {
  background-color: rgba(255, 193, 7, 0.2) !important;
}

/* Accordion theming for FAQ section */
.accordion {
  --bs-accordion-bg: var(--card-bg);
  --bs-accordion-border-color: var(--border-primary);
  --bs-accordion-border-radius: 0.5rem;
}

/* Dark mode accordion variables */
.dark-theme .accordion {
  --bs-accordion-border-color: #333333;
}

.accordion-item {
  background-color: var(--card-bg) !important;
  border-color: var(--border-primary) !important;
  color: var(--card-text) !important;
  margin-bottom: 0.75rem;
  border-radius: 0.5rem !important;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Dark mode accordion items - NO white borders */
.dark-theme .accordion-item {
  border: 1px solid #333333 !important;
  background-color: #1e1e1e !important;
  color: #ffffff !important;
}

.accordion-header {
  background-color: var(--card-bg) !important;
}

.accordion-button {
  background-color: var(--card-bg) !important;
  color: var(--card-text) !important;
  border: none !important;
  border-radius: 0.5rem !important;
  font-weight: 500;
  padding: 1rem 1.25rem;
  transition: all 0.3s ease;
}

.accordion-button:not(.collapsed) {
  background-color: var(--card-bg) !important;
  color: var(--card-text) !important;
  box-shadow: none !important;
  border-bottom: 1px solid var(--card-border) !important;
}

/* Dark mode accordion button borders */
.dark-theme .accordion-button:not(.collapsed) {
  border-bottom: 1px solid #333333 !important;
}

.accordion-button:focus {
  box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25) !important;
  border-color: var(--color-success) !important;
}

.accordion-button::after {
  filter: var(--card-text) !important;
  transition: transform 0.3s ease;
}

.dark-theme .accordion-button::after {
  filter: invert(1) !important;
}

.accordion-body {
  background-color: var(--card-bg) !important;
  color: var(--card-text) !important;
  border-top: 1px solid var(--card-border) !important;
  padding: 1.25rem;
  line-height: 1.6;
}

/* Dark mode accordion body borders */
.dark-theme .accordion-body {
  border-top: 1px solid #333333 !important;
}

.accordion-collapse {
  background-color: var(--card-bg) !important;
}

/* Custom accordion styling */
.custom-accordion {
  border: 1px solid var(--card-border) !important;
  border-radius: 0.5rem !important;
  margin-bottom: 0.75rem !important;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease;
}

.dark-theme .custom-accordion {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
}

.custom-accordion:hover {
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.dark-theme .custom-accordion:hover {
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.4);
}

/* FAQ section container theming */
#FAQs {
  padding: 2rem 0;
  background-color: var(--bg-secondary);
  border-radius: 1rem;
  margin: 2rem 0;
}

.dark-theme #FAQs {
  background-color: var(--bg-secondary);
}

/* Accordion button states */
.accordion-button:hover {
  background-color: var(--nav-hover) !important;
  color: var(--card-text) !important;
}

.accordion-button:active {
  background-color: var(--nav-active) !important;
  color: var(--card-text) !important;
}

/* Ensure accordion text is always visible */
.accordion-item * {
  color: var(--card-text) !important;
}

.accordion-header button {
  color: var(--card-text) !important;
}

.accordion-body p,
.accordion-body span,
.accordion-body div {
  color: var(--card-text) !important;
}

/* Fix accordion collapse animation */
.accordion-collapse {
  border-color: var(--card-border) !important;
}

/* Responsive FAQ styling */
@media (max-width: 768px) {
  #FAQs {
    margin: 1rem;
    padding: 1rem;
    border-radius: 0.75rem;
  }

  .accordion-button {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .accordion-body {
    padding: 1rem;
    font-size: 0.9rem;
  }
}
