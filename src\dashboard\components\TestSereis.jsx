import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Button, Pagination } from "react-bootstrap";
import { GiNotebook } from "react-icons/gi";
import { FaClock, <PERSON>aEye, FaPercentage, FaPlus } from "react-icons/fa";
import { LuLanguages } from "react-icons/lu";
import { getTestSeries } from "../../redux/slice/dashboardSlice";
import { useDispatch } from "react-redux";
import Skeleton from "react-loading-skeleton";
import { useNavigate, useLocation } from "react-router-dom";
import toast from "react-hot-toast";

const TestSeriesSection = () => {
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 9;
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [addTestLoading, setAddTestLoading] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { hash } = useLocation(); // Get the hash from URL

  useEffect(() => {
    if (hash) {
      const element = document.querySelector(hash);
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      }
    }
  }, [hash]); // Run effect when hash changes

  useEffect(() => {
    const fetchTests = async () => {
      setLoading(true);
      try {
        const response = await dispatch(getTestSeries());
        console.log(response);
        
        if (response?.payload) {
          setData(response?.payload?.data);
        } else {
          setError("Failed to fetch test series");
        }
      } catch (err) {
        setError(err.message || "Something went wrong");
      } finally {
        setLoading(false);
      }
    };

    fetchTests();
  }, []);

  useEffect(() => {
    if (data?.length > 0) {
      const filtered = data.filter((test) =>
        test?.course?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredData(filtered);
    }
  }, [searchQuery, data]);

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const isMobileView = window.innerWidth <= 768;

  const handleAddTestSeries = async(slug) => {
    // setAddTestLoading(true);
    // try {
    //   const res = await dispatch(AddTestSeries(slug));
    // if(res.meta.requestStatus === 'fulfilled'){
    //   toast.success("Test series added successfully");
    // }else{
    //   toast.error("Error in adding test series");
    // }
    // } catch (error) {
    //   console.log(error);
    //   toast.error("Error in adding test series");
    // }finally{
    //   setAddTestLoading(false);
    // }
  }

  const handleHorizontalScroll = (e) => {
    if (isMobileView) {
      e.preventDefault();
      e.stopPropagation(); // Prevent page scroll
      const container = e.currentTarget;
      container.scrollLeft += e.deltaY;
    }
  };

  const renderPaginationItems = () => {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    const maxVisiblePages = 5; // Maximum number of visible page numbers
    const pages = [];

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(
          <Pagination.Item
            key={i}
            active={i === currentPage}
            onClick={() => handlePageChange(i)}
          >
            {i}
          </Pagination.Item>
        );
      }
    } else {
      const startPage = Math.max(1, currentPage - 2);
      const endPage = Math.min(totalPages, currentPage + 2);

      if (startPage > 1) {
        pages.push(
          <Pagination.Item key={1} onClick={() => handlePageChange(1)}>
            1
          </Pagination.Item>
        );
        if (startPage > 2) {
          pages.push(<Pagination.Ellipsis key="start-ellipsis" />);
        }
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(
          <Pagination.Item
            key={i}
            active={i === currentPage}
            onClick={() => handlePageChange(i)}
          >
            {i}
          </Pagination.Item>
        );
      }

      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push(<Pagination.Ellipsis key="end-ellipsis" />);
        }
        pages.push(
          <Pagination.Item
            key={totalPages}
            onClick={() => handlePageChange(totalPages)}
          >
            {totalPages}
          </Pagination.Item>
        );
      }
    }

    return pages;
  };

  return (
    <Container className="mt-5" id="test-series">
      <Row className="justify-content-center">
        <Col md={10}>
          <h4>
            Test Series & <span className="text-success">Free</span> Quizzes
          </h4>
          <input
            type="text"
            className="form-control my-3 rounded-5"
            placeholder="Search Test Series..."
            value={searchQuery}
            onChange={handleSearch}
            style={{ maxWidth: "300px" }}
          />
        </Col>
      </Row>
      <Row className="justify-content-center">
        <Col md={10}>
          <div
            className={`d-flex ${isMobileView ? "flex-nowrap overflow-auto" : "flex-wrap"} gap-3`}
            style={{ scrollBehavior: "smooth" }}
            onWheel={handleHorizontalScroll}
          >
          {
            loading ? (
              <>
              {
              Array.from({ length: 4 }).map((_, index) => (
                  <Card
                    key={index}
                    style={{ width: "20rem", flex: "0 0 auto" }}
                    className="shadow-sm p-2"
                  >
                    {/* Header Skeleton */}
                    <div className="p-2">
                      <Skeleton height={25} width="50%" baseColor="#e6ffe6" highlightColor="#c4f7c4"/>
                    </div>

                    {/* Body Skeleton */}
                    <Card.Body>
                      <Card.Title className="fs-6">
                        <Skeleton width="80%"  baseColor="#e6ffe6" highlightColor="#c4f7c4"/>
                      </Card.Title>
                      <div className="d-flex justify-content-start mb-2 gap-2">
                        <Skeleton width={60} height={20} baseColor="#e6ffe6" highlightColor="#c4f7c4"/>
                        <Skeleton width={60} height={20} baseColor="#e6ffe6" highlightColor="#c4f7c4"/>
                      </div>
                      <Skeleton width="100%" height={35} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
                    </Card.Body>

                    {/* Footer Skeleton */}
                    <div className="p-2 bg-light d-flex align-items-center justify-content-between">
                      <Skeleton width="40%" height={15} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
                      <Skeleton width="20%" height={15} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
                    </div>
                  </Card>
                ))
              }
              </>
            ) : (
              <>
                {paginatedData.length > 0 ? (
                  paginatedData.map((test, index) => (
                    <Card
                      key={index} // Use index as the key
                      style={{ width: "20rem", flex: "0 0 auto" }}
                      className="shadow-sm p-2"
                    >
                      <div className="d-flex justify-content-start gap-4 align-items-center p-2 text-white rounded-top">
                        <span className="bg-success px-2 py-1 rounded d-flex align-items-center fw-semibold fs-6">
                          <GiNotebook className="me-2" />
                          {test?.course}
                        </span>
                        <span
                          className={`${
                            test?.paid ? "bg-danger text-white" : "bg-success"
                          } py-1 px-3 rounded fw-semibold`}
                        >
                          {test?.paid ? "Paid" : "Free"}
                        </span>
                      </div>

                      <Card.Body>
                        <Card.Title className="fs-6">
                          {test?.sub_course_name}
                          {/* {test.paper_details.length > 1 &&
                            ` - Test ${paperIndex + 1}`} */}
                        </Card.Title>
                        {/* <div className="d-flex justify-content-start mb-2 gap-2">
                          <small className="d-flex align-items-center gap-1 fw-semibold">
                            <FaClock /> {paper.duration}hr
                          </small>
                          <small className="d-flex align-items-center gap-1 fw-semibold">
                            <FaPercentage /> {paper.total_marks} marks
                          </small>
                        </div> */}
                        <div className="d-flex justify-content-between align-items-center">
                        <small>{test?.paper_details?.length > 0 ? `${test?.paper_details.length} Tests` : '0 Tests'}</small>
                          {/* <button className="btn btn-outline-success btn-sm">
                            {test.paid ? "Unlock" : "Start Now"}
                          </button> */}
                        </div>
                        <div className="d-flex align-items-center gap-3">
                        <Button variant="success" className="w-75 btn-sm py-1 mt-4 d-flex align-items-center justify-content-center gap-2" disabled={addTestLoading} onClick={() => handleAddTestSeries(test?.subcourse_slug)}>{addTestLoading ? 'Adding..' : (<><FaPlus/> Add Test Series</>)}</Button>
                        <Button variant="outline-success" className="w-25 btn-sm py-1 mt-4 d-flex align-items-center justify-content-center gap-2" onClick={() => navigate(`/dashboard/test-series/${test?.subcourse_slug}`)}><FaEye /> View</Button>
                        </div>
                      </Card.Body>

                      <div className="p-2 bg-light d-flex align-items-center justify-content-between">
                        {/* <small>
                          <a
                            href="#!"
                            className="text-success text-decoration-none"
                          >
                            Syllabus
                          </a>
                        </small> */}
                        <small>
                          <span className="ms-2">
                            <LuLanguages /> {test?.language}
                          </span>
                        </small>
                      </div>
                    </Card>
                  ))
                ) : (
                  <p>No test series found.</p>
                )}
              </>
            )
          }
            
          </div>
          {!isMobileView && filteredData?.length > itemsPerPage && (
            <Pagination className="mt-4 justify-content-center">
              <Pagination.Prev
                disabled={currentPage === 1}
                onClick={() => handlePageChange(currentPage - 1)}
              >
                Previous
              </Pagination.Prev>
              {renderPaginationItems()}
              <Pagination.Next
                disabled={currentPage === Math.ceil(filteredData.length / itemsPerPage)}
                onClick={() => handlePageChange(currentPage + 1)}
              >
                Next
              </Pagination.Next>
            </Pagination>
          )}
        </Col>
      </Row>
    </Container>
  );
};

export default TestSeriesSection;
