import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col } from "react-bootstrap";
import Results from "./Results";
import testData from "../../dummyData/testData";
import TestNavbar from "./TestNavbar";
import QuestionNavigation from "./QuestionNavigation";
import { FaLock, FaLockOpen } from "react-icons/fa";
import QuestionHeader from "./QuestionHeader";

const TestStarted2 = () => {
  const [timeLeft, setTimeLeft] = useState(20 * 60); // 20 minutes in seconds
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [timer, setTimer] = useState(0);
  const [language, setLanguage] = useState("English");
  const [testComplete, setTestComplete] = useState(false); 
  const [showTestComplete, setShowTestComplete] = useState(false);
  const currentSection = testData.sections[currentSectionIndex];
  const currentQuestion = currentSection.questions[currentQuestionIndex];
  const [questionStatuses, setQuestionStatuses] = useState({
    answered: 0,
    notAttempted: 0,
    notVisited: 0,
  });

  useEffect(() => {
    setTimer(0); 
    const interval = setInterval(() => {
      setTimer((prev) => prev + 1);
    }, 1000);
    return () => clearInterval(interval);
  }, [currentQuestionIndex]);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes}:${secs < 10 ? "0" : ""}${secs}`;
  };

  const handleSelectAnswer = (qId, answer) => {
    setSelectedAnswers({ ...selectedAnswers, [qId]: answer });
  };

  const handleSelectQuestion = (qIndex) => {
    setCurrentQuestionIndex(qIndex);
  };

  const handleSubmitSection = () => {
    let answeredCount = 0;
    let notAttemptedCount = 0;
    let notVisitedCount = 0;

    currentSection.questions.forEach((question, index) => {
      if (selectedAnswers[question.id]) {
        answeredCount++;
      } else if (
        selectedAnswers[question.id] === undefined &&
        index < currentQuestionIndex
      ) {
        notAttemptedCount++;
      } else {
        notVisitedCount++;
      }
    });

    setQuestionStatuses({
      answered: answeredCount,
      notAttempted: notAttemptedCount,
      notVisited: notVisitedCount,
    });
    setShowModal(true);

    if (currentSectionIndex === testData.sections.length - 1) {
      setShowTestComplete(true);
    }
  };

  const confirmSubmitSection = () => {
    setTimeLeft(20 * 60);
    setShowModal(false);
    if (currentSectionIndex < testData.sections.length - 1) {
      setCurrentSectionIndex(currentSectionIndex + 1);
      setCurrentQuestionIndex(0);
    }
  };

  const handleSubmitTest = () => {
    setShowTestComplete(true);
  };

  return (
    <>
      <Container fluid>
        {testComplete ? (
          <Results testData={testData} selectedAnswers={selectedAnswers} />
        ) : (
          <>
            <TestNavbar timeLeft={timeLeft} setTimeLeft={setTimeLeft} />
            <Row>
              {/* Left Column: Single Question Display */}
              <Col
                md={9}
                className="border-end d-flex flex-column"
                style={{ maxHeight: "calc(100vh - 50px)", overflowY: "auto" }}
              >
                <QuestionHeader
                  timer={timer}
                  language={language}
                  setLanguage={setLanguage}
                  currentQuestionIndex={currentQuestionIndex}
                />

                {/* Question Display */}
                <div className="border p-3 mb-4">
                  <h5>
                    <u>Question</u>
                  </h5>
                  {/* Check if question has sub-questions */}
                  {currentQuestion.subQuestions ? (
                    <Row>
                      <Col md={6} className="border-3 border-end">
                        <h5>{currentQuestion.text}</h5>
                        <div className="mt-3">
                          {currentQuestion.description}
                        </div>
                      </Col>

                      <Col md={6}>
                        <div className="mt-3 ps-md-3">
                          {currentQuestion.subQuestions.map((subQuestion) => (
                            <div key={subQuestion.text} className="mb-3">
                              <h6>{subQuestion.text}</h6>
                              {subQuestion.options.map((option) => (
                                <div key={option} className="mb-2">
                                  <input
                                    type="radio"
                                    id={option}
                                    name={`sub-question-${subQuestion.text}`}
                                    value={option}
                                    checked={
                                      selectedAnswers[subQuestion.text] ===
                                      option
                                    }
                                    onChange={() =>
                                      handleSelectAnswer(
                                        subQuestion.text,
                                        option
                                      )
                                    }
                                  />
                                  <label htmlFor={option} className="ms-2">
                                    {option}
                                  </label>
                                </div>
                              ))}
                            </div>
                          ))}
                        </div>
                      </Col>
                    </Row>
                  ) : (
                    <div className="mt-3">
                      <Col md={6}>
                        <h5>{currentQuestion.text}</h5>
                        <div className="mt-3">
                          {currentQuestion.description}
                        </div>
                      </Col>
                      {/* Single question without sub-questions */}
                      {currentQuestion.options.map((option) => (
                        <div key={option} className="mb-2">
                          <input
                            type="radio"
                            id={option}
                            name={`question-${currentQuestion.id}`}
                            value={option}
                            checked={
                              selectedAnswers[currentQuestion.id] === option
                            }
                            onChange={() =>
                              handleSelectAnswer(currentQuestion.id, option)
                            }
                          />
                          <label htmlFor={option} className="ms-2">
                            {option}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                <QuestionNavigation
                  currentQuestionIndex={currentQuestionIndex}
                  setCurrentQuestionIndex={setCurrentQuestionIndex}
                  currentSection={currentSection}
                />
              </Col>

              {/* Right Column: Section Navigation & Submit Buttons */}
              <Col
                md={3}
                className="py-4 d-flex flex-column justify-content-between bg-success bg-opacity-10 col-responsive"
              >
                <div>
                  {testData.sections.map((section, index) => (
                    <div key={index} className={`mb-3`}>
                      <div className="w-100 bg-success bg-opacity-25 p-2 d-flex justify-content-between">
                        <span className="fw-bold">Section: {section.name}</span>
                        <div className="d-flex align-items-center gap-2">
                          {index === currentSectionIndex ? (
                            <>
                              <FaLockOpen color="green" size={18} />
                              <span>{formatTime(timeLeft)}</span>
                            </>
                          ) : (
                            <>
                              <FaLock color="gray" size={18} />
                              <span>{section.duration}</span>
                            </>
                          )}
                        </div>
                      </div>
                      {index === currentSectionIndex && (
                        <div className="d-flex flex-wrap mt-2 gap-3">
                          {section.questions.map((q, qIndex) => {
                            // Determine the button background color based on the question status
                            let buttonVariant = "outline-secondary"; // Default (Unvisited)
                            if (qIndex === currentQuestionIndex) {
                              buttonVariant = "secondary"; // Active question
                            } else if (selectedAnswers[q.id]) {
                              buttonVariant = "success"; // Answered question
                            } else if (qIndex < currentQuestionIndex) {
                              buttonVariant = "danger"; // Unanswered but visited question
                            }

                            return (
                              <Button
                                key={q.id}
                                variant={buttonVariant}
                                className="m-1"
                                onClick={() => handleSelectQuestion(qIndex)}
                              >
                                {q.id}
                              </Button>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Buttons */}
                <div className="d-grid gap-3">
                  <Button
                    variant="success"
                    className="w-100 mt-3"
                    onClick={handleSubmitSection}
                  >
                    Submit Section
                  </Button>
                  <Button
                    variant="dark"
                    className="w-100 mt-3"
                    onClick={handleSubmitTest}
                  >
                    Submit Test
                  </Button>
                </div>
              </Col>
            </Row>

            {/* Modal for Section Submission */}
            <Modal show={showModal} onHide={() => setShowModal(false)} centered>
              <Modal.Header closeButton>
                <Modal.Title>Submit {currentSection.name}?</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <div>
                  <p>Questions Answered: {questionStatuses.answered}</p>
                  <p>
                    Questions Not Attempted: {questionStatuses.notAttempted}
                  </p>
                  <p>Questions Not Visited: {questionStatuses.notVisited}</p>
                </div>
              </Modal.Body>
              <Modal.Footer>
                <Button variant="secondary" onClick={() => setShowModal(false)}>
                  Cancel
                </Button>
                <Button variant="success" onClick={confirmSubmitSection}>
                  Submit
                </Button>
              </Modal.Footer>
            </Modal>
            {/* Modal for Test Submission */}
            <Modal
              show={showTestComplete}
              onHide={() => setShowTestComplete(false)}
              centered
            >
              <Modal.Header closeButton>
                <Modal.Title>Submit Test ?</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <div>
                  <p>Questions Answered: {questionStatuses.answered}</p>
                  <p>
                    Questions Not Attempted: {questionStatuses.notAttempted}
                  </p>
                  <p>Questions Not Visited: {questionStatuses.notVisited}</p>
                </div>
              </Modal.Body>
              <Modal.Footer>
                <Button variant="success" onClick={() => setTestComplete(true)}>
                  Submit
                </Button>
              </Modal.Footer>
            </Modal>
          </>
        )}
      </Container>
    </>
  );
};

export default TestStarted2;
