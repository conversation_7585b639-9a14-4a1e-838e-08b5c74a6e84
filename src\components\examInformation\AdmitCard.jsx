import React from "react";
import { Button, Container } from "react-bootstrap";
import { FaDownload } from "react-icons/fa";
import { FaRightLong } from "react-icons/fa6";


const AdmitCard = ({examinfo}) => {
    const officialUrl = "/demo.pdf"; 
    const pdfUrl = "/demo.pdf"; 
  return (
    <Container className="mt-4">
    <div className="d-flex justify-content-between align-items-center mb-3">
      <h3 className="">Admit Card</h3>
      <Button
        variant="success"
        className=""
        as="a"
        target="_blank"
        href={officialUrl}
      >
        Go to Official webiste
        <FaRightLong className="ms-2" />
      </Button>
      </div>

      {/* PDF Viewer */}
      <div className="scrollbar overflow-y-auto">
      <iframe
        src={`${pdfUrl}#toolbar=0`}
        width="100%"
        height="600px"
        className="rounded-3"
        // style={{ border: "1px solid #ccc" }}
        title="Syllabus PDF"
      ></iframe>
      </div>

      {/* Download Button */}
    </Container>
  );
};

export default AdmitCard;
