import React from "react";
import { Container, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { <PERSON> } from "react-router-dom";
import { useTheme } from "../../context/ThemeContext";

const BlogSection = () => {
  const { isDarkMode, theme } = useTheme();

  return (
    <Container className="pt-5" id="blogs">
      <div className="d-flex justify-content-between align-items-center pt-4">
        <div>
          <h2 className="text-success" style={{ color: theme.colors.text }}>
            <span className="text-success">Blogs</span>
          </h2>
        </div>
        <div>
        <Link
          to="https://blog.shashtrarth.com/"
          className="btn btn-outline-success">
          Explore
        </Link>
      </div>
      </div>      

      <Row className="justify-content-center py-3">
        <Col md={12}>
          <Row>
            <Col md={3}>
              <Card className="bg-warning bg-opacity-10 p-3 m-2 border-0 shadow-sm text-center d-flex justify-content-center align-items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth="1.5"
                  stroke="currentColor"
                  width="40px"
                  height="40px">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"
                  />
                </svg>
                <h5 className="mt-3">Latest Blogs</h5>
                {/* <Link to="/blogs" className="btn btn-outline-success">
                    Explore
                  </Link> */}
              </Card>
            </Col>
            <Col md={3}>
              <Card className="bg-info bg-opacity-10 p-3 m-2 border-0 shadow-sm text-center d-flex justify-content-center align-items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth="1.5"
                  stroke="currentColor"
                  width="40px"
                  height="40px">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M9 17.25v1.007a3 3 0 0 1-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0 1 15 18.257V17.25m6-12V15a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 15V5.25m18 0A2.25 2.25 0 0 0 18.75 3H5.25A2.25 2.25 0 0 0 3 5.25m18 0V12a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 12V5.25"
                  />
                </svg>

                <h5 className="mt-3">Technology</h5>
                {/* <Link to="/blogs" className="btn btn-outline-success">
                    Explore
                  </Link> */}
              </Card>
            </Col>
            <Col md={3}>
              <Card className="bg-danger bg-opacity-10 p-3 m-2 border-0 shadow-sm text-center d-flex justify-content-center align-items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth="1.5"
                  stroke="currentColor"
                  width="40px"
                  height="40px">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"
                  />
                </svg>

                <h5 className="mt-3">Current Affairs</h5>
                {/* <Link to="/blogs" className="btn btn-outline-success">
                    Explore
                  </Link> */}
              </Card>
            </Col>
            <Col md={3}>
              <Card className="bg-primary bg-opacity-10 p-3 m-2 border-0 shadow-sm text-center d-flex justify-content-center align-items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth="1.5"
                  stroke="currentColor"
                  width="40px"
                  height="40px">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"
                  />
                </svg>

                <h5 className="mt-3">Others</h5>
                {/* <Link to="/blogs" className="btn btn-outline-success">
                    Explore
                  </Link> */}
              </Card>
            </Col>
          </Row>
        </Col>
      </Row>
    </Container>
  );
};

export default BlogSection;
