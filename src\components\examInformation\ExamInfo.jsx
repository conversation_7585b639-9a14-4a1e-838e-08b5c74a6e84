import React from "react";
import examInfo from "../../dummyData/examInfo";
import {
  Breadcrumb,
  Button,
  Col,
  Container,
  Image,
  Row,
} from "react-bootstrap";
import { FaClock, FaDownload } from "react-icons/fa";
import PassBox from "../../commonCompoenents/PassBox";
import FeatureSection from "./FeatureSection";

const ExamInfo = ({Component}) => {
  return (
    <>
      <Container
        className=""
      >
        <Row className="justify-content-center pt-5"
        style={{
          backgroundImage:
            "linear-gradient(to top, rgba(252, 252, 252, 0.87) 10%, rgba(209, 241, 221, 0.57) 90%)",
        }}>
          <Col md={10}>
            <Breadcrumb>
              <Breadcrumb.Item href="#">Home</Breadcrumb.Item>
              <Breadcrumb.Item href="#">{examInfo?.exam?.name}</Breadcrumb.Item>
            </Breadcrumb>
            <div className="d-flex flex-md-row flex-column justify-content-md-between align-items-md-start align-items-center">
              <div className="d-flex align-items-center gap-2">
                <Image src="/examLogo.webp" width={55} />
                <h2 className="fw-normal">{examInfo?.exam?.name}</h2>
              </div>
              <Button variant="outline-success" className="px-5 py-2 align-items-center">
              <FaDownload className="me-2" />Download PDF
              </Button>
            </div>
            <p className="text-secondary mt-2 small d-flex align-items-center justify-content-md-start justify-content-center">
              <FaClock className="me-1" /> Last updated on:{" "}
              {examInfo?.exam.lastUpdated}
            </p>
          </Col>
        </Row>
        <Row className="justify-content-center">
          <Col md={10}>
        <FeatureSection/>
          </Col>
        </Row>
      
      <Row className="justify-content-center bg-light py-3">
        <Col md={7}>
          {/* <Overview examInfo={examInfo}/> */}
          {Component ? <Component examInfo={examInfo} /> : null}
        </Col>
        <Col md={3}>
          <br></br>
          <PassBox />
          
        </Col>
      </Row>
      </Container>
    </>
  );
};

export default ExamInfo;
