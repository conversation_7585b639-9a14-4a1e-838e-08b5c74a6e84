import React, {useEffect} from 'react';
import { useLocation } from 'react-router-dom';
import { Container, Row, Col, Accordion } from 'react-bootstrap';
import data from '../../dummyData/testSeries';
import { useTheme } from '../../context/ThemeContext';

const FAQSection = () => {
    const { isDarkMode, theme } = useTheme();
    const { hash } = useLocation(); // Get the hash from URL
  
    useEffect(() => {
      if (hash) {
        const element = document.querySelector(hash);
        if (element) {
          element.scrollIntoView({ behavior: "smooth" });
        }
      }
    }, [hash]); // Run effect when hash changes

const faqData = [
    {
        question: "What is Shashtrarth?",
        answer: "Shashtrarth is an online exam portal designed to help students prepare for various competitive exams through mock tests, study materials, and performance analysis."
    },
    {
        question: "Which competitive exams can I prepare for on Shashtrarth?",
        answer: "Shashtrarth provides preparation resources for exams like UPSC, SSC, Banking, Railways, State PSCs, and other government job exams."
    },
    {
        question: "How can I register on Shashtrarth?",
        answer: "You can register on Shashtrarth by visiting our website, signing up with your email or mobile number, and setting up your profile."
    },
    {
        question: "Does Shashtrarth provide free mock tests?",
        answer: "Yes, Shashtrarth offers free mock tests for various competitive exams, along with premium test series for in-depth preparation."
    },
    {
        question: "Can I track my progress on Shashtrarth?",
        answer: "Yes, our portal provides detailed performance analysis, including accuracy, speed, and subject-wise strengths and weaknesses."
    },
    // {
    //     question: "Is there a mobile app for Shashtrarth?",
    //     answer: "Currently, Shashtrarth is available as a web platform, but we are working on launching a mobile app soon."
    // },
    {
        question: "How are the mock tests structured on Shashtrarth?",
        answer: "Mock tests on Shashtrarth follow the latest exam pattern, covering all sections with time-bound practice and instant results."
    },
    // {
    //     question: "Can I download study materials from Shashtrarth?",
    //     answer: "Yes, we provide downloadable PDFs, notes, and other study materials to support your exam preparation."
    // },
    // {
    //     question: "Does Shashtrarth provide explanations for answers?",
    //     answer: "Yes, after each test, you get detailed explanations for correct answers to help you understand concepts better."
    // },
    {
        question: "How can I contact Shashtrarth for support?",
        answer: "You can reach out to our support team through the contact form on our website or email <NAME_EMAIL>."
    }
];


  return (
    <Container
      className="mt-5 py-4"
      id="FAQs"
      style={{
        backgroundColor: theme.colors.backgroundSecondary,
        borderRadius: '1rem',
        margin: '2rem auto',
        transition: 'background-color 0.3s ease'
      }}
    >
      <Row className="justify-content-center">
        <Col md={10}>
          <h4
            className="mb-4"
            style={{ color: theme.colors.text }}
          >
            Shashtrath <span className='text-success'> - FAQs</span>
          </h4>
          {/* <h4 className="mb-4">All Railway Exams 2025 - <span className='text-success'>FAQs</span></h4> */}
          <Accordion defaultActiveKey="0">
            {faqData.map((faq, index) => (
              <Accordion.Item
                eventKey={index.toString()}
                key={index}
                className="custom-accordion mb-3"
                style={{
                  backgroundColor: theme.colors.cardBackground,
                  borderColor: isDarkMode ? '#333333' : theme.colors.cardBorder,
                  border: `1px solid ${isDarkMode ? '#333333' : theme.colors.cardBorder}`,
                  borderRadius: '0.5rem',
                  overflow: 'hidden',
                  transition: 'all 0.3s ease'
                }}
              >
                <Accordion.Header
                  style={{
                    backgroundColor: theme.colors.cardBackground,
                    color: theme.colors.cardText,
                    borderBottom: `1px solid ${isDarkMode ? '#333333' : theme.colors.cardBorder}`
                  }}
                >
                  <span style={{ color: theme.colors.cardText, fontWeight: '500' }}>
                    {faq.question}
                  </span>
                </Accordion.Header>
                <Accordion.Body
                  style={{
                    backgroundColor: theme.colors.cardBackground,
                    color: theme.colors.cardText,
                    borderTop: `1px solid ${isDarkMode ? '#333333' : theme.colors.cardBorder}`
                  }}
                >
                  {faq.answer}
                </Accordion.Body>
              </Accordion.Item>
            ))}
          </Accordion>
        </Col>
      </Row>
    </Container>
  );
};

export default FAQSection;
