import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper to get auth token
const getAuthToken = (getState) => {
  const state = getState();
  return {
    accessToken: state.student.student["JWT_Token"].access,
  };
};

// Get All Scratch Cards
export const getAllScratchCards = createAsyncThunk(
  'reward/getAllScratchCards',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      const headers = {
        Authorization: `Bearer ${accessToken}`,
      };
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_ALL_SCRATCH_CARDS}`,
        { headers }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch scratch cards');
    }
  }
);

// Scratch a Card
export const scratchScratchCard = createAsyncThunk(
  'reward/scratchScratchCard',
  async (cardId, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      const headers = {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      };
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SCRATCH_CARD}`,
        { card_id: cardId },
        { headers }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to scratch card');
    }
  }
);

// Get Wallet Data
export const getWalletData = createAsyncThunk(
  'reward/getWalletData',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      const headers = {
        Authorization: `Bearer ${accessToken}`,
      };
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_WALLET_DATA}`,
        { headers }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch wallet data');
    }
  }
);

// Raise Withdrawal Request
export const raiseWithdrawalRequest = createAsyncThunk(
  'reward/raiseWithdrawalRequest',
  async (amount, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      const headers = {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      };
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_RAISE_WITHDRAWAL_REQUEST}`,
         amount,
        { headers }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to raise withdrawal request');
    }
  }
);

const rewardSlice = createSlice({
  name: 'reward',
  initialState: {
    loading: false,
    error: null,
  },
  reducers: {
    resetScratchedResult: (state) => {
      state.scratchedResult = null;
    },
    resetWithdrawalStatus: (state) => {
      state.withdrawalStatus = null;
    },
  },
  extraReducers: (builder) => {
    builder

      // Get All Scratch Cards
      .addCase(getAllScratchCards.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllScratchCards.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(getAllScratchCards.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Scratch a Scratch Card
      .addCase(scratchScratchCard.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(scratchScratchCard.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(scratchScratchCard.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get Wallet Data
      .addCase(getWalletData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getWalletData.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(getWalletData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Raise Withdrawal Request
      .addCase(raiseWithdrawalRequest.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(raiseWithdrawalRequest.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(raiseWithdrawalRequest.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { resetScratchedResult, resetWithdrawalStatus } = rewardSlice.actions;
export default rewardSlice.reducer;
