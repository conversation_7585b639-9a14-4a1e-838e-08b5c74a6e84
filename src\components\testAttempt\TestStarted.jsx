import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> } from "react-bootstrap";
import Results from "../../dashboard/pages/Results";
import TestNavbar from "./TestNavbar";
import QuestionNavigation from "./QuestionNavigation";
import { FaLock, FaLockOpen } from "react-icons/fa";
import QuestionHeader from "./QuestionHeader";
import { useNavigate } from "react-router-dom";

const TestStarted = ({ testData2 }) => {
  const [timeLeft, setTimeLeft] = useState(testData2?.sections[0]?.section_time * 60); // 30 minutes in seconds
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [timer, setTimer] = useState(0);
  const [language, setLanguage] = useState("English");
  const [testComplete, setTestComplete] = useState(false);
  const [showTestComplete, setShowTestComplete] = useState(false);
  const currentSection = testData2?.sections[currentSectionIndex];
  const currentQuestion = currentSection?.questions[currentQuestionIndex];
  const [questionStatuses, setQuestionStatuses] = useState({
    answered: 0,
    notAttempted: 0,
    notVisited: 0,
  });

  const navigate = useNavigate();

  // question timer (how much time to answer the question)
  useEffect(() => {
    setTimer(0);
    const interval = setInterval(() => {
      setTimer((prev) => prev + 1);
    }, 1000);
    return () => clearInterval(interval);
  }, [currentQuestionIndex]);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes}:${secs < 10 ? "0" : ""}${secs}`;
  };

  const handleSelectAnswer = (qId, answer) => {
    const updatedAnswers = [
      ...selectedAnswers.filter((ans) => ans.question_id !== qId),
      {
        section: currentSection?.section_name,
        question_id: qId,
        selected_option: answer,
        marks: currentSection?.marks,
        negative_marks: currentSection?.negative_marks,
      },
    ];
    setSelectedAnswers(updatedAnswers);
  };

  const handleSelectQuestion = (qIndex) => {
    setCurrentQuestionIndex(qIndex);
  };

  const handleSubmitSection = () => {
    let answeredCount = 0;
    let notAttemptedCount = 0;
    let notVisitedCount = 0;

    currentSection?.questions.forEach((question, index) => {
      const answer = selectedAnswers.find((ans) => ans?.question_id === question?.question_id);
      if (answer) {
        answeredCount++;
      } else if (!answer && index < currentQuestionIndex) {
        notAttemptedCount++;
      } else {
        notVisitedCount++;
      }
    });

    setQuestionStatuses({
      answered: answeredCount,
      notAttempted: notAttemptedCount,
      notVisited: notVisitedCount,
    });
    setShowModal(true);

    if (currentSectionIndex === testData2?.sections.length - 1) {
      setShowTestComplete(true);
    }
  };

  const confirmSubmitSection = () => {
    setShowModal(false);
    if (currentSectionIndex < testData2?.sections.length - 1) {
      setCurrentSectionIndex(currentSectionIndex + 1);
      setCurrentQuestionIndex(0);
      setTimer(0);
      setTimeLeft(testData2?.sections[currentSectionIndex + 1]?.section_time * 60);
    }
  };

  const handleSubmitTest = () => {
    localStorage.setItem("testData", JSON.stringify(selectedAnswers));
    setShowTestComplete(true);
  };

  if (testComplete) {
    navigate("/test-result");
  }

  return (
    <Container fluid>
      <>
        <TestNavbar timeLeft={timeLeft} setTimeLeft={setTimeLeft} autoSectionSubmit={confirmSubmitSection} />
        <Row>
          <Col
            md={9}
            className="border-end d-flex flex-column"
            style={{ maxHeight: "calc(100vh - 50px)", overflowY: "auto" }}
          >
            <QuestionHeader
              timer={timer}
              language={language}
              setLanguage={setLanguage}
              currentQuestionIndex={currentQuestionIndex}
              sectionMarks={{ rightMarks: currentSection?.questions[0]?.marks, negativeMarks: currentSection?.questions[0]?.negative_marks }}
            />

            <div className="border p-3 mb-4">
              <h5>
                <u>Question</u>
              </h5>
              {currentQuestion?.subQuestions ? (
                <Row>
                  <Col md={6} className="border-3 border-end">
                    <h5>{currentQuestion?.content}</h5>
                    <div className="mt-3">{currentQuestion?.description}</div>
                  </Col>
                  <Col md={6}>
                    <div className="mt-3 ps-md-3">
                      {currentQuestion?.subQuestions.map((subQuestion) => (
                        <div key={subQuestion?.content} className="mb-3">
                          <h6>{subQuestion?.content}</h6>
                          {subQuestion?.options.map((option) => (
                            <div key={option?.option_id} className="mb-2">
                              <input
                                type="radio"
                                id={option?.option_id}
                                name={`sub-question-${subQuestion?.content}`}
                                value={option?.option_id}
                                checked={
                                  selectedAnswers.find((ans) => ans.question_id === subQuestion?.content)?.selected_option === option?.option_id
                                }
                                onChange={() => handleSelectAnswer(subQuestion?.content, option?.option_id)}
                              />
                              <label htmlFor={option?.option_id} className="ms-2">
                                {option?.option_text}
                              </label>
                            </div>
                          ))}
                        </div>
                      ))}
                    </div>
                  </Col>
                </Row>
              ) : (
                <div className="mt-3">
                  <Col md={6}>
                    <h5>{currentQuestion?.content}</h5>
                    <div className="mt-3">{currentQuestion?.description ? currentQuestion?.description : null}</div>
                  </Col>
                  {currentQuestion?.options.map((option) => (
                    <div key={option?.option_id} className="mb-2">
                      <input
                        type="radio"
                        id={option?.option_id}
                        name={`question-${currentQuestion?.question_id}`}
                        value={option?.option_id}
                        checked={
                          selectedAnswers.find((ans) => ans.question_id === currentQuestion?.question_id)?.selected_option === option?.option_id
                        }
                        onChange={() => handleSelectAnswer(currentQuestion?.question_id, option?.option_id)}
                      />
                      <label htmlFor={option?.option_id} className="ms-2">
                        {option?.option_text}
                      </label>
                    </div>
                  ))}
                </div>
              )}
            </div>
            <QuestionNavigation
              currentQuestionIndex={currentQuestionIndex}
              setCurrentQuestionIndex={setCurrentQuestionIndex}
              currentSection={currentSection}
            />
          </Col>

          <Col
            md={3}
            className="py-4 d-flex flex-column justify-content-between bg-success bg-opacity-10 col-responsive"
          >
            <div>
              {testData2?.sections.map((section, index) => (
                <div key={section?.section_name} className={`mb-3`}>
                  <div className="w-100 bg-success bg-opacity-25 p-2 d-flex justify-content-between">
                    <span className="fw-bold">Section: {section?.section_name}</span>
                    <div className="d-flex align-items-center gap-2">
                      {index === currentSectionIndex ? (
                        <>
                          <FaLockOpen color="green" size={18} />
                          <span>{formatTime(timeLeft)}</span>
                        </>
                      ) : (
                        <>
                          <FaLock color="gray" size={18} />
                          <span>{section?.section_time}</span>
                        </>
                      )}
                    </div>
                  </div>
                  {index === currentSectionIndex && (
                    <div className="d-flex flex-wrap mt-2 gap-3">
                      {section?.questions.map((q, qIndex) => {
                        let buttonVariant = "outline-secondary";
                        if (qIndex === currentQuestionIndex) {
                          buttonVariant = "secondary";
                        } else if (selectedAnswers[q.question_id]) {
                          buttonVariant = "success";
                        } else if (qIndex < currentQuestionIndex) {
                          buttonVariant = "danger";
                        }

                        return (
                          <Button
                            key={q?.question_id}
                            variant={buttonVariant}
                            className="m-1"
                            onClick={() => handleSelectQuestion(qIndex)}
                          >
                            {qIndex + 1}
                          </Button>
                        );
                      })}
                    </div>
                  )}
                </div>
              ))}
            </div>

            <div className="d-grid gap-3">
              <Button variant="success" className="w-100 mt-3" onClick={handleSubmitSection}>
                Submit Section
              </Button>
              <Button variant="dark" className="w-100 mt-3" onClick={handleSubmitTest}>
                Submit Test
              </Button>
            </div>
          </Col>
        </Row>

        <Modal show={showModal} onHide={() => setShowModal(false)} centered>
          <Modal.Header closeButton>
            <Modal.Title>Submit {currentSection?.section_name}?</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div>
              <p>Questions Answered: {questionStatuses.answered}</p>
              <p>Questions Not Attempted: {questionStatuses.notAttempted}</p>
              <p>Questions Not Visited: {questionStatuses.notVisited}</p>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowModal(false)}>
              Cancel
            </Button>
            <Button variant="success" onClick={confirmSubmitSection}>
              Submit
            </Button>
          </Modal.Footer>
        </Modal>

        <Modal show={showTestComplete} onHide={() => setShowTestComplete(false)} centered>
          <Modal.Header closeButton>
            <Modal.Title>Submit Test?</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div>
              <p>Questions Answered: {questionStatuses.answered}</p>
              <p>Questions Not Attempted: {questionStatuses.notAttempted}</p>
              <p>Questions Not Visited: {questionStatuses.notVisited}</p>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="success" onClick={() => setTestComplete(true)}>
              Submit
            </Button>
          </Modal.Footer>
        </Modal>
      </>
    </Container>
  );
};

export default TestStarted;