importScripts("https://www.gstatic.com/firebasejs/9.9.0/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/9.9.0/firebase-messaging-compat.js");

// Initialize Firebase App inside the service worker
const firebaseConfig = {
  apiKey: "AIzaSyDXm7EHbpY3YyPfznLb0kusAHQXwgr-Uv4",
  authDomain: "blog-app-3fb57.firebaseapp.com",
  projectId: "blog-app-3fb57",
  storageBucket: "blog-app-3fb57.firebasestorage.app",
  messagingSenderId: "825469322490",
  appId: "1:825469322490:web:4dec36a9e03e057cbbb543",
  measurementId: "G-DM1NY3N8FJ",
};

firebase.initializeApp(firebaseConfig);

const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log(
    "[firebase-messaging-sw.js] Received background message:",
    payload
  );

  const notificationTitle = payload.notification.title;
  const notificationDescription = payload.notification.description;
  const notificationOptions = {
    body: payload.notification.body,
    logo: '',
    description: notificationDescription,
    image: payload.notification.image,
  };

  return self.registration.showNotification(
    notificationTitle,
    notificationOptions
  );
});
