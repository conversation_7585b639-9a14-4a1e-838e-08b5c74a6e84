import React from "react";
// import data from "../../../dummyData/testSeries";
import { Col, Container, Row, Table } from "react-bootstrap";

const AboutTest = ({data}) => {
  // Filter free tests
  const freeTests = data?.Tiers.flatMap((category) => category?.paper).filter((test) => test?.type === "free");

  return (
    <>
      <Container className="mb-5" fluid>
        <Row className="justify-content-center mt-3">
          <Col lg={10} md={10}>
            <h4>About {data?.subcourse_name}</h4>
            <p className="text-secondary mt-4">{data?.subcourse_description}</p>
          </Col>
        </Row>
        <Row className="justify-content-center mt-3">
          <Col lg={10} md={10}>
            <h4>Attempt {data?.subcourse_name} for Free!</h4>
            <p className="text-secondary mt-4">
              Kickstart your preparation with free mock tests that imitate the
              actual SBI PO exam environment. Prepare questions on all sections,
              analyze performance, and enhance your skills for free. So, boost
              your confidence and shine in this exam today with SBI PO test
              series.
            </p>

            {/* Table for Free Tests */}
            <Table striped bordered hover responsive className="">
              <thead className="bg-success text-white">
                <tr>
                  <td className="p-3 fw-semibold" colSpan={8}>
                    
                    {data?.subcourse_name}
                  </td>
                  <td className="p-3 fw-semibold" colSpan={4}>
                    Direct Link to Attempt Mock Test
                  </td>
                </tr>
              </thead>
              <tbody>
                {freeTests ? (
                  freeTests.map((test, index) => (
                    <tr key={index}>
                      <td className="p-3" colSpan={8}>
                        {test?.paper_name}
                      </td>
                      <td className="p-3" colSpan={4}>
                        <a href="#" className="text-success fw-semibold">
                          Attempt Now!
                        </a>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="2" className="text-center">
                      No free tests available.
                    </td>
                  </tr>
                )}
              </tbody>
            </Table>
          </Col>
        </Row>
        <Row className="justify-content-center mt-3">
          <Col lg={10} md={10}>
            <h4>Key Features of {data?.subcourse_name}</h4>
            <p className="text-secondary mt-4">
              SBI PO Online test series prepare the candidates to take up the
              exam in a productive way by simulating real-time test conditions.
              Following are some of the essential features of the SBI PO Mock
              Tests listed below.
            </p>
          </Col>
        </Row>
        {data?.Tiers.map((category) => {
          return (
            <>
              <Row className="justify-content-center mt-3">
                <Col lg={10} md={10}>
                  <h5>{category?.Tier_name}</h5>
                  <p className="text-secondary mt-4">
                    SBI PO Online test series prepare the candidates to take up
                    the exam in a productive way by simulating real-time test
                    conditions. Following are some of the essential features of
                    the SBI PO Mock Tests listed below.
                  </p>
                </Col>
              </Row>
            </>
          );
        })}
        <Row className="justify-content-center mt-3">
          <Col lg={10} md={10}>
            <h4>
              Benefits of Practicing the {data?.subcourse_name} by
              Shashtrarth
            </h4>
            <p className="text-secondary mt-4">
              The SBI PO Online Mock Test free by Shashtrarth is for those who are
              targeting hitting the exam. SBI PO test series designed to create
              a real-time experience of sitting in the SBI PO Exam and offers
              many benefits, which can enhance your practice.{" "}
              <ul>
                <li>
                  Practice Questions: Mock tests of online SBI PO attempt the
                  kind of pattern and levels of difficulties a candidate shall
                  face in his or her exams
                </li>
                <li>
                  Full-Length Tests with Section-Wise: All the subjects include
                  quantitative aptitude and reasoning; hence, the entire
                  syllabus of the complete series is holistically covered
                  through this
                </li>
                <li>
                  Time Management: It improves speed and accuracy, which helps
                  in time management so that the aspirant gives his best in the
                  exam.
                </li>
                <li>
                  Update Content: Current examination trend and syllabus updated
                  to mirror the test as closely as possible to the real SBI PO
                  exam.
                </li>
                <li>
                  Boost Confidence: Practice regularly, reduce exam phobia, and
                  make you easily comfortable with the real exam
                </li>
              </ul>
            </p>
          </Col>
        </Row>
        <Row className="justify-content-center mt-3">
          <Col lg={10} md={10}>
            <h4>How to Attempt the {data?.subcourse_name}?</h4>
            <p className="text-secondary mt-4">
              Try out the SBI PO online test series (Prelims + Mains) online and
              step up your preparation to get good marks in the exam. You just
              have to follow these simple steps to be on your way to securing
              good marks in the SBI PO (Pre + Mains).
              <ul>
                <li>
                  Step 1: First, navigate to the Shashtrarth's Online Test Series
                  Portal. Login credentials are required for all those who have
                  an account on it; otherwise, register new and sign in.
                </li>
                <li>
                  Step 2: Once you login, type "SBI PO (Pre + Mains)" in the
                  search bar. This will lead you straight to the exact mock test
                  series designed for your exams.
                </li>
                <li>
                  Step 3: Now find your choice of free SBI PO mock test (Pre +
                  Mains) and then select "View Test Series
                </li>
                <li>
                  Step 4: When ready to begin, click "Start Now" to initiate the
                  SBI PO Prelims Mock Test. The test will be launched that
                  replicates the actual exam conditions. Take the test and try
                  to complete it within the allotted time to track your
                  performance in real-time.
                </li>
              </ul>
            </p>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default AboutTest;
