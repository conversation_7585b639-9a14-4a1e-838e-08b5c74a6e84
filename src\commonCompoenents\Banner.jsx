import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Carousel, Container, Row, Col } from "react-bootstrap";
import { fetchBanners } from "../redux/slice/bannerSlice";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { useTheme } from "../context/ThemeContext";

const Banner = () => {
  const { isDarkMode, theme } = useTheme();
  const dispatch = useDispatch();
  const [banners, setBanners] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    dispatch(fetchBanners())
      .unwrap()
      .then((data) => {
        setBanners(data);
        setLoading(false);
      })
      .catch((error) => {
        console.error("Error fetching banners:", error);
        setLoading(false);
      });
  }, [dispatch]);

  const [index, setIndex] = useState(0);

  useEffect(() => {
    if (banners.length > 0) {
      const interval = setInterval(() => {
        setIndex((prevIndex) => (prevIndex + 1) % banners.length);
      }, 3000);
      return () => clearInterval(interval);
    }
  }, [banners.length]);

  return (
    <Container fluid>
      <Row
        className="justify-content-center p-md-5 p-2"
        style={{
          backgroundImage:
            "linear-gradient(90deg, rgba(225, 240, 240, 0.3) 10%, rgba(164, 234, 211, 0.3) 90%)",
        }}
      >
        <Col xs={12} md={10} lg={11}>
          {loading ? (
            <Skeleton
              height={300}
              borderRadius="1rem"
              width="100%"
              baseColor={isDarkMode ? "#2d2d2d" : "#e6ffe6"}
              highlightColor={isDarkMode ? "#404040" : "#c4f7c4"}
            />
          ) : banners.length > 0 ? (
            <Carousel activeIndex={index} onSelect={setIndex} controls indicators={false}>
              {banners.map((banner) => (
                <Carousel.Item key={banner.id}>
                  <div
                    className="d-flex justify-content-center align-items-center rounded-4"
                    style={{
                      position: "relative",
                      width: "100%",
                      aspectRatio: "21/9",
                      overflow: "hidden",
                      borderRadius: "1rem",
                    }}
                  >
                    <img
                      className="d-block"
                      src={banner.banner_image}
                      alt={banner.banner_name}
                      style={{
                        width: "100%",
                        height: "auto",
                        objectFit: "cover",
                        transition: "transform 0.5s ease",
                        borderRadius: "1rem",
                      }}
                    />
                  </div>
                </Carousel.Item>
              ))}
            </Carousel>
          ) : (
            <h3 className="text-center text-success">Latest Offers</h3>
          )}
        </Col>
      </Row>
    </Container>
  );
};

export default Banner;
