import React from "react";
import ProgressMeter from "../components/ProgressMeter";
import FriendsProgress from "../components/FriendsProgress";
import YourContacts from "../components/YourContacts";
import { Container, Row, Col } from "react-bootstrap";

const ProgressAndFriends = () => {
  return (
    <Container className="d-flex align-items-center justify-content-center">
      <Row className="justify-content-center w-100">
        {/* Progress Meter Section */}
        <Col lg={10} className="mt-5">
          <ProgressMeter />
        </Col>
        {/* Friends Progress Section */}
        <Col lg={10} className="mt-5">
          <FriendsProgress />
        </Col>
        <Col lg={10} className="mt-4">
          <YourContacts />
        </Col>
      </Row>
    </Container>
  );
};

export default ProgressAndFriends;
