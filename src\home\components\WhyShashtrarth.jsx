import React, { useState, useEffect } from "react";
import { Card, Col, Container, Row } from "react-bootstrap";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { useTheme } from "../../context/ThemeContext";

const features = [
  {
    title: "Real Exam Experience with Mock & Live Tests",
    description:
      "Timed tests, negative marking, and competitive rankings. <PERSON><PERSON><PERSON><PERSON> replicates actual exam conditions with mock tests and live competitions for real-time practice.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth="1.5"
        stroke="currentColor"
        width="30px"
        height="30px"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"
        />
      </svg>
    ),
    color: "text-info bg-info bg-opacity-10",
  },
  {
    title: "Performance Analysis & Explanations",
    description:
      "Track progress, identify strengths, and get clear solutions. Detailed reports highlight accuracy, time management, and subject-wise performance insights.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="30px"
        height="30px"
        viewBox="0 0 24 24"
        stroke="currentColor"
        fill="currentColor"
      >
        <path d="M12 4C9.243 4 7 6.243 7 9h2c0-1.654 1.346-3 3-3s3 1.346 3 3c0 1.069-.454 1.465-1.481 2.255-.382.294-.813.626-1.226 1.038C10.981 13.604 10.995 14.897 11 15v2h2v-2.009c0-.024.023-.601.707-1.284.32-.32.682-.598 1.031-.867C15.798 12.024 17 11.1 17 9c0-2.757-2.243-5-5-5zm-1 14h2v2h-2z"></path>
      </svg>
    ),
    color: "text-danger bg-danger bg-opacity-10",
  },
  {
    title: "Affordable & Flexible Plans",
    description:
      "High-quality test series at student-friendly prices. Get access to premium mock tests, study materials, and live test competitions at minimal cost.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth="1.5"
        stroke="currentColor"
        width="30px"
        height="30px"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"
        />
      </svg>
    ),
    color: "text-warning bg-warning bg-opacity-10",
  },
  {
    title: "Stay Updated with Current Affairs",
    description:
      "Daily current affairs & exclusive test series. Shastrarth provides daily blogs and topic-based quizzes to keep you ahead in your preparation.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth="1.5"
        stroke="currentColor"
        width="30px"
        height="30px"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z"
        />
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z"
        />
      </svg>
    ),
    color: "text-primary bg-primary bg-opacity-10",
  },
];


const WhyShashtrarth = () => {
  const { isDarkMode, theme } = useTheme();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  }, []);

  return (
    <Container className="mt-5">
      <Row className="mb-4">
        <Col>
          <h2 style={{ color: theme.colors.text }}>
            Why Choose <span className="text-success fw-semibold">Shashtrarth?</span>
          </h2>
        </Col>
      </Row>
      <Row className="justify-content-center">
        {features.map((feature, index) => (
          <Col key={index} md={6} lg={3} className="text-center mb-4">
      <Card
        className="border-0 shadow-sm d-flex flex-column h-100 w-100"
        style={{
          backgroundColor: theme.colors.cardBackground,
          borderColor: theme.colors.cardBorder,
          color: theme.colors.cardText,
          transition: 'all 0.3s ease'
        }}
      >
        <Card.Body
          className="d-flex flex-column flex-grow-1 justify-content-center align-items-center"
          style={{
            backgroundColor: isDarkMode ? 'rgba(13, 110, 253, 0.15)' : 'rgba(13, 110, 253, 0.1)',
            transition: 'background-color 0.3s ease'
          }}
        >                <div
                  className={`rounded-circle p-4 mb-3`}
                  style={{
                    backgroundColor: isDarkMode
                      ? (feature.color.includes('info') ? 'rgba(13, 202, 240, 0.2)' :
                         feature.color.includes('danger') ? 'rgba(220, 53, 69, 0.2)' :
                         feature.color.includes('warning') ? 'rgba(255, 193, 7, 0.2)' :
                         feature.color.includes('success') ? 'rgba(25, 135, 84, 0.2)' : 'rgba(108, 117, 125, 0.2)')
                      : (feature.color.includes('info') ? 'rgba(13, 202, 240, 0.1)' :
                         feature.color.includes('danger') ? 'rgba(220, 53, 69, 0.1)' :
                         feature.color.includes('warning') ? 'rgba(255, 193, 7, 0.1)' :
                         feature.color.includes('success') ? 'rgba(25, 135, 84, 0.1)' : 'rgba(108, 117, 125, 0.1)'),
                    color: feature.color.includes('info') ? '#0dcaf0' :
                           feature.color.includes('danger') ? '#dc3545' :
                           feature.color.includes('warning') ? '#ffc107' :
                           feature.color.includes('success') ? '#198754' : '#6c757d',
                    transition: 'all 0.3s ease'
                  }}
                >
                  {loading ? <Skeleton circle width={50} height={50} baseColor={isDarkMode ? "#2d2d2d" : "#e6ffe6"} highlightColor={isDarkMode ? "#404040" : "#c4f7c4"} /> : feature.icon}
                </div>
                {loading ? <Skeleton width={120} height={20} baseColor={isDarkMode ? "#2d2d2d" : "#e6ffe6"} highlightColor={isDarkMode ? "#404040" : "#c4f7c4"} /> : <h5 style={{ color: theme.colors.cardText }}>{feature.title}</h5>}
                {loading ? <Skeleton count={2} width={180} height={14} baseColor={isDarkMode ? "#2d2d2d" : "#e6ffe6"} highlightColor={isDarkMode ? "#404040" : "#c4f7c4"} /> : <p className="text-center" style={{ color: theme.colors.cardText }}>{feature.description}</p>}
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>
    </Container>
  );
};

export default WhyShashtrarth;
