import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Container, Nav, Offcan<PERSON> } from "react-bootstrap";
import { Link, useLocation } from "react-router-dom";
import {
  FaHome,
  FaGraduationCap,
  FaClipboardList,
  FaBook,
  FaQuestionCircle,
  FaNewspaper,
  FaEllipsisH,
  FaGift,
  FaCheckCircle,
  FaStar,
  FaBars,
  FaUser,
  FaCoins,
  FaCalendarAlt
} from "react-icons/fa";
import "bootstrap/dist/css/bootstrap.min.css";
import { useTheme } from "../context/ThemeContext";

const Sidebar = ({ show: externalShow, onClose: externalOnClose }) => {
  const [internalShow, setInternalShow] = useState(false);

  // Use external show state if provided, otherwise use internal state
  const show = externalShow !== undefined ? externalShow : internalShow;

  const handleClose = () => {
    if (externalOnClose) {
      externalOnClose();
    } else {
      setInternalShow(false);
    }
  };

  const handleShow = () => setInternalShow(true);
  return (
    <>
      <Button
        variant="light"
        onClick={handleShow}
        className="d-md-none position-absolute start-0 top-0 my-2 z-3"
      >
        <FaBars size={24} />
      </Button>
      <Container
        fluid
        className="d-none d-md-block pt-3 overflow-y-auto bg-dark text-secondary h-100 w-100"
        style={{
          minHeight: "100vh",
          overflowY: "scroll",
          scrollbarWidth: "none",
          msOverflowStyle: "none",
        }}
      >
        <SidebarContent />
      </Container>

      {/* Offcanvas Sidebar for mobile */}
      <Offcanvas
        show={show}
        onHide={handleClose}
        placement="start"
        className="bg-dark text-white custom-offcanvas"
      >
        <Offcanvas.Header closeButton>
          <Offcanvas.Title>Menu</Offcanvas.Title>
        </Offcanvas.Header>
        <Offcanvas.Body>
          <SidebarContent closeMenu={handleClose} />
        </Offcanvas.Body>
      </Offcanvas>
    </>
  );
};
const SidebarContent = ({ closeMenu }) => {
  const [activeItem, setActiveItem] = useState("");
  const location = useLocation();

  useEffect(() => {
    setActiveItem(location.pathname);
  }, [location.pathname]);
  return (
    <Nav className="flex-column">
      <Nav.Item>
        <Nav.Link
          as={Link}
          to="/dashboard"
          className="text-white d-flex align-items-center"
          style={{
            opacity: activeItem === "/dashboard" ? 1 : 0.7,
            fontSize: "1rem",
          }}
          // onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          // onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
          onClick={closeMenu}
        >
          <FaHome className="me-2" /> Dashboard
        </Nav.Link>
      </Nav.Item>

      {/* <Nav.Item className="mt-3 fw-bold small" style={{ opacity: 0.7 }}>
        Learn
      </Nav.Item>
      <Nav.Item>
        <Nav.Link
          as={Link}
          to="/skill-academy"
          className="text-white opacity-75"
          style={{ fontSize: "0.8rem" }}
          onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
        >
          <FaGraduationCap className="me-2" /> Skill Academy
        </Nav.Link>
      </Nav.Item> */}
      <Nav.Item className="mt-1 fw-bold small" style={{ opacity: 0.7 }}>
        {/* Tests */}
        <hr></hr>
      </Nav.Item>



      <Nav.Item>
        <Nav.Link
          as={Link} // Use Link from React Router
          to="/dashboard#test-series"
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === "/dashboard/test_series" ? 1 : 0.7, fontSize: "1rem", textDecoration: "none" }}
          onClick={closeMenu}
        >
          <FaClipboardList className="me-2" /> Test Series
        </Nav.Link>
      </Nav.Item>

      <Nav.Item className="mt-1 fw-bold small" style={{ opacity: 0.7 }}>
        {/* Practice */}
        <hr></hr>
      </Nav.Item>

      <Nav.Item>
        <Nav.Link
          as={Link} // Use Link from React Router
          to="/packages"
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === "/packages" ? 1 : 0.7, fontSize: "1rem", textDecoration: "none" }}
          onClick={closeMenu}
        >
          <FaClipboardList className="me-2" /> Packages
        </Nav.Link>
        
      </Nav.Item>
      <Nav.Item>
        <Nav.Link
          as={Link} // Use Link from React Router
          to="/dashboard/reward"
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === "/dashboard/reward" ? 1 : 0.7, fontSize: "1rem", textDecoration: "none" }}
          onClick={closeMenu}
        >
          <FaCoins className="me-2" /> Refer & Earn
        </Nav.Link>
        
      </Nav.Item>
      {/* <Nav.Item>
        <Nav.Link
          as={Link}
          to="/live-tests"
          className="text-white"
          style={{ opacity: 0.7, fontSize: "0.8rem" }}
          onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
        >
          <FaBook className="me-2" /> Live Tests & Quizzes
        </Nav.Link>
      </Nav.Item> */}
      {/* <Nav.Item>
        <Nav.Link
          as={Link}
          to="/dashboard/previous-year-questions"
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === '/dashboard/previous-year-questions' ? 1 : 0.7, fontSize: "1rem" }}
          // onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          // onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
          onClick={closeMenu}
        >
          <FaQuestionCircle className="me-2" /> Previous Year Questions
        </Nav.Link>
      </Nav.Item> */}

      <Nav.Item className="mt-1 fw-bold small" style={{ opacity: 0.7 }}>
        {/* Practice */}
        <hr></hr>
      </Nav.Item>
      {/* <Nav.Item>
        <Nav.Link
          as={Link}
          to="/free-quiz"
          className="text-white d-flex align-items-center"
          style={{ opacity: 0.7, fontSize: "1rem" }}
          onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
        >
          <FaQuestionCircle className="me-2" /> Free Quiz
        </Nav.Link>
      </Nav.Item> */}
      <Nav.Item>
        <Nav.Link
          as={Link}
          to="/attempted-test-series"
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === '/dashboard/attempted_tests' ? 1 : 0.7, fontSize: "1rem" }}
          // onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          // onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
          onClick={closeMenu}
        >
          <FaCheckCircle className="me-2" /> Attempted Tests
        </Nav.Link>
      </Nav.Item>
      <Nav.Item>
        <Nav.Link
          as={Link}
          to="/events"
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === '/events' ? 1 : 0.7, fontSize: "1rem" }}
          onClick={closeMenu}
        >
          <FaCalendarAlt className="me-2" /> Events
        </Nav.Link>
      </Nav.Item>
      {/* <Nav.Item>
        <Nav.Link
          as={Link}
          to="/dashboard/pass"
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === '/dashboard/pass' ? 1 : 0.7, fontSize: "1rem" }}
          // onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          // onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
          onClick={closeMenu}
        >
          <FaStar className="me-2" /> Pass
        </Nav.Link>
      </Nav.Item> */}
      {/* <Nav.Item>
        <Nav.Link
          as={Link}
          to="/dashboard/pass-pro"
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === '/dashboard/pass-pro' ? 1 : 0.7, fontSize: "1rem" }}
          // onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          // onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
        >
          <FaStar className="me-2" /> Pass Pro
        </Nav.Link>
      </Nav.Item> */}
      {/* <Nav.Item>
        <Nav.Link
          as={Link}
          to="/dashboard/pass-elite"
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === '/dashboard/pass-elite' ? 1 : 0.7, fontSize: "1rem" }}
          // onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          // onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
        >
          <FaStar className="me-2" /> Pass Elite
        </Nav.Link>
      </Nav.Item> */}

      <Nav.Item className="mt-1 fw-bold small" style={{ opacity: 0.7 }}>
        {/* Miscellaneous */}
        <hr></hr>
      </Nav.Item>
      <Nav.Item>
        <Nav.Link
          as="a" // Use a normal anchor tag
          href="https://blog.shashtrarth.com/"
          target="_blank" // Opens in a new tab
          rel="noopener noreferrer" // Security best practice
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === "/dashboard/blogs" ? 1 : 0.7, fontSize: "1rem" }}
          onClick={closeMenu}
        >
          <FaNewspaper className="me-2" /> 60 Words & Blogs
        </Nav.Link>
      </Nav.Item>

      <Nav.Item className="mt-1 fw-bold small" style={{ opacity: 0.7 }}>
        {/* Miscellaneous */}
        <hr></hr>
      </Nav.Item>
      {/* <Nav.Item>
        <Nav.Link
          href="https://exam.shashtrath.com"
          target="_blank"
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === 'https://exam.shashtrath.com' ? 1 : 0.7, fontSize: "1rem" }}
          // onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          // onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
          onClick={closeMenu}
        >
          <FaGraduationCap className="me-2" /> Exams
        </Nav.Link>
      </Nav.Item> */}
      {/* <Nav.Item>
        <Nav.Link
          as={Link}
          to="/dashboard/saved-questions"
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === '/dashboard/saved-questions' ? 1 : 0.7, fontSize: "1rem" }}
          // onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          // onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
          onClick={closeMenu}
        >
          <FaQuestionCircle className="me-2" /> Saved Questions
        </Nav.Link>
      </Nav.Item> */}
      <Nav.Item>
        <Nav.Link
          as={Link}
          to="/queries"
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === '/dashboard/questions-reported' ? 1 : 0.7, fontSize: "1rem" }}
          // onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          // onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
          onClick={closeMenu}
        >
          <FaQuestionCircle className="me-2" /> My Queries
        </Nav.Link>
      </Nav.Item>
      {/* <Nav.Item>
        <Nav.Link
          as={Link}
          to="/doubt"
          className="text-white d-flex align-items-center"
          style={{ opacity: 0.7, fontSize: "1rem" }}
          onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
        >
          <FaQuestionCircle className="me-2" /> Doubt
        </Nav.Link>
      </Nav.Item> */}
      {/* <Nav.Item>
        <Nav.Link
          as={Link}
          to="/dashboard/current-affairs"
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === '/dashboard/current-affairs' ? 1 : 0.7, fontSize: "1rem" }}
          // onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          // onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
          onClick={closeMenu}
        >
          <FaNewspaper className="me-2" /> Current Affairs
        </Nav.Link>
      </Nav.Item> */}
      <Nav.Item>
        <Nav.Link
          as={Link}
          to="/dashboard#FAQs"
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === '/dashboard/blogs' ? 1 : 0.7, fontSize: "1rem" }}
          // onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          // onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
          onClick={closeMenu}
        >
          <FaNewspaper className="me-2" /> FAQs
        </Nav.Link>
      </Nav.Item>

      <Nav.Item className="mt-1 fw-bold" style={{ opacity: 0.7 }}>
        {/* More Section */}
        <hr></hr>
      </Nav.Item>
      {/* <Nav.Item>
        <Nav.Link
          as={Link}
          to="/dashboard/reward"
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === '/dashboard/reward' ? 1 : 0.7, fontSize: "1rem" }}
          // onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          // onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
          onClick={closeMenu}
        >
          <FaGift className="me-2" /> Refer & Earn
        </Nav.Link>
      </Nav.Item> */}
      {/* <Nav.Item>
        <Nav.Link
          as={Link}
          to="/dashboard/profile"
          className="text-white small d-flex align-items-center"
          style={{ opacity: activeItem === '/dashboard/our-selections' ? 1 : 0.7, fontSize: "1rem" }}
          // onMouseEnter={(e) => (e.currentTarget.style.opacity = 1)}
          // onMouseLeave={(e) => (e.currentTarget.style.opacity = 0.7)}
          onClick={closeMenu}
        >
          <FaUser className="me-2" /> My Profile
        </Nav.Link>
      </Nav.Item> */}
      <Nav.Item>
        <Nav.Link
          as={Link}
          to="/dashboard/progress-friends"
          className="text-white d-flex align-items-center"
          style={{ opacity: activeItem === '/dashboard/progress-friends' ? 1 : 0.7, fontSize: "1rem" }}
          onClick={closeMenu}
        >
          <FaUser className="me-2" /> Progress & Friends
        </Nav.Link>
      </Nav.Item>
    </Nav>
  );
};

export default Sidebar;
