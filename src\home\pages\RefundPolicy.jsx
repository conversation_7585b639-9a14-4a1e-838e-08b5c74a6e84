import React, { useEffect, useState } from 'react';
import { FaBars, FaTimes } from "react-icons/fa";
import NavBar from "../../commonCompoenents/NavBar";
import Footer from "../components/Footer";
import { Container, <PERSON>, Col, <PERSON>, Card } from 'react-bootstrap';

const RefundPolicy = () => {
  const [activeSection, setActiveSection] = useState('');
  const [isNavVisible, setIsNavVisible] = useState(true);

  const toggleNav = () => {
    setIsNavVisible(!isNavVisible);
  };

  useEffect(() => {
    const handleScroll = () => {
      const sections = Array.from({ length: 12 }, (_, i) => `section${i + 1}`);
      let currentSection = '';
      sections.forEach((section) => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= 100 && rect.bottom >= 100) {
            currentSection = section;
          }
        }
      });
      setActiveSection(currentSection);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <>
      <NavBar />
      <Container fluid className="py-5" style={{ marginTop: "80px", minHeight: "100vh" }}>
        <Row className="justify-content-center">
          <Col lg={10} md={11} xs={12}>
            <Card className="shadow-lg border-0">
              <Card.Header className="bg-success text-white text-center py-4">
                <h1 className="mb-0">Refund Policy</h1>
                <p className="mb-0 mt-2">Effective Date: January 1, 2024</p>
              </Card.Header>

          {/* Refund Policy Content - Multi-Column Layout */}
          <Row>
            {/* Sidebar - Floating Navigation Links */}
            <div style={{
              position: 'fixed',
              top: '15%',
              right: '2%',
              width: isNavVisible ? '250px' : '50px',
              zIndex: 1000,
              backgroundColor: '#fff',
              padding: isNavVisible ? '10px' : '5px',
              borderRadius: '8px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
              transition: 'width 0.3s, padding 0.3s'
            }}>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  cursor: 'pointer'
                }}
                onClick={toggleNav}
              >
                <h4 className="font-weight-bold" style={{ display: isNavVisible ? 'block' : 'none' }}>Navigation</h4>
                {isNavVisible ? <FaTimes /> : <FaBars />}
              </div>
              {isNavVisible && (
                <ul style={{ listStyleType: 'none', padding: 0, fontSize: "0.8rem" }}>
                  {[
                    "1. Overview",
                    "2. Eligibility for Refunds",
                    "3. Refund Timeframes",
                    "4. Non-Refundable Items",
                    "5. Refund Process",
                    "6. Processing Time",
                    "7. Partial Refunds",
                    "8. Technical Issues",
                    "9. Subscription Cancellations",
                    "10. Payment Gateway Issues",
                    "11. Contact Information",
                    "12. Policy Updates"
                  ].map((title, i) => (
                    <li key={i}>
                      <a
                        href={`#section${i + 1}`}
                        onClick={(e) => {
                          e.preventDefault();
                          scrollToSection(`section${i + 1}`);
                        }}
                        style={{
                          color: activeSection === `section${i + 1}` ? '#28a745' : '#333',
                          textDecoration: 'none',
                          fontWeight: activeSection === `section${i + 1}` ? 'bold' : 'normal'
                        }}
                      >
                        {title}
                      </a>
                    </li>
                  ))}
                </ul>
              )}
            </div>

            {/* Main Content Section */}
            <Col lg={9} md={8}>
              {[
                {
                  id: "section1",
                  title: "1. Overview",
                  content: (
                    <>
                      <p><strong>Commitment to Customer Satisfaction:</strong> At Shashtrarth, we are committed to providing quality educational services and ensuring customer satisfaction.</p>
                      <p><strong>Scope:</strong> This refund policy applies to all paid services, subscriptions, and purchases made on our platform.</p>
                      <p><strong>Fair Refund Practices:</strong> We strive to maintain transparent and fair refund practices while protecting the integrity of our educational content.</p>
                    </>
                  )
                },
                {
                  id: "section2",
                  title: "2. Eligibility for Refunds",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Eligible Scenarios</h6>
                      <ul>
                        <li><strong>Technical Issues:</strong> Persistent technical problems that prevent access to purchased content for more than 48 hours.</li>
                        <li><strong>Duplicate Payments:</strong> Accidental multiple payments for the same service or subscription.</li>
                        <li><strong>Unauthorized Transactions:</strong> Payments made without user authorization (subject to verification).</li>
                        <li><strong>Service Not Delivered:</strong> Failure to provide access to purchased content within 24 hours of payment confirmation.</li>
                      </ul>
                      <h6 className="font-weight-bold">b. Refund Request Window</h6>
                      <ul>
                        <li><strong>Standard Refunds:</strong> Must be requested within 7 days of purchase.</li>
                        <li><strong>Technical Issue Refunds:</strong> Must be reported within 48 hours of the issue occurring.</li>
                        <li><strong>Subscription Refunds:</strong> Must be requested before 25% of the subscription period has elapsed.</li>
                      </ul>
                    </>
                  )
                },
                {
                  id: "section3",
                  title: "3. Refund Timeframes",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Processing Timeline</h6>
                      <ul>
                        <li><strong>Request Review:</strong> 2-3 business days for initial review and approval.</li>
                        <li><strong>Payment Gateway Processing:</strong> 5-7 business days for credit card refunds.</li>
                        <li><strong>Bank Transfer:</strong> 7-10 business days for direct bank transfers.</li>
                        <li><strong>Digital Wallet:</strong> 3-5 business days for UPI/wallet refunds.</li>
                      </ul>
                      <h6 className="font-weight-bold">b. Notification Process</h6>
                      <p>Users will receive email notifications at each stage of the refund process, including approval, processing, and completion.</p>
                    </>
                  )
                },
                {
                  id: "section4",
                  title: "4. Non-Refundable Items",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Excluded Services</h6>
                      <ul>
                        <li><strong>Completed Courses:</strong> Courses where more than 25% of content has been accessed or completed.</li>
                        <li><strong>Downloaded Materials:</strong> Any downloadable content that has been accessed or downloaded.</li>
                        <li><strong>Live Sessions:</strong> Attended live classes, webinars, or interactive sessions.</li>
                        <li><strong>Personalized Services:</strong> One-on-one tutoring sessions that have been conducted.</li>
                        <li><strong>Promotional Offers:</strong> Services purchased at discounted rates or through special promotions.</li>
                      </ul>
                      <h6 className="font-weight-bold">b. Gift Purchases</h6>
                      <p>Gift subscriptions and courses can only be refunded if the recipient has not accessed the content.</p>
                    </>
                  )
                },
                {
                  id: "section5",
                  title: "5. Refund Process",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. How to Request a Refund</h6>
                      <ol>
                        <li><strong>Contact Support:</strong> Email our support <NAME_EMAIL> with your refund request.</li>
                        <li><strong>Provide Information:</strong> Include your order ID, payment details, and reason for refund.</li>
                        <li><strong>Submit Documentation:</strong> Provide any relevant screenshots or evidence if applicable.</li>
                        <li><strong>Await Review:</strong> Our team will review your request within 2-3 business days.</li>
                      </ol>
                      <h6 className="font-weight-bold">b. Required Information</h6>
                      <ul>
                        <li>Full name and registered email address</li>
                        <li>Order/Transaction ID</li>
                        <li>Payment method used</li>
                        <li>Detailed reason for refund request</li>
                        <li>Supporting documentation (if applicable)</li>
                      </ul>
                    </>
                  )
                },
                {
                  id: "section6",
                  title: "6. Processing Time",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Review Process</h6>
                      <p>All refund requests undergo a thorough review process to ensure compliance with our policy terms and to prevent fraudulent claims.</p>
                      <h6 className="font-weight-bold">b. Approval Criteria</h6>
                      <ul>
                        <li>Request falls within eligible timeframe</li>
                        <li>Reason for refund meets policy criteria</li>
                        <li>User account is in good standing</li>
                        <li>No violation of terms of service</li>
                      </ul>
                      <h6 className="font-weight-bold">c. Communication</h6>
                      <p>Users will receive regular updates via email throughout the refund process, including any additional information required.</p>
                    </>
                  )
                },
                {
                  id: "section7",
                  title: "7. Partial Refunds",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Prorated Refunds</h6>
                      <p>For subscription services, partial refunds may be calculated based on unused portion of the subscription period.</p>
                      <h6 className="font-weight-bold">b. Course Bundles</h6>
                      <ul>
                        <li>Individual courses within bundles may be eligible for partial refunds if other courses have not been accessed.</li>
                        <li>Refund amount will be calculated based on individual course pricing.</li>
                      </ul>
                      <h6 className="font-weight-bold">c. Usage-Based Refunds</h6>
                      <p>Refunds may be adjusted based on the amount of content accessed or services utilized before the refund request.</p>
                    </>
                  )
                },
                {
                  id: "section8",
                  title: "8. Technical Issues",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Platform Downtime</h6>
                      <p>Extended platform downtime (more than 24 hours) may qualify for service credits or partial refunds.</p>
                      <h6 className="font-weight-bold">b. Content Access Issues</h6>
                      <ul>
                        <li>Inability to access purchased content due to technical errors</li>
                        <li>Video playback or streaming issues persisting beyond 48 hours</li>
                        <li>Mobile app functionality problems affecting course access</li>
                      </ul>
                      <h6 className="font-weight-bold">c. Resolution Attempts</h6>
                      <p>Our technical team will first attempt to resolve issues before processing refund requests. Refunds will be considered if technical solutions are not feasible.</p>
                    </>
                  )
                },
                {
                  id: "section9",
                  title: "9. Subscription Cancellations",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Cancellation Policy</h6>
                      <ul>
                        <li>Subscriptions can be cancelled at any time through user account settings</li>
                        <li>Cancelled subscriptions remain active until the end of the current billing period</li>
                        <li>No automatic refunds for cancelled subscriptions unless within refund window</li>
                      </ul>
                      <h6 className="font-weight-bold">b. Auto-Renewal</h6>
                      <p>Users can disable auto-renewal to prevent future charges. Refunds for auto-renewed subscriptions may be considered within 48 hours of renewal.</p>
                      <h6 className="font-weight-bold">c. Subscription Modifications</h6>
                      <p>Downgrades or upgrades to subscriptions will be prorated and adjusted in the next billing cycle.</p>
                    </>
                  )
                },
                {
                  id: "section10",
                  title: "10. Payment Gateway Issues",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Failed Transactions</h6>
                      <p>If payment is deducted but service access is not provided due to gateway issues, full refunds will be processed immediately.</p>
                      <h6 className="font-weight-bold">b. Double Charges</h6>
                      <ul>
                        <li>Duplicate charges due to payment gateway errors will be refunded in full</li>
                        <li>Processing time: 3-5 business days</li>
                        <li>No documentation required beyond transaction proof</li>
                      </ul>
                      <h6 className="font-weight-bold">c. Currency Conversion</h6>
                      <p>Refunds will be processed in the original payment currency. Any conversion charges by banks are not covered by Shashtrarth.</p>
                    </>
                  )
                },
                {
                  id: "section11",
                  title: "11. Contact Information",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Refund Support</h6>
                      <ul>
                        <li><strong>Email:</strong> <EMAIL></li>
                        <li><strong>General Support:</strong> <EMAIL></li>
                        <li><strong>Phone:</strong> +91-XXXX-XXXX-XX (Business hours: 9 AM - 6 PM IST)</li>
                        <li><strong>Response Time:</strong> 24-48 hours for email inquiries</li>
                      </ul>
                      <h6 className="font-weight-bold">b. Required Information for Contact</h6>
                      <p>When contacting support, please include your registered email, order ID, and detailed description of the issue.</p>
                      <h6 className="font-weight-bold">c. Escalation Process</h6>
                      <p>If not satisfied with the initial response, requests can be escalated to our senior support team within 7 days.</p>
                    </>
                  )
                },
                {
                  id: "section12",
                  title: "12. Policy Updates",
                  content: (
                    <>
                      <h6 className="font-weight-bold">a. Modification Rights</h6>
                      <p>Shashtrarth reserves the right to modify this refund policy at any time. Changes will be effective immediately upon posting.</p>
                      <h6 className="font-weight-bold">b. User Notification</h6>
                      <ul>
                        <li>Users will be notified of policy changes via email</li>
                        <li>Continued use of services constitutes acceptance of updated policy</li>
                        <li>Major changes will include a 30-day notice period</li>
                      </ul>
                      <h6 className="font-weight-bold">c. Grandfathering</h6>
                      <p>Existing subscriptions and purchases made before policy changes will be governed by the terms in effect at the time of purchase for the duration of that service period.</p>
                      <h6 className="font-weight-bold">d. Legal Compliance</h6>
                      <p>This policy complies with applicable consumer protection laws and regulations. In case of conflicts, local laws will take precedence.</p>
                    </>
                  )
                }
              ].map((section, index) => (
                <Card key={index} className="mb-4 border-0 shadow-sm" id={section.id}>
                  <Card.Body>
                    <h4 className="text-success mb-3">{section.title}</h4>
                    <div className="text-justify">{section.content}</div>
                  </Card.Body>
                </Card>
              ))}
            </Col>
          </Row>
            </Card>
          </Col>
        </Row>
      </Container>
      <Footer />
    </>
  );
};

export default RefundPolicy;
