import React from 'react'
import { Col, Row } from 'react-bootstrap'
import ExamOverview from './ExamOverview'
import ExamFreeTest from './ExamFreeTest'
import LatestUpdate from './LatestUpdate'
import Faq from './Faq'
import PassBox from '../../commonCompoenents/PassBox'

const Overview = ({examInfo}) => {
  return (
    <>
        <ExamOverview data={examInfo?.exam} />
        <ExamFreeTest data={examInfo?.exam} />
        <LatestUpdate data={examInfo?.exam?.latestUpdate}/>
        <Faq faqData={examInfo?.exam?.faqData} name={examInfo?.exam?.name} />
    </>
  )
}

export default Overview
