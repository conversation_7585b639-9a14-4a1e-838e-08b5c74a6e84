import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { fetchContacts } from "../../redux/slice/contactSlice";
import { But<PERSON>, Card, Row, Col } from "react-bootstrap";
import { FaUserPlus } from "react-icons/fa";

const YourContacts = () => {
  const dispatch = useDispatch();
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getContacts = async () => {
      setLoading(true);
      const res = await dispatch(fetchContacts());
      if (res?.payload?.results) {
        setContacts(res.payload.results);
      }
      setLoading(false);
    };
    getContacts();
  }, [dispatch]);

  return (
    <section className="mt-4 shadow-sm">
      <Card.Body>
        <h4 className="mb-3 text-success">Your Contacts</h4>
        {loading ? (
          <div>Loading...</div>
        ) : contacts.length === 0 ? (
          <div>No contacts found.</div>
        ) : (
          <Row>
            {contacts.map((contact) => (
              <Col md={6} key={contact.id} className="mb-3">
                <Card className="h-100">
                  <Card.Body className="d-flex justify-content-between align-items-center">
                    <div>
                      <div className="fw-bold">{contact.name}</div>
                      <div className="text-muted">{contact.contact_number}</div>
                    </div>
                    <Button variant="outline-success" size="sm">
                      <FaUserPlus className="me-1" /> Invite
                    </Button>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        )}
      </Card.Body>
    </section>
  );
};

export default YourContacts;
