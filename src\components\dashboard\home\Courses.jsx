import React, { useState } from 'react';
import { Container, Row, Col, Card, Button, Image } from 'react-bootstrap';
import useCourses from '../../hooks/useCourses';

const Courses = () => {

  const {courses, isLoading, error} = useCourses();
  console.log(courses);
  

  const [selectedCourse, setSelectedCourse] = useState(courses[0]);

  const handleCourseClick = (course) => {
    setSelectedCourse(course);
  };

  return (
    <Container className="mb-5">
      <Row className="justify-content-center">
      <Col>
      <h4><span className='text-success'>{courses && courses.length}</span> courses</h4>
      </Col>
        <Col md={12} className='bg-white border p-3 shadow-sm rounded'>
          <Row>
            {/* First Column: Courses */}
            <Col md={3} className="border-end">
              {courses && courses.map((course, index) => (
                <Button
                  key={course?.course_id}
                  variant={course?.name === selectedCourse?.name ? 'success' : 'light'}
                  onClick={() => handleCourseClick(course)}
                  className="w-100 mb-2 text-left"
                >
                  {course?.name}
                </Button>
              ))}
            </Col>

            {/* Second Column: Subcourses */}
            <Col md={9}>
              {selectedCourse?.sub_courses.length > 0 ? (
                selectedCourse?.sub_courses.map((subcourse, index) => (
                <Card key={index} className="mb-3">
                  <Card.Body className="d-flex align-items-center gap-4">
                    <Image fluid
                      src={`${import.meta.env.VITE_BASE_URL}${selectedCourse?.attachments}`}
                      alt={subcourse?.name}
                      style={{ width: '100px', height: '100px', marginRight: '20px' }}
                    />
                    <div>
                      <Card.Title>{subcourse.name}</Card.Title>
                      {/* <Card.Text>
                        <strong>Language:</strong> 
                      </Card.Text> */}
                      <Button variant="success" className='btn-sm'>View Full Course</Button>
                    </div>
                  </Card.Body>
                </Card>
              ))
              ) : (
                <p className='text-danger text-center'>no sub courses available</p>
              )}
            </Col>
          </Row>
        </Col>
      </Row>
    </Container>
  );
};

export default Courses;
