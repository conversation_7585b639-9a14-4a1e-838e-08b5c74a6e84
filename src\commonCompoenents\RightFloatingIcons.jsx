// RightFloatingIcons.jsx
import React, { useState } from 'react';
import { Button } from 'react-bootstrap';
import { BsChatDots } from 'react-icons/bs';
import ChatBot from './ChatBot';
import { useSelector } from 'react-redux';

const RightFloatingIcons = () => {
  const [showChatBot, setShowChatBot] = useState(false);
  const accessToken = useSelector((state) => state?.student?.student?.JWT_Token?.access);

  if (accessToken) {
    return null;
  }

  return (
    <>
      {!showChatBot && (
        <div style={styles.floatingButton} onClick={() => setShowChatBot(true)}>
          
          <span className='fs-6'> Chat with <PERSON>charya </span> <BsChatDots className='fs-4' style={{marginLeft: "0.5rem"}}/>
        </div>
      )}

      {showChatBot && (
        <ChatBot onClose={() => setShowChatBot(false)} />
      )}
    </>
  );
};

const styles = {
  floatingButton: {
    position: 'fixed',
    bottom: '20px',
    right: '20px',
    background: '#007bff',
    color: '#fff',
    padding: '10px 16px',
    borderRadius: '30px',
    display: 'flex',
    alignItems: 'center',
    cursor: 'pointer',
    zIndex: 9999,
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
  },
};

export default RightFloatingIcons;
