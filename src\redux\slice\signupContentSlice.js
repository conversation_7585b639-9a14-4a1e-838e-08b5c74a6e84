import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Get all sign-up contents
export const getSignUpContents = createAsyncThunk(
  "signupContent/getAll",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SIGNUP_CONTENT}`
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to fetch content");
    }
  }
);

// Slice
const signUpContentSlice = createSlice({
  name: "signupContent",
  initialState: {
    signupContents: [],
    loading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Get all content
      .addCase(getSignUpContents.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSignUpContents.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(getSignUpContents.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default signUpContentSlice.reducer;
