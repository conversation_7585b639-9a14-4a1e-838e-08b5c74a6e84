// import { useEffect, useRef } from "react";
// import { useLocation } from "react-router-dom";

// const usePageTracking = () => {
//   const location = useLocation();
//   const lastSentDataRef = useRef(null);

//   useEffect(() => {
//     const path = location.pathname;
//     let pageData = JSON.parse(localStorage.getItem("page_data")) || {};

//     pageData[path] = pageData[path] ? pageData[path] + 1 : 1;

//     localStorage.setItem("page_data", JSON.stringify(pageData));
//   }, [location.pathname]);

//   const sendPageData = async () => {
//     const pageData = JSON.parse(localStorage.getItem("page_data")) || {};

//     const pageDataString = JSON.stringify(pageData);

//     if (Object.keys(pageData).length > 0 && pageDataString !== lastSentDataRef.current) {
//       try {
//         await fetch(`${window.location.origin}/contributor/track-page-view/`, {
//           method: "POST",
//           headers: {
//             "Content-Type": "application/json",
//             "X-CSRFToken": "{{ csrf_token }}",
//           },
//           body: pageDataString,
//         });

//         lastSentDataRef.current = pageDataString;

//         localStorage.removeItem("page_data");
//       } catch (error) {
//         console.error("Error sending page data:", error);
//       }
//     }
//   };

//   useEffect(() => {
//     const interval = setInterval(sendPageData, 10000);
//     return () => clearInterval(interval);
//   }, []);
// };

// export default usePageTracking;





















// If page reloads or tab close still makes a final api call so that the data is not lost

import { useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";

const usePageTracking = () => {
  const location = useLocation();
  const lastSentDataRef = useRef(null);

  useEffect(() => {
    const path = location.pathname;
    let pageData = JSON.parse(localStorage.getItem("page_data")) || {};

    pageData[path] = pageData[path] ? pageData[path] + 1 : 1;

    localStorage.setItem("page_data", JSON.stringify(pageData));
  }, [location.pathname]);

  const sendPageData = async () => {
    const pageData = JSON.parse(localStorage.getItem("page_data")) || {};
    const pageDataString = JSON.stringify(pageData);

    if (Object.keys(pageData).length > 0 && pageDataString !== lastSentDataRef.current) {
      try {
        await fetch(`${window.location.origin}/contributor/track-page-view/`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-CSRFToken": "{{ csrf_token }}",
          },
          body: pageDataString,
        });

        lastSentDataRef.current = pageDataString;
        localStorage.removeItem("page_data");
      } catch (error) {
        console.error("Error sending page data:", error);
      }
    }
  };

  useEffect(() => {
    const interval = setInterval(sendPageData, 10000);

    // Send data when the user leaves the page (tab close, refresh, navigate away)
    const handleUnload = () => sendPageData();
    window.addEventListener("beforeunload", handleUnload);

    return () => {
      clearInterval(interval);
      window.removeEventListener("beforeunload", handleUnload);
    };
  }, []);
};

export default usePageTracking;

