import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { getCourses } from '../../redux/slice/courseSlice';

const useCourses = () => {
  const [courses, setCourses] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const dispatch = useDispatch();

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const response = await dispatch(getCourses());
        setCourses(response?.payload || []);
      } catch (error) {
        setError(error.message || 'Error fetching courses');
        console.error("Error fetching courses:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCourses();
  }, [dispatch]);

  return {
    courses,
    isLoading,
    error,
  };
};

export default useCourses;