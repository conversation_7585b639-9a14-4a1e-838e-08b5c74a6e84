import React, { useEffect, useState } from "react";
import <PERSON> from "./<PERSON>";
import { Container } from "react-bootstrap";
import ExpandedTestSeries from "./ExpandedTestSeries";
import AboutTest from "./AboutTest";
import FAQSection from "../../../dashboard/components/FAQSection";
import { useParams } from "react-router-dom";
import { useDispatch } from "react-redux";
import { getTestSeriesBySubCourse } from "../../../redux/slice/dashboardSlice";

const Test = () => {
  const [testSeries, setTestSeries] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const { slug } = useParams();
  const dispatch = useDispatch();

  useEffect(() => {
    const fetchTestSeries = async () => {
      try {
        setIsLoading(true);
        const response = await dispatch(getTestSeriesBySubCourse(slug)).unwrap();

        console.log("API Response:", response); // Debugging

        if (Array.isArray(response) && response.length > 0) {
          setTestSeries(response[0]); // 
        } else {
          console.warn("No test series found.");
          setTestSeries(null);
        }
      } catch (error) {
        console.error("Error fetching test series:", error);
        setTestSeries(null);
      } finally {
        setIsLoading(false);
      }
    };

    if (slug) {
      fetchTestSeries();
    }
  }, [dispatch, slug]);

  return (
    <Container fluid className="bg-light p-0">
      {isLoading ? (
        <p className="text-center">Loading...</p>
      ) : testSeries ? (
        <>
          <Hero data={testSeries} loading={isLoading} />
          <ExpandedTestSeries data={testSeries} loading={isLoading} />
          <AboutTest data={testSeries} loading={isLoading} />
          <FAQSection data={testSeries} loading={isLoading} />
        </>
      ) : (
        <p className="text-center">No test series available.</p>
      )}
    </Container>
  );
};

export default Test;
