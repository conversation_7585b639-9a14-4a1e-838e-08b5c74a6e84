import React, { useEffect, useMemo, useState, useRef } from "react"; // Add useRef import
import { Navbar, Nav, NavDropdown, Container, Spinner, Image, Card, Row, Col, Button, Dropdown } from "react-bootstrap";
import axios from "axios";
import { Link, useNavigate } from "react-router-dom";
import { getCourses } from "../redux/slice/courseSlice";
import { useDispatch } from "react-redux";
import { getSubjects, getSections, subjectClickCounter, sectionClickCounter } from "../redux/slice/subjectSlice"; // Import actions
import { FaChalkboardTeacher, FaUserPlus } from "react-icons/fa"; // Add FaUserPlus import
import { MdOutlineKeyboardArrowRight } from "react-icons/md";
import { BsSun, BsMoon, BsDisplay } from "react-icons/bs"; // Theme icons
import styles from "./Dropdown.module.css";
import { Helmet } from "react-helmet-async";
import { useTheme } from "../context/ThemeContext";

const NavBar = () => {
  const navigate = useNavigate();
  const { isDarkMode, themeMode, setSystemTheme, setLightTheme, setDarkTheme, getThemeLabel } = useTheme();
  const [courses, setCourses] = useState([]);
  const [subCourses, setSubCourses] = useState([]);
  const [showCoursesDropdown, setShowCoursesDropdown] = useState(false);
  const [activeCourseId, setActiveCourseId] = useState(null);
  const [activeCourseImage, setActiveCourseImage] = useState(null);
  const [loadingCourses, setLoadingCourses] = useState(false);

  const [subjects, setSubjects] = useState([]);
  const [topics, setTopics] = useState([]);
  const [loadingSubjects, setLoadingSubjects] = useState(false);
  const [showSubjectDropdown, setShowSubjectDropdown] = useState(false);
  const [activeSubjectId, setActiveSubjectId] = useState(null);
  const [subTopics, setSubTopics] = useState([]); // New state for subtopics
  const [activeTopicId, setActiveTopicId] = useState(null); // New state for active topic
  const [sections, setSections] = useState([]); // New state for sections
  const [activeSectionId, setActiveSectionId] = useState(null); // New state for active section
  const dispatch = useDispatch();

  const fetchCourses = async () => {
    try {
      setLoadingCourses(true);
      const response = await dispatch(getCourses());
      setCourses(response?.payload || []);
      if (response?.payload?.length > 0) {
        setSubCourses(response?.payload?.[0]?.sub_courses || []);
        setActiveCourseId(response?.payload?.[0]?.course_id);
        setActiveCourseImage(response?.payload?.[0]?.attachments);
      }
    } catch (error) {
      console.error("Error fetching courses:", error);
    } finally {
      setLoadingCourses(false);
    }
  };

  const fetchSubjects = async () => {
    try {
      setLoadingSubjects(true);
      const response = await dispatch(getSubjects());
      setSubjects(response?.payload?.data || []);
      if (response?.payload?.data?.length > 0) {
        setTopics(response?.payload?.data?.[0]?.topics || []);
        setActiveSubjectId(response?.payload?.data?.[0]?.subject_id);
      }
    } catch (error) {
      console.error("Error fetching subjects:", error);
    } finally {
      setLoadingSubjects(false);
    }
  };

  const fetchSections = async () => {
    try {
      console.log("Dispatching getSections..."); // Debug log
      const response = await dispatch(getSections());
      console.log("getSections response:", response); // Debug log
      setSections(response?.payload || []); // Save sections response
    } catch (error) {
      console.error("Error fetching sections:", error);
    }
  };

  useEffect(() => {
    console.log("Component mounted, fetching data..."); // Debug log
    fetchCourses();
    fetchSubjects();
    fetchSections(); // Fetch sections on component mount
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest(`.${styles.dropdownContainer}`)) {
        setShowCoursesDropdown(false);
        setShowSubjectDropdown(false);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  // Memoize courses, subjects, and sections to avoid unnecessary re-renders
  const memoizedCourses = useMemo(() => courses, [courses]);
  const memoizedSubjects = useMemo(() => subjects, [subjects]);
  const memoizedSections = useMemo(() => sections, [sections]);

  const handleSubCourseClick = (courseId) => {
    localStorage.setItem("selectedCourseId", courseId); // Store course ID in local storage
    navigate("/signup");
  };

  const handleSubTopicClick = () => {
    navigate("/signup");
  };

  // Theme toggle functions
  const handleThemeSelection = (themeType) => {
    switch (themeType) {
      case 'system':
        setSystemTheme();
        break;
      case 'light':
        setLightTheme();
        break;
      case 'dark':
        setDarkTheme();
        break;
    }
  };

  const getThemeIconComponent = (theme) => {
    switch (theme) {
      case 'system':
        return <BsDisplay size={16} />;
      case 'light':
        return <BsSun size={16} />;
      case 'dark':
        return <BsMoon size={16} />;
      default:
        return <BsDisplay size={16} />;
    }
  };

  const ResponsiveDropdown = ({ title, items, activeId, setActiveId, setSubItems, subItems, loading, isSubjects }) => {
    const itemsContainerRef = useRef(null); // Ref for the scrollable container

    // Sort items by rank in descending order
    const sortedItems = useMemo(() => {
      return items?.sort((a, b) => (b.rank || 0) - (a.rank || 0));
    }, [items]);

    const handleItemClick = async (event, item) => {
      event.stopPropagation(); // Prevent dropdown from closing
      console.log("Clicked item:", item); // Debug log for clicked item

      if (item?.section_id || item?.section_name) {
        console.log("Setting activeSectionId:", item.section_id);
        setActiveSectionId(item.section_id);
        setActiveId(null); // Clear activeSubjectId when a section is clicked

        // Dispatch sectionClickCounter with corrected payload
        try {
          await dispatch(sectionClickCounter({ section_name: item.section_name })); // Correct payload
          console.log("Section click count updated successfully");
        } catch (error) {
          console.error("Error updating section click count:", error);
        }
      } else if (item?.subject_id) {
        console.log("Setting activeSubjectId:", item.subject_id);
        setActiveId(item.subject_id);
        setActiveSectionId(null); // Clear activeSectionId when a subject is clicked

        // Dispatch subjectClickCounter with subject_id
        try {
          await dispatch(subjectClickCounter(item.subject_id)); // Pass subject_id directly
          console.log("Subject click count updated successfully");
        } catch (error) {
          console.error("Error updating subject click count:", error);
        }
      } else if (item?.course_id) {
        console.log("Setting activeCourseId:", item.course_id);
        setActiveId(item.course_id);
        setActiveSectionId(null); // Clear activeSectionId for other cases
      } else {
        console.warn("Item does not have a valid ID (course_id, subject_id, or section_id):", item);
      }

      // Handle sub-items
      const subItemsList = item?.sub_courses ?? item?.topics ?? [];
      console.log("Sub-items being set:", subItemsList);
      setSubItems(subItemsList);

      if (isSubjects) {
        console.log("Clearing subTopics for subjects");
        setSubTopics([]); // Clear subtopics only when necessary
      }
    };

    return (
      <NavDropdown
        title={title}
        id={`${title.toLowerCase()}-dropdown`}
        show={isSubjects ? showSubjectDropdown : showCoursesDropdown}
        onClick={(event) => {
          event.stopPropagation(); // Prevent dropdown from closing
          if (isSubjects) {
            setShowSubjectDropdown(!showSubjectDropdown);
            setShowCoursesDropdown(false); // Close Test Series dropdown
          } else {
            setShowCoursesDropdown(!showCoursesDropdown);
            setShowSubjectDropdown(false); // Close Subjects dropdown
          }
        }}
        className={styles.dropdownContainer} // Use CSS module class
      >
        <div className={styles.dropdownContent} style={{ display: "flex" }}>
          <div
            ref={itemsContainerRef} // Attach ref to the scrollable container
            className={styles.itemsContainer}
            style={{ flex: "0 0 25%", overflow: "auto", wordWrap: "break-word" }}
          >
            {loading ? (
              <Spinner animation="border" size="sm" />
            ) : (
              sortedItems?.map((item, index) => {
                return (
                  <div
                    key={item?.course_id || item?.subject_id || item?.section_id || `item-${index}`}
                    className={`${styles.item} ${styles.courseName} ${
                      item?.section_id && activeSectionId === item?.section_id
                        ? styles.activeItem
                        : ""
                    } ${
                      item?.subject_id && activeId === item?.subject_id
                        ? styles.activeItem
                        : ""
                    } ${
                      item?.course_id && activeId === item?.course_id
                        ? styles.activeItem
                        : ""
                    }`}
                    onClick={(event) => {
                      console.log("Item clicked:", item);
                      event.stopPropagation();
                      handleItemClick(event, item);
                    }}
                  >
                    {item?.attachments && (
                      <Image
                        src={`${import.meta.env.VITE_BASE_URL}${item?.attachments}`}
                        width="30px"
                        roundedCircle
                        className={styles.itemImage}
                      />
                    )}
                    <span>{item?.name || item?.section_name}</span>
                    <MdOutlineKeyboardArrowRight size={16} className={styles.arrowIcon} />
                  </div>
                );
              })
            )}
          </div>
          <div
            className={styles.subItemsContainer}
            style={{ flex: "0 0 35%", overflow: "auto", wordWrap: "break-word" }}
          >
            {subItems?.length > 0 ? (
              subItems?.map((subItem, index) => (
                <Card
                  key={subItem?.subcourse_id || subItem?.topic_id || `subItem-${index}`} // Add fallback key using index
                  className={`${styles.subItemCard} ${styles.commonCard} ${
                    activeTopicId === subItem?.topic_id ? styles.activeTopic : ""
                  }`}
                  onClick={(event) => {
                    event.stopPropagation(); // Prevent dropdown from closing
                    if (isSubjects) {
                      setSubTopics(subItem?.subtopics || []); // Set subtopics on click
                      setActiveTopicId(subItem?.topic_id); // Highlight clicked topic
                    } else {
                      handleSubCourseClick(activeId);
                    }
                  }}
                >
                  <Card.Body
                    className={`${styles.subItemCardBody} ${styles.commonCardBody}`}
                    style={{ padding: "0.5rem", display: "flex", alignItems: "center", justifyContent: "space-between" }}
                  >
                    <div style={{ display: "flex", alignItems: "center", gap: "0.5rem" }}>
                      {subItem?.attachments && (
                        <Image
                          src={`${import.meta.env.VITE_BASE_URL}${subItem?.attachments}`}
                          width="30px"
                          className={styles.subItemImage}
                        />
                      )}
                      <Card.Text className={styles.subItemText}>{subItem?.name}</Card.Text>
                    </div>
                    <MdOutlineKeyboardArrowRight size={16} className={styles.arrowIcon} />
                  </Card.Body>
                </Card>
              ))
            ) : (
              <p className={styles.noItemsText}>No items available</p>
            )}
          </div>
          {isSubjects && subTopics?.length > 0 && (
            <div
              className={styles.subTopicsContainer}
              style={{ flex: "0 0 38%", overflow: "auto", wordWrap: "break-word" }}
            >
              {subTopics.map((subTopic, index) => (
                <Card
                  key={subTopic?.subtopic_id || `subTopic-${index}`} // Add fallback key using index
                  className={`${styles.subTopicCard} ${styles.commonCard}`}
                  onClick={(event) => {
                    event.stopPropagation(); // Prevent dropdown from closing
                    if (subTopic?.subtopic_id) {
                      handleSubTopicClick(); // Navigate to signup only if subtopic is clicked
                    }
                  }}
                >
                  <Card.Body
                    className={`${styles.subTopicCardBody} ${styles.commonCardBody}`}
                    style={{ padding: "0.5rem", display: "flex", alignItems: "center", justifyContent: "space-between" }}
                  >
                    <div style={{ display: "flex", alignItems: "center", gap: "0.5rem" }}>
                      <Card.Text className={styles.subTopicText}>{subTopic?.name}</Card.Text>
                    </div>
                    <MdOutlineKeyboardArrowRight size={16} className={styles.arrowIcon} />
                  </Card.Body>
                </Card>
              ))}
            </div>
          )}
        </div>
      </NavDropdown>
    );
  };

  // Dynamic Test Series Schema
  const testSeriesSchema = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": "Test Series",
    "itemListElement": courses.map((course, idx) => ({
      "@type": "Course",
      "position": idx + 1,
      "name": course?.name,
      "url": "https://shashtrarth.com/"
    }))
  };

  // Dynamic Subjects Schema (from subjects and sections)
  const subjectsSchema = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": "Subjects",
    "itemListElement": [
      ...subjects.map((sub, idx) => ({
        "@type": "Thing",
        "position": idx + 1,
        "name": sub?.name,
        "url": "https://shashtrarth.com/"
      })),
      ...sections.map((section, idx) => ({
        "@type": "Thing",
        "position": subjects.length + idx + 1,
        "name": section?.section_name,
        "url": "https://shashtrarth.com/"
      }))
    ]
  };

  return (
    <>
      <Helmet>
        {/* Test Series Schema (dynamic) */}
        <script type="application/ld+json">
          {JSON.stringify(testSeriesSchema)}
        </script>
        {/* Subjects Schema (dynamic) */}
        <script type="application/ld+json">
          {JSON.stringify(subjectsSchema)}
        </script>
      </Helmet>
      <Navbar
        expand="lg"
        className="shadow-sm"
        fixed="top"
        style={{
          backgroundColor: isDarkMode ? '#1a1a1a' : '#ffffff',
          borderBottom: `1px solid ${isDarkMode ? '#333' : '#dee2e6'}`
        }}
      >
        <Container>
          <Navbar.Brand as={Link} to="/" className="cabin-sketch-bold">
            <Image src="/logo.png" width={180} />
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="navbar-nav" />
          <Navbar.Collapse id="navbar-nav">
            <Nav className="mx-auto">
              <Nav.Link as={Link} to="/">Home</Nav.Link>
              <ResponsiveDropdown
                title="Test Series"
                items={memoizedCourses}
                activeId={activeCourseId}
                setActiveId={setActiveCourseId}
                setSubItems={setSubCourses}
                subItems={subCourses}
                loading={loadingCourses}
                isSubjects={false}
              />
              <ResponsiveDropdown
                title="Subjects"
                items={[...memoizedSubjects, ...memoizedSections]} // Combine subjects and sections
                activeId={activeSubjectId}
                setActiveId={setActiveSubjectId}
                setSubItems={setTopics}
                subItems={topics}
                loading={loadingSubjects}
                isSubjects={true}
              />
              <Nav.Link href="/home#packages">Packages</Nav.Link>
              <Nav.Link as={Link} to="/events">Events</Nav.Link>
              <Nav.Link href="/home#exams">Exam and Results</Nav.Link>
              <Nav.Link href="/home#blogs">Blogs</Nav.Link>
            </Nav>

            {/* Theme Toggle Dropdown */}
            <NavDropdown
              title={
                <span style={{ color: isDarkMode ? '#ffffff' : '#000000' }}>
                  {getThemeIconComponent(themeMode)} {getThemeLabel(themeMode)}
                </span>
              }
              id="theme-dropdown"
              className="me-3"
              style={{ color: isDarkMode ? '#ffffff' : '#000000' }}
            >
              <NavDropdown.Item
                onClick={() => handleThemeSelection('system')}
                active={themeMode === 'system'}
                style={{
                  backgroundColor: themeMode === 'system' ? '#198754' : 'transparent',
                  color: themeMode === 'system' ? 'white' : (isDarkMode ? '#ffffff' : '#000000')
                }}
              >
                <BsDisplay className="me-2" />
                System
                {themeMode === 'system' && <span className="ms-auto">✓</span>}
              </NavDropdown.Item>
              <NavDropdown.Item
                onClick={() => handleThemeSelection('light')}
                active={themeMode === 'light'}
                style={{
                  backgroundColor: themeMode === 'light' ? '#198754' : 'transparent',
                  color: themeMode === 'light' ? 'white' : (isDarkMode ? '#ffffff' : '#000000')
                }}
              >
                <BsSun className="me-2" />
                Light
                {themeMode === 'light' && <span className="ms-auto">✓</span>}
              </NavDropdown.Item>
              <NavDropdown.Item
                onClick={() => handleThemeSelection('dark')}
                active={themeMode === 'dark'}
                style={{
                  backgroundColor: themeMode === 'dark' ? '#198754' : 'transparent',
                  color: themeMode === 'dark' ? 'white' : (isDarkMode ? '#ffffff' : '#000000')
                }}
              >
                <BsMoon className="me-2" />
                Dark
                {themeMode === 'dark' && <span className="ms-auto">✓</span>}
              </NavDropdown.Item>
            </NavDropdown>

            <div className="d-flex gap-2">
              <Link to="/login" style={{ textDecoration: "none" }}>
                <Button
                  variant="outline-success"
                  className="d-flex justify-content-center align-items-center gap-1"
                >
                  <FaChalkboardTeacher />
                  Login
                </Button>
              </Link>
              <Link to="/signup" style={{ textDecoration: "none" }}>
                <Button
                  variant="outline-primary"
                  className="d-flex justify-content-center align-items-center gap-1"
                >
                  <FaUserPlus /> {/* Replace SVG with React icon */}
                  Register
                </Button>
              </Link>
            </div>
          </Navbar.Collapse>
        </Container>
      </Navbar>
    </>
  );
};

export default NavBar;
