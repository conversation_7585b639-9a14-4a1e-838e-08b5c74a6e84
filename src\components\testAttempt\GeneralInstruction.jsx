import React from "react";

const GeneralInstruction = () => {
  return (
    <>
      <div className="mb-5" style={{ height: "80vh", overflowY: "auto" }}>
        <h5>General Instructions</h5>
        <ol className="ms-3 pb-3">
          <li>
            The clock will be set at the server. The countdown timer at the top
            right corner of screen will display the remaining time available for
            you to complete the examination. When the timer reaches zero, the
            examination will end by itself. You need not terminate the
            examination or submit your paper.
          </li>
          <li>
            The Question Palette displayed on the right side of screen will show
            the status of each question using one of the following symbols:
            <ul>
              <li>You have not visited the question yet.</li>
              <li>You have not answered the question.</li>
              <li>You have answered the question.</li>
              <li>
                You have NOT answered the question, but have marked the question
                for review.
              </li>
              <li>You have answered the question, but marked it for review.</li>
            </ul>
          </li>
          <p style={{ marginLeft: "-45px" }} className="mt-2">
            The <strong>Mark For Review</strong> status for a question simply
            indicates that you would like to look at that question again. If a
            question is answered, but marked for review, then the answer will be
            considered for evaluation unless the status is modified by the
            candidate.
          </p>

          <h5 className="mt-3" style={{ marginLeft: "-45px" }}>
            Navigating to a Question:
          </h5>
          <li>
            To answer a question, do the following:
            <ol>
              <li>
                Click on the question number in the Question Palette at the
                right of your screen to go to that numbered question directly.
                Note that using this option does NOT save your answer to the
                current question.
              </li>
              <li>
                Click on <strong>Save & Next</strong> to save your answer for
                the current question and then go to the next question.
              </li>
              <li>
                Click on <strong>Mark for Review</strong> & Next to save your
                answer for the current question and also mark it for review, and
                then go to the next question.
              </li>
            </ol>
          </li>
          <p className="mt-2">
            Note that your answer for the current question will not be saved, if
            you navigate to another question directly by clicking on a question
            number without saving the answer to the previous question.
          </p>
          <p>
            You can view all the questions by clicking on the{" "}
            <strong>Question Paper</strong> button.{" "}
            <span className="text-danger">
              This feature is provided, so that if you want you can just see the
              entire question paper at a glance.
            </span>
          </p>
          {/* Add more instructions */}
          <h5 className="" style={{ marginLeft: "-45px" }}>
            Answering Questions
          </h5>
          <li>
            Procedure for answering a multiple choice (MCQ) type question:
            <ol>
              <li>
                Choose one answer from the 4 options (A,B,C,D) given below the
                question, click on the bubble placed before the chosen option.
              </li>
              <li>
                To deselect your chosen answer, click on the bubble of the
                chosen option again or click on the{" "}
                <strong>Clear Response</strong> button
              </li>
              <li>
                To change your chosen answer, click on the bubble of another
                option.
              </li>
              <li>
                To save your answer, you MUST click on the{" "}
                <strong>Save & Next</strong>
              </li>
            </ol>
          </li>
          <li>
            Procedure for answering a numerical answer type question:
            <ol>
              <li>
                To enter a number as your answer, use the virtual numerical
                keypad.
              </li>
              <li>
                A fraction (e.g. -0.3 or -.3) can be entered as an answer with
                or without "0" before the decimal point.{" "}
                <span className="text-danger">
                  As many as four decimal points, e.g. 12.5435 or 0.003 ог
                  -932.6711 or 12.82 can be entered.
                </span>
              </li>
              <li>To clear your answer, click on the Clear Response button</li>
              <li>To save your answer, you MUST click on the Save & Next</li>
            </ol>
          </li>
          <li>
            To mark a question for review, click on the{" "}
            <strong>Mark for Review & Next</strong> button. If an answer is
            selected (for MCQ/MCAQ) entered (for numerical answer type) for a
            question that is <strong>Marked for Review</strong>, that answer
            will be considered in the evaluation unless the status is modified
            by the candidate.
          </li>
          <li>
            To change your answer to a question that has already been answered,
            first select that question for answering and then follow the
            procedure for answering that type of question.
          </li>
          <li>
            Note that ONLY Questions for which answers are{" "}
            <strong>saved</strong> or <strong>marked for review</strong> after
            answering will be considered for evaluation.
          </li>
          <li>
            Sections in this question paper are displayed on the top bar of the
            screen. Questions in a Section can be viewed by clicking on the name
            of that Section. The Section you are currently viewing will be
            highlighted.
          </li>
          <li>
            After clicking the <strong>Save & Next</strong> button for the last
            question in a Section, you will automatically be taken to the first
            question of the next Section in sequence.
          </li>
          <li>
            You can move the mouse cursor over the name of a Section to view the
            answering status for that Section.
          </li>
        </ol>
      </div>
    </>
  );
};

export default GeneralInstruction;
