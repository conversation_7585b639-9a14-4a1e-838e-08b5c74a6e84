import React from "react";
import { <PERSON><PERSON>, <PERSON>, Col, Container, Row } from "react-bootstrap";
import MyAttmptedTests from "./MyAttmptedTests";
import RaiseQuestion from "../../../commonCompoenents/RaiseQuestion";

const StudentAttemptedTests = () => {
  const myAttemptedTests = [
    {
        id: 1,
      title: "SBI Clerk Prelims - (Great Go): All India Rank Mega Live Test",
      duration: "60 Mins.",
      marks: "100 Marks",
      date: "19 Jan, 9:00 to 21 Jan, 23:59",
      button: "Start Now",
      syllabus: "English, Hindi",
    },
    {
        id: 2,
      title: "APCOB Assistant Manager - (Rank Matters): Mini Live Test",
      duration: "20 Mins.",
      marks: "30 Marks",
      date: "17 Jan, 19:00 to 20 Jan, 23:59",
      button: "Start Now",
      syllabus: "English",
    },
    {
        id: 3,
      title: "Banking & Financial Awareness - (<PERSON>am Prodigy): Mini Live Test",
      duration: "8 Mins.",
      marks: "20 Marks",
      date: "17 Jan, 9:00 to 19 Jan, 21:00",
      button: "Start Now",
      syllabus: "English, Hindi",
    },
    {
        id: 4,
      title: "Banking & Financial Awareness - (<PERSON>ll Spark): Mini Live Test",
      duration: "8 Mins.",
      marks: "20 Marks",
      date: "20 Jan, 9:00 to 22 Jan, 21:00",
      button: "Register",
      syllabus: "English, Hindi",
    },
    {
        id: 5,
      title: "SBI Clerk - (Reasoning Ability): Mini Live Test",
      duration: "12 Mins.",
      marks: "20 Marks",
      date: "19 Jan, 9:00 to 21 Jan, 21:00",
      button: "Start Now",
      syllabus: "English, Hindi",
    },
  ];
  return (
    <>
    <RaiseQuestion/>
      <Container className="mt-5">
        <Row className="justify-content-center">
          <Col md={10}>
            <h4 className="">Your <span className=" text-success">Attempted Tests</span></h4>
          </Col>
        </Row>
        <Row className="justify-content-center mt-4 mb-4">
          <Col md={10}>
            <MyAttmptedTests myAttemptedTests={myAttemptedTests} />
          </Col>
        </Row>

        <hr></hr>

        <Row className="justify-content-center mt-5">
          <Col md={10}>
            <h4 className="text-start mb-4 text-dark">
              More Tests For You
            </h4>
          </Col>
        </Row>

        <Row className="justify-content-center">
          <Col md={10}>
            <Row>
              {myAttemptedTests.map((test) => (
                <Col key={test.id} md={6} lg={3} className="mb-4">
                  <Card
                    className="bg-light rounded-4 shadow-sm border-1"
                    style={{ minHeight: "200px" }}
                  >
                    <Card.Body>
                      {/* Smaller title */}
                      <Card.Title className="fs-6">{test.title}</Card.Title>
                      <div className="d-flex justify-content-start mb-2 gap-2">
                        <small>
                          <i className="fa-solid fa-clock"></i> {test.duration}
                        </small>
                        <small>
                          <i className="fa-solid fa-star"></i> {test.marks}
                        </small>
                      </div>
                      <div className="d-flex justify-content-between align-items-center">
                        <small>{test.date}</small>
                      </div>
                      <button className="btn btn-success mt-4 w-100">
                        Register
                      </button>
                    </Card.Body>
                  </Card>
                </Col>
              ))}
            </Row>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default StudentAttemptedTests;
