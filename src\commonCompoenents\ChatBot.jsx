import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { sendChatMessage } from '../redux/slice/chatBotSlice';
import { IoMdSend, IoMdClose } from 'react-icons/io';
import { BsRobot } from 'react-icons/bs';

const ChatBot = ({ onClose }) => {
  const dispatch = useDispatch();
  const accessToken = useSelector((state) => state?.student?.student?.JWT_Token?.access);
  const [query, setQuery] = useState('');
  const [messages, setMessages] = useState([]);

  const formatBoldText = (text) => {
    const parts = text.split(/(\*\*.*?\*\*)/g);
    return parts.map((part, index) =>
      part.startsWith('**') && part.endsWith('**') ? (
        <strong key={index}>{part.slice(2, -2)}</strong>
      ) : (
        <span key={index}>{part}</span>
      )
    );
  };

  const handleSend = async () => {
    if (!query.trim()) return;

    const newUserMessage = { text: query, type: 'user' };
    setMessages((prev) => [...prev, newUserMessage]);

    if (!accessToken) {
      setMessages((prev) => [
        ...prev,
        { text: '⚠️ Please login first to continue chatting with me.', type: 'bot' },
      ]);
      setQuery('');
      return;
    }

    try {
      const result = await dispatch(sendChatMessage({ query })).unwrap();
      const botResponses = result.results.map((r) => ({
        text: r.explanation,
        type: 'bot',
      }));
      setMessages((prev) => [...prev, ...botResponses]);
    } catch (error) {
      setMessages((prev) => [
        ...prev,
        { text: '❌ Something went wrong. Please try again later.', type: 'bot' },
      ]);
    }

    setQuery('');
  };

  return (
    <div style={styles.chatBotContainer}>
      <div style={styles.header}>
        <span className="fs-6">
          <BsRobot className="fs-1 me-2" /> Acharya AI Chat Bot
        </span>
        <IoMdClose size={20} onClick={onClose} style={{ cursor: 'pointer' }} />
      </div>

      <div style={styles.messages}>
        {messages.map((msg, idx) => (
          <div
            key={idx}
            style={{
              ...styles.message,
              alignSelf: msg.type === 'user' ? 'flex-end' : 'flex-start',
              backgroundColor: msg.type === 'user' ? '#007bff' : '#f1f1f1',
              color: msg.type === 'user' ? '#fff' : '#000',
            }}
          >
            {formatBoldText(msg.text)}
          </div>
        ))}
      </div>

      <div style={styles.inputArea}>
        <input
          type="text"
          placeholder="Ask me anything..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && handleSend()}
          style={styles.input}
        />
        <IoMdSend size={24} onClick={handleSend} style={styles.sendIcon} />
      </div>
    </div>
  );
};

const styles = {
  chatBotContainer: {
    position: 'fixed',
    bottom: '80px',
    right: '20px',
    width: '350px',
    height: '500px',
    backgroundColor: '#fff',
    borderRadius: '16px',
    boxShadow: '0 4px 24px rgba(0,0,0,0.2)',
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    zIndex: 9999,
  },
  header: {
    backgroundColor: '#007bff',
    color: '#fff',
    padding: '12px 16px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  messages: {
    flex: 1,
    padding: '12px',
    overflowY: 'auto',
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    backgroundColor: '#f9f9f9',
  },
  message: {
    padding: '10px 14px',
    borderRadius: '18px',
    maxWidth: '80%',
    fontSize: '14px',
    whiteSpace: 'pre-wrap',
  },
  inputArea: {
    display: 'flex',
    padding: '10px',
    borderTop: '1px solid #ddd',
    alignItems: 'center',
    gap: '8px',
  },
  input: {
    flex: 1,
    padding: '10px 14px',
    borderRadius: '20px',
    border: '1px solid #ccc',
    fontSize: '14px',
    outline: 'none',
  },
  sendIcon: {
    cursor: 'pointer',
    color: '#007bff',
  },
};

export default ChatBot;
