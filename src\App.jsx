import { getToken, onMessage } from "firebase/messaging";
import { messaging, onForegroundMessage } from "../firebase";
import { useEffect, useState } from "react";
import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import { toast, Toaster } from "react-hot-toast"; 
import { useDispatch } from "react-redux";
import { createDevice } from "./redux/slice/FCMSlice"; 
import HomePage from "./home/<USER>/HomePage";
import ChooseAuth from "./dashboard/pages/ChooseAuth";
import Signup from "./home/<USER>/Signup";
import Login from "./home/<USER>/Login";
import Rewards from "./dashboard/pages/Rewards";
import TestAttempt from "./components/testAttempt/TestAttempt";
import ScratchCards from "./dashboard/components/ScratchCards";
import Profile from "./dashboard/pages/Profile";
import Layout from "./dashboard/pages/Layout";
import Home from "./dashboard/pages/Home";
import Test from "./components/dashboard/testSeries/Test";
import PrivacyPolicy from "./home/<USER>/PrivacyPolicy";
import TermsConditions from "./home/<USER>/TermsConditions";
import RefundPolicy from "./home/<USER>/RefundPolicy";
import ExamInfo from "./components/examInformation/ExamInfo";
import StudentAttemptedTests from "./components/dashboard/attemptedTest/StudentAttemptedTests";
import StudentTestSeries from "./components/dashboard/testSeries/StudentTestSeries";
import Overview from "./components/examInformation/Overview";
import AdmitCard from "./components/examInformation/AdmitCard";
import Syllabus from "./components/examInformation/Syllabus";
import ElibilityCriteria from "./components/examInformation/ElibilityCriteria";
import Result from "./components/examInformation/Result";
import SalaryInfo from "./components/examInformation/SalaryInfo";
import Results from "./dashboard/pages/Results";
import ExamDashboard from "./components/testAttempt/ExamDashboard";
import Membership from "./dashboard/pages/Membership";
import Queries from "./dashboard/pages/Quories";
import AttemptedTestSeries from "./dashboard/pages/AttemptedTestSeries";
import Events from "./home/<USER>/Events";
import NavBar from "./commonCompoenents/NavBar";
import Packages from "./dashboard/pages/Packages";
import AboutUs from "./home/<USER>/AboutUs";
import ProgressAndFriends from "./dashboard/pages/ProgressAndFriends";
import PlayStoreDownload from "./home/<USER>/PlayStoreDownload";
import ContactUs from "./home/<USER>/ContactUs";
import ReferAndEarn from "./home/<USER>/ReferAndEarn";
import ThemeWrapper from "./components/ThemeWrapper";
import ThemeTest from "./components/ThemeTest";
import DownloadTest from "./dashboard/pages/DownloadTest";
import TestDownloadPage from "./dashboard/pages/TestDownloadPage";
import BottomNav from './commonCompoenents/BottomNav';
import PopupDisplay from './commonCompoenents/PopupDisplay';
import PopupTestPage from './components/PopupTestPage';
import AppLauncherPrompt from "./commonCompoenents/AppLauncherPrompt";

function App() {
  const dispatch = useDispatch();
  const [isDeviceRegistered, setIsDeviceRegistered] = useState(false); // Track dispatch status

  const requestNotificationPermission = async () => {
    const permission = await Notification.requestPermission();
    if (permission === "granted") {
      try {
        const token = await getToken(messaging, {
          vapidKey: import.meta.env.VITE_VAPID_KEY,
        });
        if (token && !isDeviceRegistered) { // Ensure action is dispatched only once
          console.log("FCM Token:", token);

          // Dispatch createDevice action
          dispatch(
            createDevice({
              registration_token: token,
              device_type: "web",
              is_registered: false,
              subscription_type: "trial",
              site: "https://shashtrarth.com/",
              interest: [],
              registered_courses: [],
            })
          );

          setIsDeviceRegistered(true); // Mark as dispatched
          return token;
        }
      } catch (error) {
        console.error("Error getting FCM token:", error);
      }
    } else if (permission === "denied") {
      alert(
        "You denied the permission. Please grant it to use this app efficiently"
      );
    }
  };

  const handleForegroundMessage = (payload) => {
    console.log("Foreground message received:", payload);

    // Add a unique identifier to avoid duplicate handling
    const notificationId = payload?.notification?.title + payload?.notification?.body;
    if (window.lastNotificationId === notificationId) {
      console.warn("Duplicate notification ignored.");
      return;
    }
    window.lastNotificationId = notificationId;

    const { title = "No Title", body = "No Body", image, description, url, icon } = payload.notification || {};

    if (!title && !body) {
      console.warn("Notification payload is missing title and body.");
      return;
    }

    // Display notification using React Hot Toast
    toast.custom(
      (t) => (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            padding: "10px",
            background: "#fff",
            border: "1px solid #ddd",
            borderRadius: "8px",
            boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
            maxWidth: "300px",
            cursor: "pointer",
          }}
          onClick={() => {
            if (url) {
              window.open(url, "_blank");
            }
          }}
        >
          {image && (
            <img
              src={image}
              alt="Notification"
              style={{ width: "50px", height: "50px", marginRight: "10px" }}
            />
          )}
          <div>
            <strong>{title}</strong>
            <p>{body}</p>
            {description && <small>{description}</small>}
          </div>
        </div>
      ),
      { duration: 5000 }
    );
  };

  const handleBackgroundMessage = (payload) => {
    console.log("Background message received:", payload);
    const { title = "No Title", body = "No Body", image, url, icon } = payload.notification || {};

    if (!title && !body) {
      console.warn("Notification payload is missing title and body.");
      return;
    }

    // Use browser's native notification system
    if (Notification.permission === "granted") {
      const notification = new Notification(title, {
        body,
        icon: icon || '/favicon.ico', // Use the icon from payload, fallback to favicon
      });

      // Add click event to redirect to the URL
      notification.onclick = () => {
        if (url) {
          try {
            const validUrl = new URL(url); // Validate the URL
            window.open(validUrl.href, "_blank"); // Open in the default browser
          } catch (error) {
            console.error("Invalid URL in notification payload:", url);
          }
        }
      };
    } else {
      console.warn("Notification permission is not granted.");
    }
  };

  useEffect(() => {
    requestNotificationPermission();

    // Handle foreground messages
    const unsubscribeForeground = onForegroundMessage((payload) => {
      console.log("Foreground message received:", payload);
      handleForegroundMessage(payload);
    });

    // Handle background messages
    const unsubscribeBackground = onMessage(messaging, (payload) => {
      if (document.visibilityState === "hidden") {
        console.log("Background message received:", payload);
        handleBackgroundMessage(payload);
      }
    });

    // Cleanup subscriptions on unmount
    return () => {
      if (unsubscribeForeground) {
        unsubscribeForeground(); // Ensure foreground listener is removed
      }
      if (unsubscribeBackground) {
        unsubscribeBackground(); // Ensure background listener is removed
      }
    };
  }, []);

      // Only run on mobile browsers old script not used now

  // useEffect(() => {
  //   const userAgent = window.navigator.userAgent.toLowerCase();
  //   const isMobile = /android|iphone|ipad|ipod/i.test(userAgent);
  //   if (isMobile) {
  //     const script = document.createElement("script");
  //     script.src = "/open-app.js";
  //     script.async = true;
  //     document.body.appendChild(script);
  //     return () => {
  //       document.body.removeChild(script);
  //     };
  //   }
  // }, []);

  return (
    <>
      <Router>
        <ThemeWrapper>
           <AppLauncherPrompt />
          <Routes>
          {/* for home */}
          <Route path="/" element={<HomePage />} />
          <Route path="/home" element={<HomePage />} />
          <Route path="/privacy_policy" element={<PrivacyPolicy />} />
          <Route path="/about-us" element={<AboutUs />} />
          <Route path="/terms_condition" element={<TermsConditions />} />
          <Route path="/refund_policy" element={<RefundPolicy />} />
          <Route path="/signup/:courseId" element={<Signup />} />
          <Route path="/signup/" element={<Signup />} />
          <Route path="/students/signup-request/:referral_id" element={<Signup />} />
          <Route path="/login" element={<Login />} />
          <Route path="/login/:package_id" element={<Login />} />
          {/* after login  */}
          <Route path="/dashboard" element={<Layout />}>
            <Route index element={<Home />} />
            <Route path="profile" element={<Profile />} />
            <Route path="progress-friends" element={<ProgressAndFriends />} />
            <Route path="reward" element={<Rewards />} />
            <Route path="test_series" element={<StudentTestSeries />} />
            <Route path="test-series/:slug" element={<Test />} />
            <Route path="attempted_tests" element={<StudentAttemptedTests />} />
            <Route path="exam-info" element={<ExamInfo Component={Overview} />} />
            <Route path="exam-info/admit-card" element={<ExamInfo Component={AdmitCard} />} />
            <Route path="exam-info/syllabus" element={<ExamInfo Component={Syllabus} />} />
            <Route path="exam-info/eligibility-criteria" element={<ExamInfo Component={ElibilityCriteria} />} />
            <Route path="exam-info/result" element={<ExamInfo Component={Result} />} />
            <Route path="exam-info/salary" element={<ExamInfo Component={SalaryInfo} />} />
            <Route path="exam-info/pyq" element={<ExamInfo Component={Overview} />} />
          </Route>
          <Route path="/packages" element={<Packages/>} />          
          <Route path="/profile" element={<Profile />} />
          <Route path="/register" element={<ChooseAuth />} />
          <Route path="/membership" element={<Membership/>} />
          <Route path="/queries" element={<Queries/>} />
          <Route path="/attempted-test-series" element={<AttemptedTestSeries/>} />
          <Route path="/events" element={<Events/>} />
          <Route path="/test-result" element={<Results />} />
          <Route path="/download-app" element={<PlayStoreDownload />} />
          <Route path="/contact-us" element={<ContactUs />} />
          <Route path="/refer-and-earn" element={<ReferAndEarn />} />


          <Route path="/exam-dashboard" element={<ExamDashboard />} />
          <Route path="/theme-test" element={<ThemeTest />} />
          <Route path="/download-test" element={<DownloadTest />} />
          <Route path="/test-download-page" element={<TestDownloadPage />} />
          <Route path="/popup-test" element={<PopupTestPage />} />
          {/* <Route path="/test-attempt" element={<TestAttempt />} /> */}
          </Routes>
          <BottomNav />
          <PopupDisplay />
        </ThemeWrapper>
      </Router>
    </>
  );
}

export default App;
