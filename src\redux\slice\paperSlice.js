import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

const BASE_URL = import.meta.env.VITE_BASE_URL;

// Function to get access token
const getAccessToken = (getState) => {
  const { student } = getState();
  return student?.student?.JWT_Token?.access || null;
};

const getStudentId = (getState) => {
  const { student } = getState();
  return student?.student?.student?.id || null;
}

// Async thunk for creating a paper
export const createPaper = createAsyncThunk(
  "paper/createPaper",
  async ({ entity_id, entity_type="paper" }, { getState, rejectWithValue }) => {
    try {
      const token = getAccessToken(getState);
      const student_id = getStudentId(getState);
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GENERATE_PAPER}`, // Adjust endpoint if needed
        { student_id, entity_type, entity_id },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Something went wrong");
    }
  }
);

// Async thunk for submitting a test (sub-course ID is used)
export const submitTest = createAsyncThunk(
  "paper/submitTest",
  async ({ paper_id, answers }, { getState, rejectWithValue }) => {
    try {
      const token = getAccessToken(getState);
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SUBMIT_TEST}${paper_id}/`,
        { answers },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Something went wrong");
    }
  }
);

const paperSlice = createSlice({
  name: "paper",
  initialState: {
    loading: false,
    error: null,
    success: false,
    testResult: null, // Store the result of test submission
    testData: null,
  },
  reducers: {
    resetStatus: (state) => {
      state.loading = false;
      state.error = null;
      state.success = false;
      state.testResult = null;
      state.testData = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(createPaper.pending, (state) => {
        state.loading = true;
        // state.error = null;
        state.success = false;
      })
      .addCase(createPaper.fulfilled, (state, action) => {
        state.loading = false;
        // state.success = true;
        state.testData = action.payload;
      })
      .addCase(createPaper.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(submitTest.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(submitTest.fulfilled, (state, action) => {
        state.loading = false;
        state.testResult = action.payload;
      })
      .addCase(submitTest.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { resetStatus } = paperSlice.actions;
export default paperSlice.reducer;
