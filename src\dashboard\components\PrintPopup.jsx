import React from 'react';
import { Container, <PERSON>, <PERSON>, <PERSON>, But<PERSON> } from 'react-bootstrap';
import { FaCheckCircle, FaTrophy } from 'react-icons/fa';
import { BsArrowUpRightSquareFill } from 'react-icons/bs';
import { FaGooglePlay } from 'react-icons/fa'; // Added import for Play Store icon

const PrintPopup = () => {
  return (
    <Card className="bg-info text-dark p-4 border-0"
    style={{
        backgroundImage:
          "linear-gradient(90deg, rgba(252, 252, 252, 0.87) 10%, rgba(200, 239, 192, 0.88) 90%)",
      }}>
      <div className="text-center mb-3">
        <h5>Download Shashtrarth App</h5>
        <p>to see your performance</p>
      </div>

      <div className="d-flex justify-content-between align-items-center">
        <div>
          <h6>Get following benefits :</h6>
          <ul className="list-unstyled">
            <li><FaCheckCircle className="me-2 text-success" /> Better test taking experience</li>
            <li><FaCheckCircle className="me-2 text-success" /> Detailed Analysis</li>
            <li><FaCheckCircle className="me-2 text-success" /> Complete Solutions</li>
          </ul>
        </div>
      </div>

      <div className="text-center mt-3">
        <Button variant="success">
          <FaGooglePlay className="me-2" /> Get Shashtrarth App
        </Button>
      </div>
      <div className="text-center mt-3">
        <small> coming soon ! </small>
      </div>
    </Card>
  );
};

export default PrintPopup;