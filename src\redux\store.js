import { configureStore } from "@reduxjs/toolkit";
import storage from "redux-persist/lib/storage";
import { combineReducers } from "redux";
import persistReducer from "redux-persist/es/persistReducer";
import reportErrorReducer, { reportError } from "../redux/slice/reportErrorSlice";

// Import Reducers
import bannerReducer from "../redux/slice/bannerSlice";
import blogReducer from "../redux/slice/blogSlice";
import customerCareReducer from "../redux/slice/customerCareSlice";
import studentReducer from "../redux/slice/studentSlice";
import paperReducer from "../redux/slice/paperSlice";
import PageViewReducer from "../redux/slice/TrackPageViewSlice";
import signUpContentReducer from "../redux/slice/signupContentSlice";
import packageReducer from "../redux/slice/packageSlice";
import chatBotReducer from "../redux/slice/chatBotSlice";
import rewardReducer from "../redux/slice/rewardSlice";
import eventReducer from "../redux/slice/eventSlice";
import contactReducer from "../redux/slice/contactSlice"; // Import contactReducer

// Persist config for redux-persist
const persistConfig = {
  key: "root",
  storage,
  whitelist: ["student"], // Only persist the 'student' slice
};

// Combine all reducers
const rootReducer = combineReducers({
  blog: blogReducer,
  customerCare: customerCareReducer,
  student: studentReducer,
  banners: bannerReducer,
  paper: paperReducer,
  pageView: PageViewReducer,
  signupContent: signUpContentReducer,
  packages: packageReducer,
  chatBot: chatBotReducer,
  rewards: rewardReducer,
  events: eventReducer,
  errorReport: reportErrorReducer,
  contact: contactReducer, // Added contact slice
});

// Create persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

let previousErrors = {}; // Store previously reported errors
let errorTimeout = null; // Store timeout reference

// Error tracking middleware with optimization
const errorTrackingMiddleware = (storeAPI) => (next) => (action) => {
  const result = next(action); // Process the action first
  const state = storeAPI.getState(); // Get the updated state

  const errorData = extractErrors(state); // Extract errors

  if (Object.keys(errorData).length > 0 && hasNewErrors(errorData)) {
    // Clear previous timeout (if any)
    if (errorTimeout) {
      clearTimeout(errorTimeout);
    }    // Delay dispatching the error report by 2 seconds to prevent rapid calls
    errorTimeout = setTimeout(() => {
      storeAPI.dispatch(reportError({ 
        error_data: {
          ...errorData,
          app_type: 'React JS Web App'
        }
      }));
      previousErrors = errorData; // Update previous errors
    }, 2000);
  }

  return result;
};

// Function to extract errors from the store
const extractErrors = (state) => {
  let errors = {
    app_type: 'React JS Web App' // Adding app type identifier
  };

  for (const key in state) {
    if (state[key]?.error) {
      errors[key] = { error: state[key].error };
    }
  }

  return errors;
};

// Function to check if there are new errors not reported before
const hasNewErrors = (currentErrors) => {
  return JSON.stringify(previousErrors) !== JSON.stringify(currentErrors);
};

// Configure the Redux store
const store = configureStore({
  reducer: persistedReducer,
  devTools: import.meta.env.VITE_REDUX_DEVTOOLS === 'true', // Enable DevTools based on .env
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }).concat(errorTrackingMiddleware), // Add optimized error tracking middleware
});

export default store;
