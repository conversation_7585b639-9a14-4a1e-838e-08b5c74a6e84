import React, { useState, useEffect } from 'react';
import { Button, Form, Modal } from 'react-bootstrap';
import { useDispatch } from 'react-redux';
import { forgotPassword, verifyForgotPasswordOtp, resendOtp } from '../redux/slice/studentSlice';
import toast from 'react-hot-toast';
import { useTheme } from '../context/ThemeContext';

const ForgetAuth = ({ showForgetModal, setShowForgetModal }) => {
    const { isDarkMode, theme } = useTheme();
    const [forgetEmail, setForgetEmail] = useState("");
    const [newPassword, setNewPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [otp, setOtp] = useState("");
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [loading, setLoading] = useState(false);
    const [passwordLoad, setPasswordLoad] = useState(false);
    const [emailError, setemailError] = useState("");
    const [passwordError, setpasswordError] = useState("");
    const [cnfmPasswordError, setcnfmPasswordError] = useState("");
    const [otpError, setOtpError] = useState("");
    const [resendTimer, setResendTimer] = useState(60);
    const [resendAttempts, setResendAttempts] = useState(3);
    const dispatch = useDispatch();

    useEffect(() => {
        let timer;
        if (resendTimer > 0) {
            timer = setTimeout(() => setResendTimer(resendTimer - 1), 1000);
        }
        return () => clearTimeout(timer);
    }, [resendTimer]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setemailError("");
        if (!forgetEmail) {
            setemailError("Email should not be empty");
            return;
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(forgetEmail)) {
            setemailError("Please enter a valid email address");
            return;
        }
        setLoading(true);
        try {
            const res = await dispatch(forgotPassword(forgetEmail));
            if (res?.payload) {
                setShowNewPassword(true);
                toast.success(res.payload?.message);
            } else {
                toast.error("email is not existed");
            }
        } catch (error) {
            toast.error("email is not existed", error);
        } finally {
            setLoading(false);
        }
    };

    const handleChangePassword = async (e) => {
        e.preventDefault();

        // Reset error states
        setOtpError("");
        setpasswordError("");
        setcnfmPasswordError("");

        // Validate OTP
        if (!otp) {
            setOtpError("OTP is required");
            return;
        }
        if (otp.length < 6) {
            setOtpError("OTP must be at least 6 characters");
            return;
        }

        // Validate Password
        if (!newPassword) {
            setpasswordError("Password is required");
            return;
        }
        const passwordRegex = /^(?=.*[A-Z]|.*[@$!%*?&]).{8,}$/;
        if (!passwordRegex.test(newPassword)) {
            setpasswordError("Password must be at least 8 characters long and include at least one uppercase letter or one special character");
            return;
        }

        // Validate Confirm Password
        if (!confirmPassword) {
            setcnfmPasswordError("Confirm password is required");
            return;
        }
        if (newPassword !== confirmPassword) {
            setcnfmPasswordError("Passwords do not match");
            return;
        }

        setPasswordLoad(true);
        try {
            const res = await dispatch(verifyForgotPasswordOtp({
                email: forgetEmail,
                otp: otp,
                new_password: newPassword,
                confirm_password: confirmPassword
            }));
            if (res?.payload) {
                toast.success(res.payload?.otp || res.payload?.message);

                // Reset resend OTP state if password reset is successful
                if (res.payload?.message?.includes("Password reset successful")) {
                    setShowNewPassword(false);
                    setResendAttempts(3);
                    setResendTimer(60);
                    setForgetEmail("");
                    setOtp("");
                    setNewPassword("");
                    setConfirmPassword("");
                }

                setShowForgetModal(false);
            } else {
                toast.error("Error occurred during password change");
            }
        } catch (error) {
            toast.error(error?.message || "An error occurred");
        } finally {
            setPasswordLoad(false);
        }
    };

    const handleResendOtp = () => {
        if (resendAttempts > 0) {
            dispatch(resendOtp(forgetEmail));
            setResendAttempts(resendAttempts - 1);
            setResendTimer(60);
        }
    };

    return (
        <>
            <Modal
                show={showForgetModal}
                onHide={() => setShowForgetModal(false)}
                centered
                data-bs-theme={isDarkMode ? 'dark' : 'light'}
            >
                <Modal.Header
                    closeButton
                    style={{
                        backgroundColor: theme.colors.surface,
                        borderBottom: `1px solid ${theme.colors.border}`,
                        color: theme.colors.text
                    }}
                >
                    <Modal.Title style={{ color: theme.colors.text }}>
                        Forgot Password
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body
                    style={{
                        backgroundColor: theme.colors.surface,
                        color: theme.colors.text
                    }}
                >
                    {showNewPassword ? (
                        <>
                            <Form>
                                <Form.Group className="mb-3">
                                    <Form.Label style={{ color: theme.colors.text }}>OTP</Form.Label>
                                    <Form.Control
                                        type="text"
                                        placeholder="6 digit"
                                        onChange={(e) => {
                                            if (e.target.value.length > 6) {
                                                toast.error("OTP cannot exceed 6 characters");
                                                return;
                                            }
                                            setOtp(e.target.value);
                                        }}
                                        value={otp}
                                        isInvalid={!!otpError}
                                        style={{
                                            backgroundColor: theme.colors.inputBackground,
                                            borderColor: theme.colors.inputBorder,
                                            color: theme.colors.inputText
                                        }}
                                    />
                                    <Form.Control.Feedback type="invalid">
                                        {otpError}
                                    </Form.Control.Feedback>
                                </Form.Group>
                                <div className="d-flex justify-content-center align-items-center flex-column">
                                    <Button
                                        variant={resendAttempts === 0 ? "secondary" : resendTimer > 0 ? "secondary" : "primary"}
                                        onClick={handleResendOtp}
                                        disabled={resendTimer > 0 || resendAttempts === 0}
                                        className="mb-2"
                                    >
                                        {resendAttempts === 0
                                            ? "Max Attempts Reached"
                                            : resendTimer > 0
                                            ? `Resend OTP (${resendTimer}s)`
                                            : "Resend OTP"}
                                    </Button>
                                    <h6 className="text-center mb-3">
                                        {resendAttempts === 0
                                            ? "You have reached the maximum attempts"
                                            : `Attempts: ${3 - resendAttempts}/3`}
                                    </h6>
                                </div>
                                <Form.Group className="mb-3">
                                    <Form.Label style={{ color: theme.colors.text }}>New Password</Form.Label>
                                    <Form.Control
                                        type="password"
                                        placeholder="Enter new password"
                                        onChange={(e) => setNewPassword(e.target.value)}
                                        value={newPassword}
                                        isInvalid={!!passwordError}
                                        style={{
                                            backgroundColor: theme.colors.inputBackground,
                                            borderColor: theme.colors.inputBorder,
                                            color: theme.colors.inputText
                                        }}
                                    />
                                    <Form.Control.Feedback type="invalid">
                                        {passwordError}
                                    </Form.Control.Feedback>
                                </Form.Group>
                                <Form.Group className="mb-3">
                                    <Form.Label style={{ color: theme.colors.text }}>Confirm Password</Form.Label>
                                    <Form.Control
                                        type="password"
                                        placeholder="Confirm password"
                                        onChange={(e) => setConfirmPassword(e.target.value)}
                                        value={confirmPassword}
                                        isInvalid={!!cnfmPasswordError}
                                        style={{
                                            backgroundColor: theme.colors.inputBackground,
                                            borderColor: theme.colors.inputBorder,
                                            color: theme.colors.inputText
                                        }}
                                    />
                                    <Form.Control.Feedback type="invalid">
                                        {cnfmPasswordError}
                                    </Form.Control.Feedback>
                                </Form.Group>
                                <Button
                                    variant="outline-success"
                                    type="submit"
                                    className="w-100"
                                    onClick={handleChangePassword}
                                    disabled={passwordLoad}
                                >
                                    {passwordLoad ? 'Changing..' : 'Change Password'}
                                </Button>
                            </Form>
                        </>
                    ) : (
                        <>
                            <Form>
                                <Form.Group className="mb-3">
                                    <Form.Label style={{ color: theme.colors.text }}>Your Email</Form.Label>
                                    <Form.Control
                                        type="email"
                                        placeholder="Enter your email"
                                        value={forgetEmail}
                                        onChange={(e) => setForgetEmail(e.target.value)}
                                        isInvalid={!!emailError}
                                        style={{
                                            backgroundColor: theme.colors.inputBackground,
                                            borderColor: theme.colors.inputBorder,
                                            color: theme.colors.inputText
                                        }}
                                    />
                                    <Form.Control.Feedback type="invalid">
                                        {emailError}
                                    </Form.Control.Feedback>
                                </Form.Group>

                                <Button variant="outline-success" type="submit" className="w-100" onClick={handleSubmit} disabled={loading}>
                                    {loading ? "Submitting.." : "Submit"}
                                </Button>
                            </Form>
                        </>
                    )}
                </Modal.Body>
            </Modal>
        </>
    );
};

export default ForgetAuth;
