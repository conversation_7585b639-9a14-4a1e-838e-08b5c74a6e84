import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>, Col, Container, Row } from "react-bootstrap";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css"; // Importing skeleton CSS
import { useTheme } from "../../context/ThemeContext";

const TestSeriesComponent = () => {
  const { isDarkMode, theme } = useTheme();
  const [loading, setLoading] = useState(true);

  // Simulate loading delay (e.g., data fetching)
  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 3000); // Simulate a 3-second load
    return () => clearTimeout(timer); // Clean up the timer on component unmount
  }, []);

  return (
    <Container className="mt-5">
      <Row className="mb-4 justify-content-center align-items-center">
        <Col>
        {
          loading ? (<Skeleton width={200} baseColor={isDarkMode ? "#2d2d2d" : "#e6ffe6"} highlightColor={isDarkMode ? "#404040" : "#c4f7c4"}/>) : (<h2 style={{ color: theme.colors.text }}>
            Our
            <span className="text-success fw-semibold"> Offerings </span>
          </h2>)      }
         
        </Col>
      </Row>
      <Row className="justify-content-center align-items-center">
        <Col>
          <Row>
            <Col md={6} lg={3} >
              <Card
                className="border-1 shadow-sm mb-4"
                style={{
                  cursor: "pointer",
                  backgroundColor: theme.colors.cardBackground,
                  borderColor: theme.colors.cardBorder,
                  color: theme.colors.cardText,
                  transition: 'all 0.3s ease'
                }}
              >
                {loading ? (
                  <>
                    <Skeleton height={200} width="100%" className="mx-auto d-block" baseColor={isDarkMode ? "#2d2d2d" : "#e6ffe6"} highlightColor={isDarkMode ? "#404040" : "#c4f7c4"} />
                    <Card.Body>
                      <Skeleton height={30} width="60%" className="mb-2" baseColor={isDarkMode ? "#2d2d2d" : "#e6ffe6"} highlightColor={isDarkMode ? "#404040" : "#c4f7c4"} />
                      <Skeleton height={20} width="80%" className="mb-3" baseColor={isDarkMode ? "#2d2d2d" : "#e6ffe6"} highlightColor={isDarkMode ? "#404040" : "#c4f7c4"} />
                      <div className="text-center">
                        <Skeleton height={40} width="50%" className="mx-auto" baseColor={isDarkMode ? "#2d2d2d" : "#e6ffe6"} highlightColor={isDarkMode ? "#404040" : "#c4f7c4"} />
                      </div>
                    </Card.Body>
                  </>
                ) : (
                  <>
                    <Card.Img
                      variant="top"
                      src="/mockTest.png"
                      className="mx-auto d-block"
                      style={{ height: "8rem", width: "8rem" }}
                    />
                    <Card.Body>
                      <Card.Title style={{ color: theme.colors.cardText }}>Mock Tests</Card.Title>
                      <Card.Text className="text-start" style={{ color: theme.colors.cardText }}>
                        Test Your Knowledge Exam-Ready Mock Tests!
                      </Card.Text>
                      <div className="text-center">
                        <a className="text-success" href="#">
                          EXPLORE COURSES
                        </a>
                      </div>
                    </Card.Body>
                  </>
                )}
              </Card>
            </Col>

            {/* <Col md={4}>
              <Card className="border-1 shadow-sm" style={{ cursor: "pointer" }}>
                {loading ? (
                  <>
                    <Skeleton height={200} width="100%" className="mx-auto d-block" />
                    <Card.Body>
                      <Skeleton height={30} width="60%" className="mb-2" />
                      <Skeleton height={20} width="80%" className="mb-3" />
                      <div className="text-center">
                        <Skeleton height={40} width="50%" className="mx-auto" />
                      </div>
                    </Card.Body>
                  </>
                ) : (
                  <>
                    <Card.Img
                      variant="top"
                      src="/liveTest.png"
                      className="mx-auto d-block"
                      style={{width: "8rem", height: "8rem" }}
                    />
                    <Card.Body>
                      <Card.Title>Live Tests</Card.Title>
                      <Card.Text className="text-start">
                       Compete in Real-Time Join Live Tests Now!
                      </Card.Text>
                      <div className="text-center">
                      <a className="text-success" href="#">
                          EXPLORE COURSES
                        </a>
                      </div>
                    </Card.Body>
                  </>
                )}
              </Card>
            </Col> */}

            <Col md={6} lg={3} >
              <Card
                className="border-1 shadow-sm mb-4"
                style={{
                  cursor: "pointer",
                  backgroundColor: theme.colors.cardBackground,
                  borderColor: theme.colors.cardBorder,
                  color: theme.colors.cardText,
                  transition: 'all 0.3s ease'
                }}
              >
                {loading ? (
                  <>
                    <Skeleton height={200} width="100%" className="mx-auto d-block" />
                    <Card.Body>
                      <Skeleton height={30} width="60%" className="mb-2" />
                      <Skeleton height={20} width="80%" className="mb-3" />
                      <div className="text-center">
                        <Skeleton height={40} width="50%" className="mx-auto" />
                      </div>
                    </Card.Body>
                  </>
                ) : (
                  <>
                    <Card.Img
                      variant="top"
                      src="/eBook.png"
                      className="mx-auto d-block"
                      style={{width: "8rem", height: "8rem" }}
                    />
                    <Card.Body>
                      <Card.Title>E-Books</Card.Title>
                      <Card.Text className="text-start">
                        Your Digital Library Download & Learn Anytime!
                      </Card.Text>
                      <div className="text-center">
                      <a className="text-success" href="#">
                          Coming Soon! 
                        </a>
                      </div>
                    </Card.Body>
                  </>
                )}
              </Card>
            </Col>

            <Col md={6} lg={3} >
              <Card className="border-1 shadow-sm mb-4" style={{ cursor: "pointer" }}>
                {loading ? (
                  <>
                    <Skeleton height={200} width="100%" className="mx-auto d-block" />
                    <Card.Body>
                      <Skeleton height={30} width="60%" className="mb-2" />
                      <Skeleton height={20} width="80%" className="mb-3" />
                      <div className="text-center">
                        <Skeleton height={40} width="50%" className="mx-auto" />
                      </div>
                    </Card.Body>
                  </>
                ) : (
                  <>
                    <Card.Img
                      variant="top"
                      src="/printedBook.png"
                      className="mx-auto d-block"
                      style={{width: "8rem", height: "8rem" }}
                    />
                    <Card.Body>
                      <Card.Title>Printed Books</Card.Title>
                      <Card.Text className="text-start">
                       Get Exam-Ready with Expert-Curated Books! 
                      </Card.Text>
                      <div className="text-center">
                      <a className="text-success" href="#">
                      Coming Soon! 
                        </a>
                      </div>
                    </Card.Body>
                  </>
                )}
              </Card>
            </Col>

            {/* <Col md={4}>
              <Card className="border-1 shadow-sm" style={{ cursor: "pointer" }}>
                {loading ? (
                  <>
                    <Skeleton height={200} width="100%" className="mx-auto d-block" />
                    <Card.Body>
                      <Skeleton height={30} width="60%" className="mb-2" />
                      <Skeleton height={20} width="80%" className="mb-3" />
                      <div className="text-center">
                        <Skeleton height={40} width="50%" className="mx-auto" />
                      </div>
                    </Card.Body>
                  </>
                ) : (
                  <>
                    <Card.Img
                      variant="top"
                      src="/topicWiseVideo.png"
                      className="mx-auto d-block"
                      style={{width: "8rem", height: "8rem" }}
                    />
                    <Card.Body>
                      <Card.Title>Topic Wise Video</Card.Title>
                      <Card.Text className="text-start">
                       Master Every Topic Watch & Learn!
                      </Card.Text>
                      <div className="text-center">
                      <a className="text-success" href="#">
                          EXPLORE COURSES
                        </a>
                      </div>
                    </Card.Body>
                  </>
                )}
              </Card>
            </Col> */}

            <Col md={6} lg={3} >
              <Card className="border-1 shadow-sm mb-4" style={{ cursor: "pointer" }}>
                {loading ? (
                  <>
                    <Skeleton height={200} width="100%" className="mx-auto d-block" />
                    <Card.Body>
                      <Skeleton height={30} width="60%" className="mb-2" />
                      <Skeleton height={20} width="80%" className="mb-3" />
                      <div className="text-center">
                        <Skeleton height={40} width="50%" className="mx-auto" />
                      </div>
                    </Card.Body>
                  </>
                ) : (
                  <>
                    <Card.Img
                      variant="top"
                      src="/freeStudy.png"
                      className="mx-auto d-block"
                      style={{width: "8rem", height: "8rem" }}
                    />
                    <Card.Body>
                      <Card.Title>Free Study Material</Card.Title>
                      <Card.Text className="text-start">
                       Boost Your Prep Access Free Resources!
                      </Card.Text>
                      <div className="text-center">
                      <a className="text-success" href="#">
                      Coming Soon! 
                        </a>
                      </div>
                    </Card.Body>
                  </>
                )}
              </Card>
            </Col>
          </Row>
        </Col>
      </Row>
    </Container>
  );
};

export default TestSeriesComponent;
