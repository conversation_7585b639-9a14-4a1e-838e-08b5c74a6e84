import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper function to get the authToken from Redux state
const getAuthToken = (getState) => {
  const state = getState();
  return {
    accessToken: state.customerCare?.access,
  };
};

// Create Device Thunk (No Authorization Required)
export const createDevice = createAsyncThunk(
  "fcm/createDevice",
  async (deviceData, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CREATE_DEVICE}`,
        deviceData
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error registering device");
    }
  }
);

// Patch Device Thunk (Requires Authorization)
export const patchDevice = createAsyncThunk(
  "fcm/patchDevice",
  async ({ deviceId, updateData }, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      if (!accessToken) throw new Error("Unauthorized");

      const response = await axios.patch(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PATCH_DEVICE}${deviceId}/`,
        updateData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error updating device");
    }
  }
);

const fcmSlice = createSlice({
  name: "fcm",
  initialState: {
    device: null,
    isLoading: false,
    error: null,
    isDeviceCreated: false, // Flag to track if the device is created
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Create Device
      .addCase(createDevice.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createDevice.fulfilled, (state, action) => {
        state.isLoading = false;
        state.device = action.payload;
        state.isDeviceCreated = true; // Set flag to true after successful creation
      })
      .addCase(createDevice.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Patch Device
      .addCase(patchDevice.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(patchDevice.fulfilled, (state, action) => {
        state.isLoading = false;
        state.device = { ...state.device, ...action.payload };
      })
      .addCase(patchDevice.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

// Thunk to dispatch createDevice only once after app load
export const initializeDevice = (deviceData) => async (dispatch, getState) => {
  const { isDeviceCreated } = getState().fcm;
  if (!isDeviceCreated) {
    await dispatch(createDevice(deviceData));
  }
};

export default fcmSlice.reducer;
