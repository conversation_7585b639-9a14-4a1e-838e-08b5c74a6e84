
import { Col, <PERSON>, Spinner, Container } from "react-bootstrap";

const LoadingScreen = ({text}) => {
  return (
    <Container fluid className="d-flex justify-content-center align-items-center min-vh-100">
      <Row>
        <Col xs={12} className="text-center">
          <Spinner animation="border" variant="success" role="status" />
          <p className="mt-3 text-success">{text || 'Loading Blog..'}</p>
        </Col>
      </Row>
    </Container>
  );
};

export default LoadingScreen;
