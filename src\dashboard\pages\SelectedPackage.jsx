import React, { useState } from 'react';
import Checkout from '../../components/student_home_page/Checkout'; // Import the Checkout component

const SelectedPackage = () => {
    const [checkoutDetails, setCheckoutDetails] = useState({
        packageName: "Selected Package Name",
        originalPrice: 1000,
        packageId: 1,
        descriptions: ["Feature 1", "Feature 2", "Feature 3"],
    });

    return (
        <Checkout checkoutDetails={checkoutDetails} setCheckoutDetails={setCheckoutDetails} />
    );
};

export default SelectedPackage;
