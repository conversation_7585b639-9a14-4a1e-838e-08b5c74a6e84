import React from 'react';
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaEnvelope } from "react-icons/fa";

export default function LeftFLoatingIcons() {
    return (
        <>
            <div
                style={{
                    position: "fixed",
                    bottom: "1.1rem",
                    left: "1.1rem",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: "10px",
                }}
            >
                {/* WhatsApp Icon */}
                <a
                    href="https://wa.me/+916207628282"
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                        backgroundColor: "#25D366",
                        color: "#fff",
                        padding: "10px",
                        borderRadius: "50%",
                        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
                        cursor: "pointer",
                    }}
                >
                    <FaWhatsapp size={24} />
                </a>

                {/* Call Icon */}
                <a
                    href="tel:+916207628282"
                    style={{
                        backgroundColor: "#007bff",
                        color: "#fff",
                        padding: "10px",
                        borderRadius: "50%",
                        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
                        cursor: "pointer",
                    }}
                >
                    <FaPhone size={24} />
                </a>

                {/* Email Icon */}
                <a
                    href="mailto:<EMAIL>"
                    style={{
                        backgroundColor: "#EA4335",
                        color: "#fff",
                        padding: "10px",
                        borderRadius: "50%",
                        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
                        cursor: "pointer",
                    }}
                >
                    <FaEnvelope size={24} />
                </a>
            </div>
        </>
    )
}
