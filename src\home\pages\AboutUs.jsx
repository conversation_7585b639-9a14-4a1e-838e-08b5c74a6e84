import React from 'react';
import NavBar from "../../commonCompoenents/NavBar";
import Footer from "../components/Footer";
import { Container, Row, Col, Card } from 'react-bootstrap';

const AboutUs = () => {
  return (
    <>
      <NavBar />
      <section style={{ backgroundColor: '#f5f5f5' }}>
        <Container className="mt-5">
          <Row className="text-center mb-5">
            <Col>
              <h1 className="mt-5 display-4 text-uppercase text-success cabin-sketch-bold">About Us</h1>
              <p className="lead cabin-sketch-regular">Learn. Practice. Compete. Improve. That’s the Shashtrath way.</p>
            </Col>
          </Row>
          <Row>
            <Col lg={12}>
              <Card className="border-0 mb-5">
                <Card.Body>
                  <p>
                    <strong>Shashtrath</strong> is an evolving digital platform built to empower students preparing for competitive exams across India. 
                    We believe that success in examinations is not just about hard work—it's about smart preparation, personalized insights, and community support.
                  </p>
                  <p>
                    Our platform offers <strong>mock tests</strong>, <strong>performance analytics</strong>, <strong>rank comparisons</strong>, and <strong>strategy-based insights</strong> 
                    to help every learner understand their strengths and improve where it matters most.
                  </p>
                  <p>
                    But we’re more than just a testing platform. With an integrated <strong>blog</strong>, real-time <strong>referral rewards</strong>, 
                    and a student-first mobile app, we’re creating an ecosystem that makes exam prep engaging, efficient, and empowering.
                  </p>
                  <p className="font-weight-bold">Learn. Practice. Compete. Improve.</p>
                  <p>That’s the Shashtrath way.</p>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </section>
      <Footer />
    </>
  );
};

export default AboutUs;
