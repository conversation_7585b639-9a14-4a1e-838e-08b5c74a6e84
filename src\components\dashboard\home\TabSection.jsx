import React, { useState } from 'react';
import { Container, Row, Col, Tab, Tabs } from 'react-bootstrap';
import Courses from './Courses';

// const Courses = () => <div>Courses Content</div>;
const FreeResources = () => <div>Free Resources Content</div>;
const TestSeries = () => <div>Test Series Content</div>;
const StudyNotes = () => <div>Study Notes Content</div>;

const TabSection = () => {
  const [key, setKey] = useState('courses');

  return (
    <Container className="my-5">
      <Row className="justify-content-center">
        <Col md={10}>
          <Tabs
            id="tab-navigation"
            activeKey={key}
            onSelect={(k) => setKey(k)}
            className="mb-3"
          >
            <Tab eventKey="courses" title="Courses">
              <Courses />
            </Tab>
            <Tab eventKey="freeResources" title="Free Resources">
              <FreeResources />
            </Tab>
            <Tab eventKey="testSeries" title="Test Series">
              <TestSeries />
            </Tab>
            <Tab eventKey="studyNotes" title="Study Notes">
              <StudyNotes />
            </Tab>
          </Tabs>
        </Col>
      </Row>
    </Container>
  );
};

export default TabSection;
