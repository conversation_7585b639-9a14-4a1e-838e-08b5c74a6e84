import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper to get auth token
const getAuthToken = (getState) => {
  const state = getState();
  return {
    accessToken: state.student.student["JWT_Token"].access,
  };
};

// Get Razorpay Config
export const getRazorpayConfig = createAsyncThunk(
  'subscription/getRazorpayConfig',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_RAZORPAY_CONFIG}`
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch Razorpay config');
    }
  }
);

//  Create Subscription
export const createSubscription = createAsyncThunk(
  'subscription/createSubscription',
  async (data, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);

      const headers = {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      };

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CREATE_SUBSCRIPTION}`,
        data,
        { headers }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to create subscription');
    }
  }
);

// Verify Payment
export const verifyPayment = createAsyncThunk(
  'subscription/verifyPayment',
  async (verificationData, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);

      const headers = {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      };

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_VERIFY_PAYMENT}`,
        verificationData,
        { headers }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to verify payment');
    }
  }
);

//  Validate Coupon Code
export const validateCouponCode = createAsyncThunk(
  'subscription/validateCouponCode',
  async (code, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);

      const headers = {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      };

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_VALIDATE_COUPON}`,
         code,
        { headers }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Invalid coupon code');
    }
  }
);

//  Validate Gift Card
export const validateGiftCard = createAsyncThunk(
  'subscription/validateGiftCard',
  async (code, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);

      const headers = {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      };

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_VALIDATE_GIFT_CARD}`,
         code ,
        { headers }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Invalid gift card code');
    }
  }
);

const subscriptionSlice = createSlice({
  name: 'subscription',
  initialState: {
    config: null,
    subscription: null,
    verifyStatus: null,
    couponValidation: null,
    giftCardValidation: null,
    loading: false,
    error: null,
  },
  reducers: {
    resetVerifyStatus: (state) => {
      state.verifyStatus = null;
    },
  },
  extraReducers: (builder) => {
    builder

      // Razorpay Config
      .addCase(getRazorpayConfig.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getRazorpayConfig.fulfilled, (state, action) => {
        state.loading = false;
        state.config = action.payload;
      })
      .addCase(getRazorpayConfig.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create Subscription
      .addCase(createSubscription.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSubscription.fulfilled, (state, action) => {
        state.loading = false;
        state.subscription = action.payload;
      })
      .addCase(createSubscription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Verify Payment
      .addCase(verifyPayment.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.verifyStatus = null;
      })
      .addCase(verifyPayment.fulfilled, (state, action) => {
        state.loading = false;
        state.verifyStatus = action.payload;
      })
      .addCase(verifyPayment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.verifyStatus = null;
      })

      // Validate Coupon Code
      .addCase(validateCouponCode.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.couponValidation = null;
      })
      .addCase(validateCouponCode.fulfilled, (state, action) => {
        state.loading = false;
        state.couponValidation = action.payload;
      })
      .addCase(validateCouponCode.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.couponValidation = null;
      })

      // Validate Gift Card
      .addCase(validateGiftCard.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.giftCardValidation = null;
      })
      .addCase(validateGiftCard.fulfilled, (state, action) => {
        state.loading = false;
        state.giftCardValidation = action.payload;
      })
      .addCase(validateGiftCard.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.giftCardValidation = null;
      });
  },
});

export const { resetVerifyStatus } = subscriptionSlice.actions;

export default subscriptionSlice.reducer;
