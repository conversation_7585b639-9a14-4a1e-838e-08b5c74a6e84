import React from "react";
import { Container, Table, Row, <PERSON>, <PERSON>, <PERSON><PERSON>, ProgressBar } from "react-bootstrap";
import { <PERSON>a<PERSON><PERSON><PERSON>, FaCheckCircle, FaExclamationCircle } from "react-icons/fa";
import ResultNavbar from "./ResultNavbar";
import MarksDistribution from "./MarksDistribution";
import QuestionDistribution from "./QuestionDistribution";
import CompareWithTopper from "./CompareWithTopper";
import testData2 from "../../dummyData/testData2";
import { useNavigate } from "react-router-dom";

const Results = () => {
  const navigate = useNavigate();
  const testData = testData2;
  const selectedAnswers = JSON.parse(localStorage.getItem("testData"));
  console.log(selectedAnswers);
  const totalQuestions = testData?.sections.reduce(
    (total, section) => total + section?.questions.length,
    0
  );
  const answeredQuestions = selectedAnswers.length;
  // const answeredQuestions = Object.keys(selectedAnswers).filter(
  //   (qId) => selectedAnswers[qId] !== undefined
  // ).length;
  const accuracy = ((answeredQuestions / totalQuestions) * 100).toFixed(2);
  const score = answeredQuestions; // Assume 1 point per question for simplicity
  const percentile = accuracy > 50 ? "Above Average" : "Below Average";
  console.log(selectedAnswers);

  return (
    <>
      <ResultNavbar testData={testData} />
      <Container style={{ marginTop: "80px" }} className="">
        {/* Overall Performance Summary */}
        
        {/* re attempt with pass pro  */}

        {/* <Row className="justify-content-center my-4">
          <Col md={4}>
            <Card className="bg-light border border-warning">
              <Card.Body>
                <h5 className="card-title">
                  Reattempt Test with Shashtrarth{" "}
                  <span className="badge bg-warning text-dark">PASS PRO</span>
                </h5>
                <p className="card-text">Learn from past mistakes & improve</p>
                <Button
                  variant="success"
                  className="w-100"
                  onClick={() => navigate("/test-attempt")}>
                  Reattempt Test
                </Button>
              </Card.Body>
            </Card>
          </Col>
        </Row> */}

        <Card className="mb-4">
          <Card.Body>
            <Card.Title className="h4">Overall Performance Summary</Card.Title>
            <Row className="d-flex justify-content-between align-items-center">
              {/* Rank */}
              <Col className="d-flex align-items-center">
                <FaTrophy color="#FFD700" size={24} />
                <span className="ms-2 fw-bold">
                  {score}/{totalQuestions}
                </span>
                <span className="ms-1 text-muted">Rank</span>
              </Col>

              {/* Score */}
              <Col className="d-flex align-items-center">
                <FaTrophy color="#FFD700" size={24} />
                <span className="ms-2 fw-bold">{score}</span>
                <span className="ms-1 text-muted">Score</span>
              </Col>

              {/* Attempted */}
              <Col className="d-flex align-items-center">
                <FaCheckCircle color="#28a745" size={24} />
                <span className="ms-2 fw-bold">{answeredQuestions}</span>
                <span className="ms-1 text-muted">Attempted</span>
              </Col>

              {/* Not Attempted */}
              <Col className="d-flex align-items-center">
                <FaExclamationCircle color="#dc3545" size={24} />
                <span className="ms-2 fw-bold">
                  {totalQuestions - answeredQuestions}
                </span>
                <span className="ms-1 text-muted">Not Attempted</span>
              </Col>

              {/* Accuracy */}
              <Col className="d-flex align-items-center">
                <FaCheckCircle color="#28a745" size={24} />
                <span className="ms-2 fw-bold">{accuracy}%</span>
                <span className="ms-1 text-muted">Accuracy</span>
              </Col>

              {/* Percentile */}
              <Col className="d-flex align-items-center">
                <FaTrophy color="#FFD700" size={24} />
                <span className="ms-2 fw-bold">{percentile}</span>
                <span className="ms-1 text-muted">Percentile</span>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Section-wise Summary */}
        {/* <Card>
          <Card.Body>
            <Card.Title className="h5">Section-wise Summary</Card.Title>
            <Table striped bordered hover responsive>
              <thead>
                <tr>
                  <th>Section Name</th>
                  <th>Answered</th>
                  <th>Not Attempted</th>
                  <th>Not Visited</th>
                </tr>
              </thead>
              <tbody>
                {testData?.sections.map((section, index) => {
                  const answered = section?.questions.filter((q) =>
                    selectedAnswers.some((ans) => ans.question_id === q.id)
                  ).length;
                  const notAttempted = section.questions.filter(
                    (q) =>
                      !selectedAnswers.some((ans) => ans.question_id === q.id)
                  ).length;
                  const notVisited = section.questions.filter(
                    (q, idx) =>
                      idx < answered &&
                      !selectedAnswers.some((ans) => ans.question_id === q.id)
                  ).length;
                  return (
                    <tr key={index}>
                      <td>{section.name}</td>
                      <td>{answered}</td>
                      <td>{notAttempted}</td>
                      <td>{notVisited}</td>
                    </tr>
                  );
                })}
              </tbody>
            </Table>
          </Card.Body>
        </Card> */}

        {/* Weakness and Strengths */}
        <Row className="justify-content-center mt-5">
          <Col md={12}>
            <h4>Your Weakness and Strengths</h4>
          </Col>
        </Row>
        <Row>
          {testData?.sections.map((section, index) => {
            const total = section?.questions.length;
            const correct = section?.questions.filter((q) =>
              selectedAnswers.some((ans) => ans.question_id === q.id)
            ).length;
            const sectionAccuracy =
              total > 0 ? ((correct / total) * 100).toFixed(2) : 0;

            return (
              <Col md={12} lg={12} key={section?.name} className="mb-3">
                <Card className="p-3 shadow-sm">
                  <Card.Body className="d-flex justify-content-start gap-5">
                    <div className="">
                      <Card.Title className="h6">{section?.name}</Card.Title>
                      <div className="d-flex flex-column">
                        <div>Accuracy %</div>
                        <ProgressBar
                          now={sectionAccuracy}
                          label={`${sectionAccuracy}%`}
                          variant="success"
                          className="mt-3"
                        />
                      </div>
                    </div>

                    {/* Question Status Display */}
                    <div className="mt-3 d-flex flex-wrap gap-2">
                      {section.questions.map((q, idx) => {
                        const isAttempted = selectedAnswers.some(
                          (ans) => ans.question_id === q.id
                        );
                        return (
                          <div
                            key={q?.id}
                            style={{
                              width: "32px",
                              height: "32px",
                              borderRadius: "50%",
                              backgroundColor: isAttempted
                                ? "#28a745"
                                : "#6c757d",
                              color: "white",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              fontSize: "0.9rem",
                              fontWeight: "bold",
                            }}>
                            {idx + 1}
                          </div>
                        );
                      })}
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            );
          })}
        </Row>

        {/* Question Distribution and Marks Distribution Chart  */}

        {/* <Row className="justify-content-start mt-5">
          <Col md={6} className="mb-md-0 mb-4">
            <MarksDistribution />
          </Col>
          <Col md={6}>
            <QuestionDistribution
              testData={testData}
              selectedAnswers={selectedAnswers}
            />
          </Col>
        </Row> */}

        {/* compare with topper */}

        {/* <Row className="justify-content-start mt-5 pb-4">
          <Col md={8}>
            <h4>Compare with Topper</h4>
            <CompareWithTopper />
          </Col>
        </Row> */}
      </Container>
    </>
  );
};

export default Results;
