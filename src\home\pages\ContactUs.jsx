import React from 'react';
import NavBar from '../../commonCompoenents/NavBar';
import Footer from '../components/Footer';
import { Container, Row, Col } from 'react-bootstrap';

const EMAIL = '<EMAIL>';
const WHATSAPP = '+************';
const PHONE = '+************';
const ADDRESS = 'Pinak Venture, F2/9 Jai Durga Society, Netaji Nagar, Hill No 3, 90ft Road, Sakinaka, Kurla West Mumbai, India';
const MAP_URL = 'https://maps.app.goo.gl/fQfjVzeNBH88xBP7A';

const ContactUs = () => {
  return (
    <>
      <NavBar />
      <div style={{ minHeight: '80vh', background: '#f5f6fa', paddingTop: window.innerWidth > 768 ? 80 : 40 }}>
        <Container fluid className="py-4 d-flex justify-content-center">
          <Row className="align-items-center g-4 w-100 justify-content-center">
            <Col xs={12} md={10} lg={10} xl={9} style={{ background: '#fff', borderRadius: 12, boxShadow: '0 2px 8px #0001', padding: 24, maxWidth: 900 }}>
              <Row className="align-items-center g-4">
                <Col xs={12} md={6}>
                  <h2 style={{ textAlign: 'center', marginBottom: 32, width: '100%' }}>Contact Us</h2>
                  <div style={{ marginBottom: 24 }}>
                    <a href={`mailto:${EMAIL}`} style={{ display: 'flex', alignItems: 'center', textDecoration: 'none', color: '#0078d4', marginBottom: 16 }}>
                      <span style={{ fontSize: 22, marginRight: 12 }}>📧</span>
                      <span>{EMAIL}</span>
                    </a>
                    <a href={`https://wa.me/${WHATSAPP.replace('+', '')}`} target="_blank" rel="noopener noreferrer" style={{ display: 'flex', alignItems: 'center', textDecoration: 'none', color: '#25D366', marginBottom: 16 }}>
                      <span style={{ fontSize: 22, marginRight: 12 }}>💬</span>
                      <span>WhatsApp: {WHATSAPP}</span>
                    </a>
                    <a href={`tel:${PHONE}`} style={{ display: 'flex', alignItems: 'center', textDecoration: 'none', color: '#34b7f1', marginBottom: 16 }}>
                      <span style={{ fontSize: 22, marginRight: 12 }}>📞</span>
                      <span>{PHONE}</span>
                    </a>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24, color: '#a259ec' }}>
                    <span style={{ fontSize: 22, marginRight: 12 }}>📍</span>
                    <span>{ADDRESS}</span>
                  </div>
                </Col>
                <Col xs={12} md={6} className="d-flex justify-content-center">
                  <div style={{ width: '100%', maxWidth: 500, borderRadius: 12, overflow: 'hidden', height: 300 }}>
                    <iframe
                      src="https://www.google.com/maps?q=Pinak+Venture,+F2/9+Jai+Durga+Society,+Netaji+Nagar,+Hill+No+3,+90ft+Road,+Sakinaka,+Kurla+West+Mumbai,+India&output=embed"
                      width="100%"
                      height="300"
                      style={{ border: 0 }}
                      allowFullScreen=""
                      loading="lazy"
                      referrerPolicy="no-referrer-when-downgrade"
                      title="Google Map"
                    ></iframe>
                  </div>
                </Col>
              </Row>
            </Col>
          </Row>
        </Container>
      </div>
      <Footer />
    </>
  );
};

export default ContactUs;
