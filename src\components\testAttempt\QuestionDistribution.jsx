import React from "react";
import { Bar } from "react-chartjs-2";
import { Card } from "react-bootstrap";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const QuestionDistribution = ({ testData, selectedAnswers }) => {
  // Calculate question distribution
  const totalQuestions = testData.sections.reduce((total, section) => total + section.questions.length, 0);
  const answered = selectedAnswers.length;
  const notAnswered = totalQuestions - answered;
  const skipped = testData?.sections.reduce(
    (total, section) =>
      total +
      section.questions.filter((q) => !selectedAnswers.some((ans) => ans.question_id === q.id)).length,
    0
  );

  const data = {
    labels: ["Answered", "Not Answered", "Skipped"],
    datasets: [
      {
        label: "Number of Questions",
        data: [answered, notAnswered, skipped],
        backgroundColor: ["#28a745", "#dc3545", "#6c757d"], // Success, Danger, Secondary colors
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        display: true,
      },
    },
    scales: {
      x: {
        title: {
          display: true,
          text: "Question Status",
        },
        grid: {
          display: false, // Removes vertical grid lines
        },
      },
      y: {
        title: {
          display: true,
          text: `Total Questions: ${totalQuestions}`,
        },
        min: 0,
        max: totalQuestions, // Set Y-axis max to total number of questions
        ticks: {
          stepSize: totalQuestions / 5, // Adjust step size dynamically (divides into 5 parts)
        },
      },
    },
  };

  return (
    <Card className="border-0 bg-light p-md-3 rounded">
      <Card.Body>
        <Card.Title className="h5">Question Distribution</Card.Title>
        <Bar data={data} options={options} />
      </Card.Body>
    </Card>
  );
};

export default QuestionDistribution;
