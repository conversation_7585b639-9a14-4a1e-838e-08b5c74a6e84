import React from 'react'
import { <PERSON><PERSON>, <PERSON>, Col, Image, Row } from 'react-bootstrap'
import { Link } from 'react-router-dom'

const MyTestSereis = ({mytestSeriesData}) => {
  return (
    <>
        <Row>
        {mytestSeriesData.map((series) => (
          <Col key={series.id} md={6} lg={3} className="mb-4">
            <Card
              className="bg-light rounded-4 shadow-sm border-1"
              style={{minHeight: "200px"}}>
              <Card.Body>
                <div className="d-flex align-items-center gap-2 mb-3">
                <Image
                variant="top"
                src={series.logo}
                alt={series.name}
                style={{ width: "36px", height: "36px", objectFit: "cover" }}
              />
                <Card.Title>{series.name}</Card.Title>
                </div>
                <Card.Text>
                  <strong>Total Tests:</strong> {series.totalTests}
                  <br />
                  <strong>Free Tests:</strong> {series.freeTests}
                </Card.Text>
                  <Button variant="success" as={Link} to="/dashboard/test-series" className="w-100">
                    Go to Test Series
                  </Button>
              </Card.Body>
            </Card>
          </Col>
        ))}
        </Row>
    </>
  )
}

export default MyTestSereis
