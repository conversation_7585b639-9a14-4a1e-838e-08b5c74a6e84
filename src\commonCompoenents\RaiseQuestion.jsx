import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, Modal, Form } from "react-bootstrap";
import Swal from "sweetalert2";
import { useDispatch, useSelector } from "react-redux";
import { createTicket } from "../redux/slice/ticketSlice";
import { FaQuestionCircle } from "react-icons/fa";
import { getCustomerCareList } from "../redux/slice/customerCareSlice";
import toast from "react-hot-toast";
import imageCompression from 'browser-image-compression';

const RaiseQuestion = () => {
  const [showModal, setShowModal] = useState(false);
  const [customercareslist, setCustomercareslist] = useState([]);
  const [customercare, setCustomercare] = useState("");
  const [subject, setSubject] = useState("");
  const [description, setDescription] = useState("");
  const [attachment, setAttachment] = useState(null);
  const [tags, setTags] = useState("");
  const [loading, setLoading] = useState(false);
  const [validated, setValidated] = useState(false);
  const [image, setImage] = useState(null);
  const [imageError, setImageError] = useState("");
  const [preview, setPreview] = useState(null);
  const [isCheckingImage, setIsCheckingImage] = useState(false);
  const [imageSizeText, setImageSizeText] = useState(""); 

  const imageInputRef = useRef(null);

  const student = useSelector((state) => state?.student?.student?.student);

  const dispatch = useDispatch();

  const handleImageChange = (e) => {
    const file = e.target.files[0];

    if (!file) return;

    setIsCheckingImage(true);
    setImageError("");
    setImage(null);
    setPreview(null);

    const originalSizeKB = (file.size / 1024).toFixed(2);

    if (file.size <= 200 * 1024) {
      // Directly set the image if it is small enough
      setImage(file);
      const reader = new FileReader();
      reader.onload = () => setPreview(reader.result);
      reader.readAsDataURL(file);
      setIsCheckingImage(false);
    } else {
      const options = {
        maxSizeMB: 0.20,
        maxWidthOrHeight: 300,
        useWebWorker: true,
      };

      try {
        imageCompression(file, options).then((compressedFile) => {
          const compressedSizeKB = (compressedFile.size / 1024).toFixed(2);
          console.log("Compressed file size:", compressedFile.size); // Debugging

          if (compressedFile.size <= 200 * 1024) {
            // Convert Blob to File (required for form data)
            const fileName = "compressed_" + file.name;
            const compressedFileAsFile = new File([compressedFile], fileName, {
              type: compressedFile.type,
            });

            console.log("Setting compressed image:", compressedFileAsFile);

            // Set the image as File object
            setImage(compressedFileAsFile);

            const reader = new FileReader();
            reader.onload = () => setPreview(reader.result);
            reader.readAsDataURL(compressedFileAsFile);

            // Display the image sizes in the UI
            setImageSizeText(
              `Original Size: ${originalSizeKB} KB, Compressed Size: ${compressedSizeKB} KB`
            );
          } else {
            setImageError(`Image exceeds 200KB even after compression. Original: ${originalSizeKB} KB, Compressed: ${compressedSizeKB} KB.`);
          }
        }).catch((error) => {
          console.error("Image compression failed:", error);
          setImageError("An error occurred while compressing the image.");
        }).finally(() => {
          setIsCheckingImage(false);
        });
      } catch (error) {
        console.error("Error handling image change:", error);
        setImageError("An error occurred while processing the image.");
        setIsCheckingImage(false);
      }
    }
  };

  const getCustomerCares = async () => {
    try {
      const res = await dispatch(getCustomerCareList()).unwrap();
      if (res) {
        console.log(res);
        setCustomercareslist(res);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getCustomerCares();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const form = e.currentTarget;

    // Check form validity
    if (form.checkValidity() === false) {
      e.stopPropagation();
      setValidated(true);
      return;
    }

    const ticketData = {
      customer_id: Number(customercare),
      student_id: student?.id,
      ticket_status: "open",
      priority: "high",
      subject: subject,
      description: description,
      resolve_summary: "",
      tags: tags,
      ...(image && { attachments: image }),
    };

    try {
      setLoading(true);
      const res = await dispatch(createTicket({ data: ticketData }));
      if (res?.meta?.requestStatus === "fulfilled") {
        toast.success("Issue sent successfully!");
        setShowModal(false);
        resetForm();
      }
    } catch (err) {
      toast.error("Failed to send issue");
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setCustomercare("");
    setSubject("");
    setDescription("");
    setAttachment(null);
    setTags("");
    setPreview(null);
    setImage(null);
    setImageError("");
    setImageSizeText("");
    setValidated(false);
  };

  return (
    <>
      <Button
        variant="success"
        style={{
          position: "fixed",
          bottom: "20px",
          left: "20px",
          zIndex: "100",
        }}
        className="d-flex justify-content-between align-items-center gap-1"
        onClick={() => setShowModal(true)}
      >
        <FaQuestionCircle size={16} />
        <span className="fs-6">Raise a Query</span>
      </Button>

      <Modal show={showModal} onHide={() => setShowModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Raise a Query</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form noValidate validated={validated} onSubmit={handleSubmit}>
            {/* Contributor Field */}
            <Form.Group className="mb-3">
              <Form.Label>CustomerCare</Form.Label>
              <Form.Select
                value={customercare}
                onChange={(e) => setCustomercare(e.target.value)}
                required
                isInvalid={validated && !customercare}
              >
                <option value="" disabled>
                  Select a customercare
                </option>
                {customercareslist &&
                  customercareslist.map((customercare) => (
                    <option key={customercare.id} value={customercare.id}>
                      {customercare.user.first_name}{" "}
                      {customercare.user.last_name}
                    </option>
                  ))}
              </Form.Select>
              <Form.Control.Feedback type="invalid">
                Please select a customercare.
              </Form.Control.Feedback>
            </Form.Group>

            {/* Subject Field */}
            <Form.Group className="mb-3">
              <Form.Label>Subject</Form.Label>
              <Form.Control
                type="text"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                required
                isInvalid={validated && !subject}
              />
              <Form.Control.Feedback type="invalid">
                Please provide a subject.
              </Form.Control.Feedback>
            </Form.Group>

            {/* Description Field */}
            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                required
                isInvalid={validated && !description}
              />
              <Form.Control.Feedback type="invalid">
                Please provide a description.
              </Form.Control.Feedback>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Tags</Form.Label>
              <Form.Control
                type="text"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                required
                isInvalid={validated && !tags}
              />
              <Form.Control.Feedback type="invalid">
                Please provide some tags.
              </Form.Control.Feedback>
            </Form.Group>

            {/* Attachment Field */}
            <Form.Group controlId="issueImage" className="my-3">
              {imageSizeText && <p className="text-success">{imageSizeText}</p>}

              {imageError && <p className="text-danger mb-2">{imageError}</p>}
              <Form.Label>Attachment (Optional) (Under 200 KB)</Form.Label>
              <Form.Control
                ref={imageInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageChange}
              />
            </Form.Group>

            {preview && (
              <div className="mb-3">
                <img
                  src={preview}
                  alt="Preview"
                  style={{
                    width: "100%",
                    maxHeight: "200px",
                    objectFit: "cover",
                  }}
                />
              </div>
            )}

            {/* Submit Button */}
            <Button
              variant="success"
              type="submit"
              className="w-100"
              disabled={loading}
            >
              {loading ? "Submitting..." : "Submit"}
            </Button>
          </Form>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default RaiseQuestion;
