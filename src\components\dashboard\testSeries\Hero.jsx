import React, { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Container, Row, Col, Card, Button, Breadcrumb, Image } from "react-bootstrap";
import { FaCheckCircle, FaPlus, FaStar } from "react-icons/fa";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { useTheme } from "../../../context/ThemeContext"; // Add this import
// import data from "../../../dummyData/testSeries";

const Hero = ({ data, loading }) => {
  const { isDarkMode } = useTheme(); // Add this line

  const backgroundStyle = isDarkMode
    ? {
        backgroundImage: "linear-gradient(90deg, rgba(33, 37, 41, 0.95) 10%, rgba(33, 37, 41, 0.85) 90%)",
        color: "#fff"
      }
    : {
        backgroundImage: "linear-gradient(90deg, rgba(252, 252, 252, 0.87) 10%, rgba(209, 241, 221, 0.57) 90%)"
      };

  console.log(data);


  // console.log("dummydata: ", data);


  // useEffect(() => {
  //   // Simulate data loading for 2 seconds
  //   setTimeout(() => {
  //     setLoading(false);
  //   }, 3000);
  // }, []);

  // const { name, total, freeTestsTotal, totalUsers, categories } =
  //   data.testSeries;

  const calculateTotalFreeTests = (data) => {
    if (!data || !data?.Tiers) return 0;

    return data?.Tiers.reduce((tierTotal, tier) => {
      if (!tier.paper) return tierTotal;

      return tierTotal + tier.paper.reduce((paperTotal, paper) => {
        return paperTotal + (paper.freeTestsTotal || 0);
      }, 0);
    }, 0);
  };

  return (
    <Container
      className="mb-5 py-5"
      fluid
      style={backgroundStyle}
    >
      <Row className="justify-content-center">
        {/* First column - Test series details */}
        <Col lg={10} md={10}>
          <Row className="d-flex justify-content-around">
            <Col lg={6}>
              {/* Breadcrumbs */}
              {loading ? (
                <Skeleton
                  height={20}
                  width="50%"
                  baseColor="#e6ffe6"
                  highlightColor="#c4f7c4"
                />
              ) : (
                <Breadcrumb>
                  <Link to="/dashboard" style={{ color: isDarkMode ? '#fff' : 'inherit', textDecoration: 'none' }}>
                    <Breadcrumb.Item style={{ color: isDarkMode ? '#fff' : 'inherit' }}> Dashboard / </Breadcrumb.Item>
                  </Link>
                  <Breadcrumb.Item active style={{ color: isDarkMode ? '#fff' : 'inherit' }}> Test Series</Breadcrumb.Item>
                </Breadcrumb>
              )}

              {/* Test Series Name */}
              {loading ? (
                <Skeleton
                  height={30}
                  width="80%"
                  baseColor="#e6ffe6"
                  highlightColor="#c4f7c4"
                />
              ) : (
                <h4>{data?.subcourse_name}</h4>
              )}
              {loading ? (
                <Skeleton
                  height={15}
                  width="50%"
                  baseColor="#e6ffe6"
                  highlightColor="#c4f7c4"
                />
              ) : (
                <p className="text-muted small fw-semibold">
                  {/* Last Updated on Jan 20, 2025 */}
                  {data?.subcourse_description}
                </p>
              )}

              {/* Test Summary with vertical line */}
              <div className="d-flex align-items-center">
                {loading ? (
                  <>
                    <Skeleton
                      height={20}
                      width="40%"
                      baseColor="#e6ffe6"
                      highlightColor="#c4f7c4"
                    />
                    <div
                      className="mx-3"
                      style={{ height: "20px", borderLeft: "3px solid #ccc" }}
                    />
                    <Skeleton
                      height={20}
                      width="40%"
                      baseColor="#e6ffe6"
                      highlightColor="#c4f7c4"
                    />
                    <div
                      className="mx-3"
                      style={{ height: "20px", borderLeft: "3px solid #ccc" }}
                    />
                    <Skeleton
                      height={20}
                      width="40%"
                      baseColor="#e6ffe6"
                      highlightColor="#c4f7c4"
                    />
                  </>
                ) : (
                  <>
                    <span className="fw-semibold">
                      {/* {total}  */}
                      Total Tests
                    </span>
                    <div
                      className="mx-3"
                      style={{ height: "20px", borderLeft: "3px solid #ccc" }}
                    />
                    <span className="text-white badge bg-success small">
                      {calculateTotalFreeTests(data)} FREE TESTS
                    </span>
                    <div
                      className="mx-3"
                      style={{ height: "20px", borderLeft: "3px solid #ccc" }}
                    />
                    <span>{data?.totalUsers} users</span>
                  </>
                )}
              </div>
              <hr />

              {/* Test Series Categories (Prelims & Mains) */}
              <Row className="mt-4">
                {loading ? (
                  <>
                    <Col xs={6}>
                      <Skeleton
                        height={15}
                        width="80%"
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                      />
                      <Skeleton
                        height={15}
                        width="80%"
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                      />
                      <Skeleton
                        height={15}
                        width="80%"
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                      />
                    </Col>
                    <Col xs={6}>
                      <Skeleton
                        height={15}
                        width="80%"
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                      />
                      <Skeleton
                        height={15}
                        width="80%"
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                      />
                      <Skeleton
                        height={15}
                        width="80%"
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                      />
                    </Col>
                  </>
                ) : (
                  <>
                    {data?.Tiers.map((category) => (
                      <Col xs={6} key={category?.key}>
                        <ul>
                          {
                            <li>
                              {category?.paper?.length} {category?.Tier_name}
                            </li>
                          }
                        </ul>
                      </Col>
                    ))}
                  </>
                )}
              </Row>

              {/* Add Test Series Button */}
              {/* {loading ? (
                <Skeleton
                  height={40}
                  width="100%"
                  baseColor="#e6ffe6"
                  highlightColor="#c4f7c4"
                />
              ) : (
                <Button variant="success" className="mt-3 w-100 d-flex align-items-center gap-2 justify-content-center">
                  <FaPlus/> Add this Test Series
                </Button>
              )} */}
            </Col>

            {/* Second column - Pro Subscription Card */}
            <Col lg={4} className="mt-3 mt-lg-0">
              <Card className="">
                <div className="position-absolute top-0 start-0">
                  {loading ? (
                    <>
                      <Skeleton
                        height={10}
                        width={30}
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                      />
                    </>
                  ) : (
                    <>
                      <span className="badge bg-warning text-dark">NEW</span>
                    </>
                  )}
                </div>
                <div className="p-3">
                  <div className="d-flex justify-content-start align-items-center gap-2">
                    {loading ? (
                      <>
                        <Skeleton
                          height={60}
                          width={180}
                          baseColor="#e6ffe6"
                          highlightColor="#c4f7c4"
                        />
                        <Skeleton
                          height={20}
                          width={50}
                          baseColor="#e6ffe6"
                          highlightColor="#c4f7c4"
                        />
                      </>
                    ) : (
                      <>
                        <Image
                          src="/shash_logo.png"
                          alt="sahshtrarth Logo"
                          className=""
                          width={180}
                        />
                        <span className="badge bg-success text-light">
                          PASS PRO
                        </span>
                      </>
                    )}
                  </div>
                  <h5 className="mt-3">
                    {loading ? (
                      <Skeleton
                        height={25}
                        width="80%"
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                      />
                    ) : (
                      "Shashtrarth is free for eairly bird students"
                    )}
                  </h5>
                  <ul className="list-unstyled">
                    {loading ? (
                      <>
                        <Skeleton
                          height={15}
                          width="80%"
                          baseColor="#e6ffe6"
                          highlightColor="#c4f7c4"
                        />
                        <Skeleton
                          height={15}
                          width="80%"
                          baseColor="#e6ffe6"
                          highlightColor="#c4f7c4"
                        />
                        <Skeleton
                          height={15}
                          width="80%"
                          baseColor="#e6ffe6"
                          highlightColor="#c4f7c4"
                        />
                        <Skeleton
                          height={15}
                          width="80%"
                          baseColor="#e6ffe6"
                          highlightColor="#c4f7c4"
                        />
                        <Skeleton
                          height={15}
                          width="80%"
                          baseColor="#e6ffe6"
                          highlightColor="#c4f7c4"
                        />
                      </>
                    ) : (
                      <>
                        <li>
                          <FaCheckCircle className="me-2 text-success" />
                          All Test Series
                        </li>
                        <li>
                          <FaCheckCircle className="me-2 text-success" />
                          Can Access Paid Test Series too
                        </li>
                        <li>
                          <FaCheckCircle className="me-2 text-success" />
                          Unlimited Practice
                        </li>
                        <li>
                          <FaCheckCircle className="me-2 text-success" />
                          Unlimited Test Re-Attempts
                        </li>
                        <li>
                          <FaCheckCircle className="me-2 text-success" />
                          30 Days
                        </li>
                      </>
                    )}
                  </ul>
                  {loading ? (
                    <Skeleton
                      height={40}
                      width="100%"
                      baseColor="#e6ffe6"
                      highlightColor="#c4f7c4"
                    />
                  ) : (
                    <Button variant="success" className="mt-3 w-100">
                      Get Pass Pro
                    </Button>
                  )}
                </div>
              </Card>
            </Col>
          </Row>
        </Col>
      </Row>
    </Container>
  );
};

export default Hero;

