import React, { useEffect, useState } from "react";
import {
  Container,
  Row,
  Col,
} from "react-bootstrap";

import { useDispatch, useSelector } from "react-redux";

import { getStudentProfile } from "../../redux/slice/studentSlice";
import Skeleton from "react-loading-skeleton";
import TicketCard from "../components/TicketCard";
import TestSeriesCard from "../../commonCompoenents/TestSeriesCard";
import Sidebar from "../../commonCompoenents/Sidebar";
import CustomNavbar from "../../commonCompoenents/Nav";
import { useNavigate, useLocation } from "react-router-dom";

const AttemptedTestSeries = () => {
  const studentData = useSelector((state) => state?.student);
  const navigate = useNavigate();
  const [show, setShow] = useState(false);
  const [formData, setFormData] = useState();
  const [loading, setLoading] = useState(false);
  // const [student, setStudent] = useState();

  const dispatch = useDispatch();

  const { hash } = useLocation(); // Get the hash from URL

  const getStudentData = async () => {
    setLoading(true);
    try {
      const res = await dispatch(
        getStudentProfile({ id: studentData?.student?.student?.id })
      );
      if (res?.meta?.requestStatus === "fulfilled") {
        setFormData(res?.payload);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    const scrollToHash = async () => {
      await getStudentData(); // Wait for getStudentData to fulfill
      if (hash) {
        const element = document.querySelector(hash);
        if (element) {
          element.scrollIntoView({ behavior: "smooth" });
        }
      }
    };
    scrollToHash();
  }, [hash]); // Run effect when hash changes
  return (
    <Container fluid>
      <Row>
        <Col md={3} lg={2} className="p-0">
          <Sidebar />
        </Col>
        <Col md={9} lg={10}>
          <CustomNavbar />
          <div
            style={{
              maxHeight: "90vh",
              overflowY: "scroll",
              scrollbarWidth: "none" /* Firefox */,
              msOverflowStyle: "none",
            }}
            className="overflow-y-auto"
          >
            <Row className="justify-content-center w-100">


              <Col lg={10} className="mt-5">
                <h2 className="text-start mb-4 text-success" id="attempted-tests">My Attempted Tests</h2>

                <div className="d-flex flex-wrap justify-content-start">
                  {formData?.card_data?.length > 0 ? (
                    formData.card_data.map((test, index) => (
                      <TestSeriesCard
                        key={index}
                        test={test}
                        addTestLoading={false} // Example: set a state for loading if needed
                        handleAddTestSeries={(slug) => console.log("Adding:", slug)}
                        navigate={navigate}
                      />
                    ))
                  ) : (
                    <p>No attempted tests found.</p>
                  )}
                </div>
              </Col>

            </Row>
          </div>
        </Col>
      </Row>

    </Container>
  );
};

export default AttemptedTestSeries;
