import React from 'react';
import { Container, Row, Col, Card } from 'react-bootstrap';
import { MdOutlineFileDownload } from 'react-icons/md';
import { FaCalculator, FaBrain, FaGlobeAmericas, FaLaptopCode, FaNewspaper } from 'react-icons/fa';
import { GiElectric, GiAtom } from 'react-icons/gi';

const StudyNotesSection = () => {
  const notesData = [
    {
      subject: "Quantitative Aptitude",
      count: "18+",
      type: "PDF Notes",
      icon: <FaCalculator size={30} />,
      iconColor: "#007bff" // Blue color for icon background
    },
    {
      subject: "General Intelligence & Reasoning",
      count: "53+",
      type: "PDF Notes",
      icon: <FaBrain size={30} />,
      iconColor: "#28a745" // Green color for icon background
    },
    {
      subject: "General Awareness",
      count: "187+",
      type: "PDF Notes",
      icon: <FaGlobeAmericas size={30} />,
      iconColor: "#ffc107" // Yellow color for icon background
    },
    {
      subject: "Electrical Engineering",
      count: "45+",
      type: "PDF Notes",
      icon: <GiElectric size={30} />,
      iconColor: "#dc3545" // Red color for icon background
    },
    {
      subject: "General Science",
      count: "66+",
      type: "PDF Notes",
      icon: <GiAtom size={30} />,
      iconColor: "#6f42c1" // Purple color for icon background
    },
    {
      subject: "Computer Awareness",
      count: "15+",
      type: "PDF Notes",
      icon: <FaLaptopCode size={30} />,
      iconColor: "#17a2b8" // Cyan color for icon background
    },
    {
      subject: "Current Affairs",
      count: "56+",
      type: "PDF Notes",
      icon: <FaNewspaper size={30} />,
      iconColor: "#fd7e14" // Orange color for icon background
    }
  ];

  return (
    <Container className="mt-5"
    style={{  background: "linear-gradient(to bottom, rgb(248, 248, 248), rgb(214, 241, 214), rgb(248, 248, 248))",  paddingBlock: "20px"}}>
      <Row className="justify-content-center">
        <Col md={10}>
          <div className="d-flex justify-content-between align-items-center mb-3">
            <h4><span className='text-success'>440+</span> Study Notes</h4>
            <button className="btn btn-success px-3 py-2 bg-opacity-25 text-white"> 
              <MdOutlineFileDownload size={20} /> Download Curriculum
            </button>
          </div>
          <p className="text-muted mb-3">All subject notes covered</p>

          <div className="d-flex flex-wrap">
            {notesData.map((note, index) => (
              <Card key={index} style={{ width: '320px', margin: '10px' }} className="shadow-sm">
                <div className="p-3 bg-light rounded-top d-flex align-items-center">
                  {/* Icon with circular background */}
                  <div 
                    className="d-flex justify-content-center align-items-center"
                    style={{
                      width: '40px',
                      height: '40px',
                      backgroundColor: note.iconColor,
                      borderRadius: '50%',
                      color: 'white',
                      marginRight: '10px'
                    }}
                  >
                    {note.icon}
                  </div>
                  {/* Right Side: Subject, Count, and Type */}
                  <div>
                    <div>
                      <span className="font-weight-bold">{note.subject}</span>
                    </div>
                    <div className="text-muted">
                      <span>{note.count} {note.type}</span>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </Col>
      </Row>
    </Container>
  );
};

export default StudyNotesSection;
