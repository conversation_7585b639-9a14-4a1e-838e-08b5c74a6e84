import React from "react";
import { Container, Row, Col, But<PERSON>, Image } from "react-bootstrap";
import { useNavigate } from "react-router-dom";

const ChooseAuth = () => {
  const navigate = useNavigate();

  const handleNavigation = (path) => {
    navigate(path);
  };

  return (
    <>
      <Container
        className="d-flex flex-column justify-content-center align-items-center"
        style={{ minHeight: "100vh", backgroundColor: "white" }}
      >
        <Row className="justify-content-center">
          <Image
            src="/signup.svg"
            alt="register image"
            style={{ width: "400px", height: "300px" }}
          />
        </Row>
        <h1
          className="text-center"
          style={{ fontSize: "2.5rem", fontWeight: "800", color: "#3F3D56" }}
        >
          Shasthrarth
        </h1>
        <p
          className="text-center text-muted mt-3"
          style={{ fontSize: "16px", opacity: 0.6 }}
        >
          Where You Unlock Your Study Potential With Our Wide Range of
          Courses & Test-sereis!
        </p>
        <Row className="mt-4">
          <Col className="d-flex justify-content-center">
            <Button
              onClick={() => handleNavigation("/login")}
              variant="success"
              style={{
                color: "#f3f4f6",
                width: "200px",
                margin: "10px",
              }}
            >
              Login
            </Button>
          </Col>
          <Col className="d-flex justify-content-center">
            <Button
              onClick={() => handleNavigation("/signup")}
              variant="outline-success"
              style={{
                backgroundColor: "#e4e4e7",
                color: "#3F3D56",
                width: "200px",
                margin: "10px",
              }}
            >
              Signup
            </Button>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default ChooseAuth;
