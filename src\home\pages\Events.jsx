import React, { useState, useEffect } from "react";
import {
  Contain<PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  Badge,
  Button,
  Modal,
  Image,
  Pagination,
  Form,
  Alert,
  Spinner,
} from "react-bootstrap";
import { FaCalendarAlt, FaClock, FaMapMarkerAlt, FaUsers, Fa<PERSON>ye, Fa<PERSON>ilter, FaUserPlus } from "react-icons/fa";
import { useDispatch, useSelector } from "react-redux";
import { subscribeEvents, clearErrors, clearSubscriptionStatus } from "../../redux/slice/eventSlice";
import NavBar from "../../commonCompoenents/NavBar";
import Footer from "../components/Footer";
import SubscribedEvents from "../../commonCompoenents/SubscribedEvents";
import { toast } from "react-hot-toast";

const Events = () => {
  const dispatch = useDispatch();
  const { subscribeLoading, subscribeError, subscriptionStatus } = useSelector((state) => state.events);

  const [events, setEvents] = useState([]);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [showSubscribeForm, setShowSubscribeForm] = useState(false);
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [eventsPerPage] = useState(6);

  // Subscription form state
  const [subscriptionData, setSubscriptionData] = useState({
    username: '',
    password: ''
  }); // Number of events per page

  // Dummy event data
  const dummyEvents = [
    {
      id: 1,
      title: "Annual Science Fair 2024",
      description: "Join us for an exciting showcase of innovative science projects and experiments from students across all departments.",
      date: "2024-03-15",
      time: "10:00 AM",
      location: "Main Auditorium",
      category: "academic",
      attendees: 250,
      image: "https://via.placeholder.com/400x200/007bff/ffffff?text=Science+Fair",
      status: "upcoming",
      organizer: "Science Department",
      registrationRequired: true,
      maxAttendees: 300,
      tags: ["science", "innovation", "students"]
    },
    {
      id: 2,
      title: "Cultural Festival - Rangmanch",
      description: "A vibrant celebration of arts, music, dance, and cultural diversity. Experience performances from various cultural groups.",
      date: "2024-03-22",
      time: "6:00 PM",
      location: "Open Ground",
      category: "cultural",
      attendees: 450,
      image: "https://via.placeholder.com/400x200/28a745/ffffff?text=Cultural+Festival",
      status: "upcoming",
      organizer: "Cultural Committee",
      registrationRequired: false,
      maxAttendees: 500,
      tags: ["culture", "music", "dance", "arts"]
    },
    {
      id: 3,
      title: "Tech Symposium 2024",
      description: "Explore the latest trends in technology with expert speakers, workshops, and networking opportunities.",
      date: "2024-02-28",
      time: "9:00 AM",
      location: "Conference Hall",
      category: "technology",
      attendees: 180,
      image: "https://via.placeholder.com/400x200/dc3545/ffffff?text=Tech+Symposium",
      status: "completed",
      organizer: "IT Department",
      registrationRequired: true,
      maxAttendees: 200,
      tags: ["technology", "innovation", "networking"]
    },
    {
      id: 4,
      title: "Sports Championship",
      description: "Annual inter-department sports competition featuring cricket, football, basketball, and athletics.",
      date: "2024-04-05",
      time: "8:00 AM",
      location: "Sports Complex",
      category: "sports",
      attendees: 320,
      image: "https://via.placeholder.com/400x200/ffc107/000000?text=Sports+Championship",
      status: "upcoming",
      organizer: "Sports Committee",
      registrationRequired: true,
      maxAttendees: 400,
      tags: ["sports", "competition", "athletics"]
    },
    {
      id: 5,
      title: "Career Guidance Workshop",
      description: "Professional development workshop with industry experts sharing insights on career opportunities and skill development.",
      date: "2024-03-10",
      time: "2:00 PM",
      location: "Seminar Hall",
      category: "professional",
      attendees: 95,
      image: "https://via.placeholder.com/400x200/6f42c1/ffffff?text=Career+Workshop",
      status: "upcoming",
      organizer: "Placement Cell",
      registrationRequired: true,
      maxAttendees: 100,
      tags: ["career", "professional", "skills"]
    },
    {
      id: 6,
      title: "Alumni Meet 2024",
      description: "Reconnect with fellow alumni, share experiences, and network with professionals from various industries.",
      date: "2024-02-15",
      time: "7:00 PM",
      location: "Grand Hall",
      category: "networking",
      attendees: 150,
      image: "https://via.placeholder.com/400x200/17a2b8/ffffff?text=Alumni+Meet",
      status: "completed",
      organizer: "Alumni Association",
      registrationRequired: true,
      maxAttendees: 200,
      tags: ["alumni", "networking", "reunion"]
    },
    {
      id: 7,
      title: "Mathematics Olympiad 2024",
      description: "Test your mathematical skills in this challenging competition featuring problems from various mathematical domains.",
      date: "2024-04-12",
      time: "9:00 AM",
      location: "Mathematics Department",
      category: "academic",
      attendees: 120,
      image: "https://via.placeholder.com/400x200/007bff/ffffff?text=Math+Olympiad",
      status: "upcoming",
      organizer: "Mathematics Department",
      registrationRequired: true,
      maxAttendees: 150,
      tags: ["mathematics", "competition", "academic"]
    },
    {
      id: 9,
      title: "Robotics Workshop",
      description: "Hands-on workshop on building and programming robots using Arduino and Raspberry Pi platforms.",
      date: "2024-01-20",
      time: "2:00 PM",
      location: "Engineering Lab",
      category: "technology",
      attendees: 80,
      image: "https://via.placeholder.com/400x200/dc3545/ffffff?text=Robotics+Workshop",
      status: "completed",
      organizer: "Engineering Department",
      registrationRequired: true,
      maxAttendees: 100,
      tags: ["robotics", "programming", "engineering"]
    },
    {
      id: 10,
      title: "Basketball Tournament",
      description: "Inter-college basketball championship with teams from various institutions competing for the trophy.",
      date: "2024-04-20",
      time: "4:00 PM",
      location: "Basketball Court",
      category: "sports",
      attendees: 500,
      image: "https://via.placeholder.com/400x200/ffc107/000000?text=Basketball+Tournament",
      status: "upcoming",
      organizer: "Sports Committee",
      registrationRequired: true,
      maxAttendees: 600,
      tags: ["basketball", "tournament", "sports"]
    },
    {
      id: 11,
      title: "Entrepreneurship Summit",
      description: "Learn from successful entrepreneurs and startup founders about building and scaling businesses.",
      date: "2024-03-05",
      time: "10:00 AM",
      location: "Business Center",
      category: "professional",
      attendees: 180,
      image: "https://via.placeholder.com/400x200/6f42c1/ffffff?text=Entrepreneurship+Summit",
      status: "upcoming",
      organizer: "Business Department",
      registrationRequired: true,
      maxAttendees: 200,
      tags: ["entrepreneurship", "business", "startup"]
    },
    {
      id: 12,
      title: "Photography Contest",
      description: "Capture the beauty around you in this photography competition with multiple categories and prizes.",
      date: "2024-01-15",
      time: "12:00 PM",
      location: "Campus Wide",
      category: "cultural",
      attendees: 150,
      image: "https://via.placeholder.com/400x200/28a745/ffffff?text=Photography+Contest",
      status: "completed",
      organizer: "Photography Club",
      registrationRequired: true,
      maxAttendees: 200,
      tags: ["photography", "contest", "creativity"]
    }
  ];

  useEffect(() => {
    // Simulate API call
    setEvents(dummyEvents);
  }, []);

  // Handle subscription success/error
  useEffect(() => {
    if (subscriptionStatus) {
      toast.success('Successfully subscribed to events!');
      setShowSubscribeForm(false);
      setSubscriptionData({ username: '', password: '' });
      dispatch(clearSubscriptionStatus());
    }
  }, [subscriptionStatus, dispatch]);

  useEffect(() => {
    if (subscribeError) {
      toast.error(subscribeError.message || 'Failed to subscribe to events');
      dispatch(clearErrors());
    }
  }, [subscribeError, dispatch]);

  const handleShowModal = (event) => {
    setSelectedEvent(event);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedEvent(null);
    setShowSubscribeForm(false);
    setSubscriptionData({ username: '', password: '' });
  };

  const handleRegisterClick = () => {
    setShowSubscribeForm(true);
  };

  const handleSubscriptionInputChange = (e) => {
    const { name, value } = e.target;
    setSubscriptionData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubscriptionSubmit = (e) => {
    e.preventDefault();
    if (subscriptionData.username && subscriptionData.password) {
      dispatch(subscribeEvents(subscriptionData));
    } else {
      toast.error('Please enter both username and password');
    }
  };

  const getStatusBadge = (status) => {
    const variants = {
      upcoming: "primary",
      completed: "secondary",
      ongoing: "success"
    };
    return <Badge bg={variants[status] || "secondary"}>{status.toUpperCase()}</Badge>;
  };

  const getCategoryColor = (category) => {
    const colors = {
      academic: "primary",
      cultural: "success",
      technology: "danger",
      sports: "warning",
      professional: "info",
      networking: "dark"
    };
    return colors[category] || "secondary";
  };

  // Apply filters
  const filteredEvents = events.filter(event => {
    const categoryMatch = categoryFilter === "all" || event.category === categoryFilter;
    const statusMatch = statusFilter === "all" || event.status === statusFilter;
    return categoryMatch && statusMatch;
  });

  // Pagination logic
  const indexOfLastEvent = currentPage * eventsPerPage;
  const indexOfFirstEvent = indexOfLastEvent - eventsPerPage;
  const currentEvents = filteredEvents.slice(indexOfFirstEvent, indexOfLastEvent);
  const totalPages = Math.ceil(filteredEvents.length / eventsPerPage);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [categoryFilter, statusFilter]);

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <>
      {/* Navigation Bar */}
      <NavBar />

      {/* Main Content */}
      <Container className="py-4" style={{ marginTop: "80px", minHeight: "100vh" }}>
      <Row className="mb-4">
        <Col>
          <h2 className="text-success mb-3">
            <FaCalendarAlt className="me-2 mb-2" />
            Events & Activities
          </h2>
          <p className="text-muted">
            Stay updated with all the exciting events, workshops, and activities happening in our institution.
          </p>
        </Col>
      </Row>

      {/* My Subscribed Events Section */}
      <Row className="mb-5">
        <Col>
          <h4 className="text-success mb-3">My Subscribed Events</h4>
          <SubscribedEvents showTitle={false} maxEvents={3} />
        </Col>
      </Row>

      {/* Filter Buttons */}
      <Row className="mb-4">
        <Col>
          <div className="mb-3">
            <h6 className="text-muted mb-2">
              <FaFilter className="me-2" />
              Filter by Status
            </h6>
            <div className="d-flex flex-wrap gap-2 mb-3">
              <Button
                variant={statusFilter === "all" ? "success" : "outline-success"}
                onClick={() => setStatusFilter("all")}
                size="sm"
              >
                All Status
              </Button>
              <Button
                variant={statusFilter === "upcoming" ? "success" : "outline-success"}
                onClick={() => setStatusFilter("upcoming")}
                size="sm"
              >
                Upcoming
              </Button>
              <Button
                variant={statusFilter === "completed" ? "success" : "outline-success"}
                onClick={() => setStatusFilter("completed")}
                size="sm"
              >
                Completed
              </Button>
            </div>
          </div>

          <div>
            <h6 className="text-muted mb-2">Filter by Category</h6>
            <div className="d-flex flex-wrap gap-2">
              <Button
                variant={categoryFilter === "all" ? "success" : "outline-success"}
                onClick={() => setCategoryFilter("all")}
                size="sm"
              >
                All Categories
              </Button>
              <Button
                variant={categoryFilter === "academic" ? "success" : "outline-success"}
                onClick={() => setCategoryFilter("academic")}
                size="sm"
              >
                Academic
              </Button>
              <Button
                variant={categoryFilter === "cultural" ? "success" : "outline-success"}
                onClick={() => setCategoryFilter("cultural")}
                size="sm"
              >
                Cultural
              </Button>
              <Button
                variant={categoryFilter === "technology" ? "success" : "outline-success"}
                onClick={() => setCategoryFilter("technology")}
                size="sm"
              >
                Technology
              </Button>
              <Button
                variant={categoryFilter === "sports" ? "success" : "outline-success"}
                onClick={() => setCategoryFilter("sports")}
                size="sm"
              >
                Sports
              </Button>
              <Button
                variant={categoryFilter === "professional" ? "success" : "outline-success"}
                onClick={() => setCategoryFilter("professional")}
                size="sm"
              >
                Professional
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {/* Events Grid */}
      <Row>
        {currentEvents.map((event) => (
          <Col lg={4} md={6} sm={12} className="mb-4" key={event.id}>
            <Card className="h-100 shadow-sm border-0">
              <Card.Img
                variant="top"
                src={event.image}
                style={{ height: "200px", objectFit: "cover" }}
              />
              <Card.Body className="d-flex flex-column">
                <div className="d-flex justify-content-between align-items-start mb-2">
                  <Badge bg={getCategoryColor(event.category)} className="mb-2">
                    {event.category.toUpperCase()}
                  </Badge>
                  {getStatusBadge(event.status)}
                </div>

                <Card.Title className="h5 mb-2">{event.title}</Card.Title>
                <Card.Text className="text-muted small flex-grow-1">
                  {event.description.length > 100
                    ? `${event.description.substring(0, 100)}...`
                    : event.description}
                </Card.Text>

                <div className="mt-auto">
                  <div className="d-flex align-items-center mb-2 text-muted small">
                    <FaCalendarAlt className="me-2" />
                    <span>{formatDate(event.date)}</span>
                  </div>
                  <div className="d-flex align-items-center mb-2 text-muted small">
                    <FaClock className="me-2" />
                    <span>{event.time}</span>
                  </div>
                  <div className="d-flex align-items-center mb-2 text-muted small">
                    <FaMapMarkerAlt className="me-2" />
                    <span>{event.location}</span>
                  </div>
                  <div className="d-flex align-items-center mb-3 text-muted small">
                    <FaUsers className="me-2" />
                    <span>{event.attendees}/{event.maxAttendees} attendees</span>
                  </div>

                  <Button
                    variant="outline-success"
                    size="sm"
                    className="w-100"
                    onClick={() => handleShowModal(event)}
                  >
                    <FaEye className="me-2" />
                    View Details
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      {filteredEvents.length === 0 && (
        <Row>
          <Col className="text-center py-5">
            <h4 className="text-muted">No events found</h4>
            <p className="text-muted">Try adjusting your filters or check back later for new events.</p>
          </Col>
        </Row>
      )}

      {/* Pagination */}
      {filteredEvents.length > eventsPerPage && (
        <Row className="mt-4">
          <Col className="d-flex justify-content-center">
            <Pagination>
              <Pagination.First
                onClick={() => handlePageChange(1)}
                disabled={currentPage === 1}
              />
              <Pagination.Prev
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              />

              {/* Page numbers */}
              {[...Array(totalPages)].map((_, index) => {
                const pageNumber = index + 1;
                const isCurrentPage = pageNumber === currentPage;

                // Show first page, last page, current page, and pages around current page
                if (
                  pageNumber === 1 ||
                  pageNumber === totalPages ||
                  (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
                ) {
                  return (
                    <Pagination.Item
                      key={pageNumber}
                      active={isCurrentPage}
                      onClick={() => handlePageChange(pageNumber)}
                    >
                      {pageNumber}
                    </Pagination.Item>
                  );
                } else if (
                  pageNumber === currentPage - 2 ||
                  pageNumber === currentPage + 2
                ) {
                  return <Pagination.Ellipsis key={pageNumber} />;
                }
                return null;
              })}

              <Pagination.Next
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              />
              <Pagination.Last
                onClick={() => handlePageChange(totalPages)}
                disabled={currentPage === totalPages}
              />
            </Pagination>
          </Col>
        </Row>
      )}

      {/* Results Summary */}
      {filteredEvents.length > 0 && (
        <Row className="mt-3">
          <Col className="text-center">
            <small className="text-muted">
              Showing {indexOfFirstEvent + 1} to {Math.min(indexOfLastEvent, filteredEvents.length)} of {filteredEvents.length} events
            </small>
          </Col>
        </Row>
      )}

      {/* Event Details Modal */}
      <Modal show={showModal} onHide={handleCloseModal} size="lg" centered>
        <Modal.Header closeButton>
          <Modal.Title>{selectedEvent?.title}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedEvent && (
            <>
              <Image
                src={selectedEvent.image}
                fluid
                className="mb-3 rounded"
                style={{ width: "100%", height: "250px", objectFit: "cover" }}
              />

              <div className="d-flex justify-content-between align-items-center mb-3">
                <Badge bg={getCategoryColor(selectedEvent.category)} className="fs-6">
                  {selectedEvent.category.toUpperCase()}
                </Badge>
                {getStatusBadge(selectedEvent.status)}
              </div>

              <p className="mb-3">{selectedEvent.description}</p>

              <Row className="mb-3">
                <Col md={6}>
                  <div className="d-flex align-items-center mb-2">
                    <FaCalendarAlt className="me-2 text-success" />
                    <strong>Date:</strong>
                    <span className="ms-2">{formatDate(selectedEvent.date)}</span>
                  </div>
                  <div className="d-flex align-items-center mb-2">
                    <FaClock className="me-2 text-success" />
                    <strong>Time:</strong>
                    <span className="ms-2">{selectedEvent.time}</span>
                  </div>
                </Col>
                <Col md={6}>
                  <div className="d-flex align-items-center mb-2">
                    <FaMapMarkerAlt className="me-2 text-success" />
                    <strong>Location:</strong>
                    <span className="ms-2">{selectedEvent.location}</span>
                  </div>
                  <div className="d-flex align-items-center mb-2">
                    <FaUsers className="me-2 text-success" />
                    <strong>Attendees:</strong>
                    <span className="ms-2">{selectedEvent.attendees}/{selectedEvent.maxAttendees}</span>
                  </div>
                </Col>
              </Row>

              <div className="mb-3">
                <strong>Organizer:</strong>
                <span className="ms-2">{selectedEvent.organizer}</span>
              </div>

              <div className="mb-3">
                <strong>Registration Required:</strong>
                <span className="ms-2">
                  {selectedEvent.registrationRequired ? "Yes" : "No"}
                </span>
              </div>

              <div className="mb-3">
                <strong>Tags:</strong>
                <div className="mt-2">
                  {selectedEvent.tags?.map((tag, index) => (
                    <Badge key={index} bg="light" text="dark" className="me-2 mb-1">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Subscription Form */}
              {showSubscribeForm && (
                <div className="mt-4 p-3 border rounded bg-light">
                  <h6 className="mb-3">
                    <FaUserPlus className="me-2" />
                    Subscribe to Events
                  </h6>

                  <Alert variant="info" className="small">
                    <strong>Note:</strong> If you haven't signed up with the Shashtrarth app, please{" "}
                    <a
                      href="https://shashtrarth.com/signup"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="alert-link"
                    >
                      register first
                    </a>{" "}
                    to get your username and password.
                  </Alert>

                  <Form onSubmit={handleSubscriptionSubmit}>
                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>Username</Form.Label>
                          <Form.Control
                            type="text"
                            name="username"
                            value={subscriptionData.username}
                            onChange={handleSubscriptionInputChange}
                            placeholder="Enter your username"
                            required
                          />
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>Password</Form.Label>
                          <Form.Control
                            type="password"
                            name="password"
                            value={subscriptionData.password}
                            onChange={handleSubscriptionInputChange}
                            placeholder="Enter your password"
                            required
                          />
                        </Form.Group>
                      </Col>
                    </Row>

                    <div className="d-flex gap-2">
                      <Button
                        type="submit"
                        variant="success"
                        disabled={subscribeLoading}
                        className="flex-fill"
                      >
                        {subscribeLoading ? (
                          <>
                            <Spinner size="sm" className="me-2" />
                            Subscribing...
                          </>
                        ) : (
                          'Subscribe to Events'
                        )}
                      </Button>
                      <Button
                        type="button"
                        variant="outline-secondary"
                        onClick={() => setShowSubscribeForm(false)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </Form>
                </div>
              )}
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseModal}>
            Close
          </Button>
          {selectedEvent?.status === "upcoming" && !showSubscribeForm && (
            <Button variant="success" onClick={handleRegisterClick}>
              <FaUserPlus className="me-2" />
              Subscribe to Event
            </Button>
          )}
        </Modal.Footer>
      </Modal>
      </Container>

      {/* Footer */}
      <Footer />
    </>
  );
};

export default Events;
