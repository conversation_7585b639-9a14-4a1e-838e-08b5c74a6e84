import React from "react";
import { Button, Container } from "react-bootstrap";
import { FaDownload } from "react-icons/fa";

const pdfUrl = "/demo.pdf"; 

const Result = ({examinfo}) => {
  return (
    <Container className="mt-4">
    <div className="d-flex justify-content-between align-items-center mb-3">
      <h3 className="">Result</h3>
      <Button
        variant="success"
        className=""
        href={pdfUrl}
        download="Result.pdf"
      >
        <FaDownload className="me-2" />
        Download PDF
      </Button>
      </div>

      {/* PDF Viewer */}
      <div className="scrollbar overflow-y-auto">
      <iframe
        src={`${pdfUrl}#toolbar=0`}
        width="100%"
        height="600px"
        className="rounded-3"
        // style={{ border: "1px solid #ccc" }}
        title="Result PDF"
      ></iframe>
      </div>

      {/* Download Button */}
    </Container>
  );
};

export default Result;
