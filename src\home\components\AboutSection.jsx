import React from 'react';
import { Container, <PERSON>, Col, Card } from 'react-bootstrap';

const AboutSection = () => {
  return (
    <section className="py-5 mt-5 bg-light"
      style={{
        // marginTop: "150px",
        // background: "linear-gradient(130deg, white,rgb(152, 206, 199)), url('https://www.transparenttextures.com/patterns/cubes.png')",
        // // backgroundSize: "cover",
        // backgroundBlendMode: "overlay",
        // opacity: 0.9, 
      }}>
      <Container>
        <Row className="justify-content-center">
          <Col md={12}>
            <Card className="border-0 bg-transparent">
              <Card.Body>
                <p className="fw-semibold">Student-Centric Approach: Your Success, Our Mission</p>
                <p>
                  In the era of technology, E-learning has made it possible for students to learn from the comfort of their homes. With the same motive, Shastrarth is providing a comfortable learning environment for the aspirants preparing for government exams at an affordable cost.
                </p>
                <p>
                  Started from scratch with an offline coaching center in Delhi in 2010, Shastrarth is now one of the top online coaching institutes in 2022. We have come a long way and continue to innovate to make Shastrarth an all-rounder Ed-tech company, helping every student build their career.
                </p>
                <p>
                  At Shastrarth, we understand that preparing for competitive exams is not just about studying hard—it's about studying smart. That’s why we provide a structured, data-driven, and result-oriented approach to help students crack their dream exams.
                </p>

                <p className="fw-semibold text-success">What does Shastrarth provide?</p>
                <p>Shastrarth offers E-learning programs in various sectors. Here's what we specialize in:</p>
                <ul className="mt-3 list-unstyled">
                  <li>✔ Prepare for any government exam: Banking, Railways, SSC, Teaching, Defence, Engineering, UPSC, State PCS, and more.</li>
                  <li>✔ Access high-quality study materials: Mock Tests, Live Classes, Video Courses, Ebooks, and Books—all at an affordable price.</li>
                  <li>✔ Learn from experienced faculty for entrance exams: NEET (UG & PG), JEE Mains & Advanced, GATE, UGC NET, CUET, Law Entrance, IPM, and more.</li>
                  <li>✔ Get school-level study material: Class 9th-12th courses, online classes, and interactive learning methods.</li>
                  <li>✔ Study in multiple languages: Materials available in English, Hindi, and 8 vernacular languages (Tamil, Telugu, Bengali, Marathi, Odia, Gujarati, Assamese, Malayalam).</li>
                </ul>

                <p className="fw-semibold text-success">Experienced Faculty & Authentic Study Material for Government Exams</p>
                <p>
                  Government exams are highly competitive, and every candidate seeks an edge. At Shastrarth, we guide students from their first step of preparation to the final revision. Our structured plans include study materials, notes, video classes, doubt sessions, test series, and mock tests.
                </p>

                <p className="fw-semibold text-success">Making Education Interesting for School Students</p>
                <p>
                  The shift to online education raised concerns for parents, but at Shastrarth, we ensure engaging and interactive learning experiences. Our faculty makes learning fun through innovative teaching methods, ensuring that students grasp concepts effectively.
                </p>

                <p className="fw-semibold text-success">Why Choose Shastrarth?</p>
                <p>The key reasons why thousands of students trust Shastrarth:</p>
                <ul className="mt-3 list-unstyled">
                  <li className="mb-3"><span className="bg-success bg-opacity-75 text-white py-1 px-3 rounded-5">1 Lakh+ Selection:</span> Over 1 lakh candidates have successfully secured government jobs with our expert guidance.</li>
                  <li className="mb-3"><span className="bg-success bg-opacity-75 text-white py-1 px-3 rounded-5">Fruitful Live Sessions:</span> Interactive classes where students share ideas, ask questions, and learn effectively.</li>
                  <li className="mb-3"><span className="bg-success bg-opacity-75 text-white py-1 px-3 rounded-5">Top-notch Faculty:</span> Experienced educators who stay updated with the latest exam trends and provide personalized mentorship.</li>
                  <li className="mb-3"><span className="bg-success bg-opacity-75 text-white py-1 px-3 rounded-5">Authentic Study Material:</span> Expert-crafted materials aligned with the latest exam patterns for easy learning.</li>
                  <li className="mb-3"><span className="bg-success bg-opacity-75 text-white py-1 px-3 rounded-5">Mock Tests & Test Series:</span> Real-exam-like tests to help students build confidence and improve performance.</li>
                </ul>
              </Card.Body>
            </Card>

          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default AboutSection;