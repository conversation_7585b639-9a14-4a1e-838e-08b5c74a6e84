import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from 'react-redux';
import { But<PERSON>, Container, Row, Col, Card, Badge } from "react-bootstrap";
import { submitTest } from "../../redux/slice/paperSlice";
import { FaChessBoard, FaChessPawn, FaChessQueen, FaClock, FaLock, FaUnlock } from "react-icons/fa";
import { ProgressBar } from "react-bootstrap"
import { useNavigate } from "react-router-dom";

const ExamDashboard = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
    const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
    const [currentSubQuestionIndex, setCurrentSubQuestionIndex] = useState(0);
    const [expandedSection, setExpandedSection] = useState(0); 
    const [lockedSections, setLockedSections] = useState([]);
    const testData = useSelector((state) => state?.paper?.testData);
    const paper_id = useSelector((state) => state?.paper?.testData?.details?.paper_id);
    const test_name = useSelector((state) => state?.paper?.testData?.details?.subcourse_name);

    const [timers, setTimers] = useState(
        testData.sections.map((section) => section.section_time * 60)
    );
    const [answers, setAnswers] = useState([]);
    const [visitedQuestions, setVisitedQuestions] = useState({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const currentSection = testData.sections[currentSectionIndex];

    useEffect(() => {
        const interval = setInterval(() => {
            setTimers((prevTimers) => {
                const newTimers = [...prevTimers];
                if (newTimers[currentSectionIndex] > 0) {
                    newTimers[currentSectionIndex] -= 1;
                } else {
                    handleSubmitSection(currentSectionIndex);
                    if (currentSectionIndex < testData.sections.length - 1) {
                        moveToNextSection();
                    } else {
                        autoSaveAllSectionsAndSubmit(); // Auto-save all sections and submit the test
                    }
                }
                return newTimers;
            });
        }, 1000);

        return () => clearInterval(interval);
    }, [currentSectionIndex]);

    useEffect(() => {
        setExpandedSection(currentSectionIndex);
    }, [currentSectionIndex]);

    const moveToNextSection = () => {
        if (currentSectionIndex < testData.sections.length - 1) {
            setCurrentSectionIndex((prev) => prev + 1);
        } else {
            alert("Exam completed!");
            handleSubmit(); // Automatically submit the test
        }
    };

    const handleOptionSelect = (questionId, optionId, marks, negativeMarks) => {
        setAnswers((prev) => {
            const updatedAnswers = prev.filter((ans) => ans.question_id !== questionId);
            return [...updatedAnswers, { section: currentSection.section_name, question_id: questionId, selected_option: optionId, marks, negative_marks: negativeMarks }];
        });

        setVisitedQuestions((prev) => ({
            ...prev,
            [questionId]: "attempted",
        }));
    };

    const markQuestionVisited = (questionId) => {
        if (!visitedQuestions[questionId]) {
            setVisitedQuestions((prev) => ({
                ...prev,
                [questionId]: "visited",
            }));
        }
    };

    const goToNextQuestion = () => {
        markQuestionVisited(currentSubQuestion ? currentSubQuestion.question_id : currentQuestion.question_id);
        if (currentQuestion.question_type === "master_question" || currentQuestion.question_type === "master_option") {
            if (currentSubQuestionIndex < currentQuestion.subquestions.length - 1) {
                setCurrentSubQuestionIndex(currentSubQuestionIndex + 1);
            } else if (currentQuestionIndex < currentSection.questions.length - 1) {
                setCurrentQuestionIndex(currentQuestionIndex + 1);
                setCurrentSubQuestionIndex(0);
            } else if (currentSectionIndex < testData.sections.length - 1) {
                setCurrentSectionIndex(currentSectionIndex + 1);
                setCurrentQuestionIndex(0);
                setCurrentSubQuestionIndex(0);
            }
        } else {
            if (currentSubQuestionIndex < currentQuestion.subquestions.length - 1) {
                setCurrentSubQuestionIndex(currentSubQuestionIndex + 1);
            } else if (currentQuestionIndex < currentSection.questions.length - 1) {
                setCurrentQuestionIndex(currentQuestionIndex + 1);
                setCurrentSubQuestionIndex(0);
            } else if (currentSectionIndex < testData.sections.length - 1) {
                setCurrentSectionIndex(currentSectionIndex + 1);
                setCurrentQuestionIndex(0);
                setCurrentSubQuestionIndex(0);
            }
        }
    };

    const goToPreviousQuestion = () => {
        markQuestionVisited(currentSubQuestion ? currentSubQuestion.question_id : currentQuestion.question_id);
        if (currentQuestion.question_type === "master_question" || currentQuestion.question_type === "master_option") {
            if (currentSubQuestionIndex > 0) {
                setCurrentSubQuestionIndex(currentSubQuestionIndex - 1);
            } else if (currentQuestionIndex > 0) {
                setCurrentQuestionIndex(currentQuestionIndex - 1);
                setCurrentSubQuestionIndex(currentSection.questions[currentQuestionIndex - 1].subquestions.length - 1);
            } else if (currentSectionIndex > 0) {
                setCurrentSectionIndex(currentSectionIndex - 1);
                setCurrentQuestionIndex(testData.sections[currentSectionIndex - 1].questions.length - 1);
                setCurrentSubQuestionIndex(testData.sections[currentSectionIndex - 1].questions[testData.sections[currentSectionIndex - 1].questions.length - 1].subquestions.length - 1);
            }
        } else {
            if (currentSubQuestionIndex > 0) {
                setCurrentSubQuestionIndex(currentSubQuestionIndex - 1);
            } else if (currentQuestionIndex > 0) {
                setCurrentQuestionIndex(currentQuestionIndex - 1);
                setCurrentSubQuestionIndex(currentSection.questions[currentQuestionIndex - 1].subquestions.length - 1);
            } else if (currentSectionIndex > 0) {
                setCurrentSectionIndex(currentSectionIndex - 1);
                setCurrentQuestionIndex(testData.sections[currentSectionIndex - 1].questions.length - 1);
                setCurrentSubQuestionIndex(testData.sections[currentSectionIndex - 1].questions[testData.sections[currentSectionIndex - 1].questions.length - 1].subquestions.length - 1);
            }
        }
    };

    const currentQuestion = currentSection.questions[currentQuestionIndex];
    const currentSubQuestion = currentQuestion.subquestions ? currentQuestion.subquestions[currentSubQuestionIndex] : null;

    const getProgressColor = (progress) => {
        if (progress <= 25) return "#dc3545"; // Red (0-25%)
        if (progress <= 50) return "#ffc107"; // Yellow (25-50%)
        if (progress <= 75) return "#007bff"; // Blue (50-75%)
        return "#28a745"; // Green (75-100%)
    };

    const uniqueQuestionIds = new Set();
    testData.sections.forEach(section => {
        section.questions.forEach(question => {
            if (question.subquestions) {
                question.subquestions.forEach(subQuestion => {
                    uniqueQuestionIds.add(subQuestion.question_id);
                });
            } else {
                question.subquestions.forEach(subQuestion => {
                    uniqueQuestionIds.add(subQuestion.question_id);
                });
            }
        });
    });

    const totalUniqueQuestions = uniqueQuestionIds.size;
    console.log("Total Unique Questions:", totalUniqueQuestions);

    const attemptedQuestions = Object.keys(visitedQuestions).filter(questionId => visitedQuestions[questionId] === "attempted").length;
    const progress = Math.min((attemptedQuestions / totalUniqueQuestions) * 100, 100);

    const getGradientBackground = (timeLeft, totalTime) => {
    const percentage = (timeLeft / totalTime) * 100;

    if (percentage >= 50) {
        return `linear-gradient(to right, #28a745 ${(percentage - 50) * 2}%, #ffc107 100%)`;
    } else if (percentage > 0) {
        return `linear-gradient(to right, #ffc107 ${percentage * 2}%, #dc3545 100%)`;
    } else {
        return `#dc3545`; // When time is 0, make it fully red
    }
};


    const getButtonStyle = (questionId) => {
        if (visitedQuestions[questionId] === "attempted") return { color: "green" };  // Attempted (Green)
        if (visitedQuestions[questionId] === "visited") return { color: "orange" };   // Visited (Yellow)
        return { color: "gray" };  // Not visited (Gray)
    };

    const getButtonIcon = (questionId) => {
        const iconStyle = { fontSize: "1.5rem" }; // Increase icon size
        if (visitedQuestions[questionId] === "attempted") return <FaChessQueen style={iconStyle} />;
        if (visitedQuestions[questionId] === "visited") return <FaChessPawn style={iconStyle} />;
        return <FaChessBoard style={iconStyle} className="text-dark" />;
    };

    const autoSaveAllSectionsAndSubmit = async () => {
        for (let i = 0; i < testData.sections.length; i++) {
            handleSubmitSection(i);
        }
        handleSubmit();
    };

    const handleSubmit = async () => {
        if (!answers.length) {
            alert("Please answer at least one question before submitting.");
            return;
        }

        setIsSubmitting(true);
        console.log("paper_id", paper_id)
        console.log("answers", answers)

        const result = await dispatch(submitTest({ paper_id: paper_id, answers: answers }));

        if (result.meta.requestStatus === "fulfilled") {
            // alert("Exam submitted successfully!");
            console.log("Response:", result.payload);
            navigate("/test-result");
        } else {
            alert(`Submission failed: ${result.payload || "Unknown error"}`);
            console.error("Error:", result);
        }
        setIsSubmitting(false);
    };

    const handleSubmitSection = (sectionIndex) => {
        const sectionAnswers = answers.filter(ans => ans.section === testData.sections[sectionIndex].section_name);
        if (!sectionAnswers.length) {
            alert("Please answer at least one question in this section before submitting.");
            return;
        }

        alert(`Section ${testData.sections[sectionIndex].section_name} submitted successfully!`);
        setLockedSections([...lockedSections, sectionIndex]);
    };

    return (
        <>
        <section className="custom-offcanvas"  style={{
            minHeight: "100vh",
            minWidth: "100vw",
            background:
              "linear-gradient(130deg, white,rgb(129, 194, 141)), url('https://www.transparenttextures.com/patterns/gplay.png')",
            // backgroundSize: "contain",
            backgroundBlendMode: "overlay",
            opacity: 0.9,
            // backgroundImage:
            //   "linear-gradient(90deg, rgba(232, 219, 219, 0.3) 10%, rgba(164, 234, 211, 0.3) 90%)",
          }}>
        <Container className="pt-4">
            <div fluid className="p-3">
                <h5 className="text-center test-success" >{test_name}</h5>
                <h6 className="mb-4">Test Progress</h6>
                <div style={{ width: "100%", background: "#fff", borderRadius: "5px", position: "relative" }}>
                    <div style={{
                        width: `${progress}%`,
                        height: "12px",
                        backgroundColor: getProgressColor(progress),
                        borderRadius: "5px",
                        transition: "width 0.5s ease-in-out",
                        position: "relative",
                    }}>
                        <span style={{
                            position: "absolute",
                            top: "-20px",
                            left: "50%",
                            transform: "translateX(-50%)",
                            fontSize: "12px",
                            fontWeight: "bold",
                            color: "black",
                        }}>
                            {Math.round(progress)}%
                        </span>
                    </div>
                </div>
            </div>

            <Row>
                <Col md={8}>
                    <Card className="mb-3 rounded-5">
                        <Card.Body>
                            {currentQuestion.question_type === "master_question" || currentQuestion.question_type === "master_option" ? (
                                <>
                                    <div style={{ position: "sticky", top: 0, background: "white", zIndex: 1 }}>
                                        <h6>{currentQuestion.title}</h6>
                                        <p>{currentQuestion.passage_content}</p>
                                        {currentQuestion.conditions && <p>{currentQuestion.conditions}</p>}
                                    </div>
                                    <div style={{ maxHeight: "400px", overflowY: "auto" }}>
                                        {currentSubQuestion && (
                                            <>
                                                <h6 > <span className="p-2 mt-2 bg-success text-light rouded-5" style={{borderRadius: "1rem"}}> Q {currentSubQuestionIndex + 1} of {currentQuestion.subquestions.length}</span> {currentSubQuestion.content}</h6>
                                                <ul className="list-unstyled">
                                                    {currentSubQuestion.options.map((option) => (
                                                        <li key={option.option_id} className="mb-2">
                                                            <input
                                                                type="radio"
                                                                name={`question_${currentSubQuestion.question_id}`}
                                                                value={option.option_id}
                                                                className="mx-2"
                                                                checked={answers.some(ans => ans.question_id === currentSubQuestion.question_id && ans.selected_option === option.option_id)}
                                                                onChange={() => handleOptionSelect(
                                                                    currentSubQuestion.question_id,
                                                                    option.option_id,
                                                                    currentSubQuestion.marks,
                                                                    currentSubQuestion.negative_marks
                                                                )}
                                                            />
                                                            <label>{option.option_text}</label>
                                                        </li>
                                                    ))}
                                                </ul>
                                            </>
                                        )}
                                    </div>
                                </>
                            ) : (
                                <div key={currentSubQuestion.question_id}>
                                    <h6>Q{currentQuestionIndex + 1}: {currentSubQuestion.content}</h6>
                                    <ul className="list-unstyled">
                                        {currentSubQuestion.options.map((option) => (
                                            <li key={option.option_id} className="mb-2">
                                                <input
                                                    type="radio"
                                                    name={`question_${currentSubQuestion.question_id}`}
                                                    value={option.option_id}
                                                    className="mx-2"
                                                    checked={answers.some(ans => ans.question_id === currentSubQuestion.question_id && ans.selected_option === option.option_id)}
                                                    onChange={() => handleOptionSelect(
                                                        currentSubQuestion.question_id,
                                                        option.option_id,
                                                        currentSubQuestion.marks,
                                                        currentSubQuestion.negative_marks
                                                    )}
                                                />
                                                <label>{option.option_text}</label>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                        </Card.Body>
                    </Card>

                    <div className="d-flex justify-content-between my-3">
                        <Button variant="outline-dark" onClick={goToPreviousQuestion} disabled={currentSectionIndex === 0 && currentQuestionIndex === 0 && currentSubQuestionIndex === 0}>
                            Previous
                        </Button>

                        <Button variant="outline-dark" onClick={goToNextQuestion} disabled={currentSectionIndex === testData.sections.length - 1 && currentQuestionIndex === currentSection.questions.length - 1 && currentSubQuestionIndex === currentQuestion.subquestions.length - 1}>
                            Next
                        </Button>
                    </div>
                </Col>

                <Col md={4}>
                    <Card className="rounded-3">
                        <Card.Body>
                            {testData.sections.map((section, secIndex) => (
                                <div key={secIndex}>
                                    <Card.Header
                                        style={{
                                            background: getGradientBackground(timers[secIndex], section.section_time * 60),
                                            color: "black",
                                            padding: "10px",
                                            fontWeight: "bold",
                                            transition: "background 0.5s ease-in-out",
                                            cursor: "pointer",
                                            marginBottom: "1rem",
                                            display: "flex",
                                            justifyContent: "space-between",
                                            alignItems: "center"
                                        }}
                                        onClick={() => setExpandedSection(expandedSection === secIndex ? null : secIndex)}
                                    >
                                        <div>
                                            <h6>{section.section_name}</h6>
                                            <div className="d-flex justify-content-between">
                                                <div><h6 style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                                                <FaClock /> {Math.floor(timers[secIndex] / 60)}:
                                                {(timers[secIndex] % 60).toString().padStart(2, "0")}
                                            </h6></div>
                                            <div>
                                                
                                            </div>
                                            </div>
                                            
                                        </div>
                                        <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
                                            {lockedSections.includes(secIndex) ? <FaLock style={{ color: "red" }} /> : <FaUnlock style={{ color: "green" }} />}
                                            {!lockedSections.includes(secIndex) && (
                                                <Button variant="outline-light" size="sm" onClick={(e) => { e.stopPropagation(); handleSubmitSection(secIndex); }}>
                                                    Submit Section
                                                </Button>
                                            )}
                                        </div>
                                    </Card.Header>
                                    {expandedSection === secIndex && (
                                        <div style={{ display: "flex", flexWrap: "wrap", gap: "5px", padding: "10px" }}>
                                            {section.questions.map((question, index) => (
                                                question.subquestions.map((subQuestion, subIndex) => (
                                                    <Button
                                                        key={subQuestion.question_id}
                                                        variant="light"
                                                        size="sm"
                                                        className="m-1"
                                                        style={{
                                                            ...getButtonStyle(subQuestion.question_id),
                                                            display: "flex",
                                                            flexDirection: "column",
                                                            alignItems: "center",
                                                            justifyContent: "center",
                                                            width: "50px",
                                                            height: "60px",
                                                            padding: "5px",
                                                        }}
                                                        onClick={() => {
                                                            setCurrentSectionIndex(secIndex);
                                                            setCurrentQuestionIndex(index);
                                                            setCurrentSubQuestionIndex(subIndex);
                                                            markQuestionVisited(subQuestion.question_id);
                                                        }}
                                                        disabled={lockedSections.includes(secIndex)}
                                                    >
                                                        <span style={getButtonStyle(subQuestion.question_id)}>{getButtonIcon(subQuestion.question_id)}</span>
                                                        <span style={getButtonStyle(subQuestion.question_id)}>{index + 1}.{subIndex + 1}</span> {/* Show sequential numbers */}
                                                    </Button>
                                                ))
                                            ))}
                                        </div>
                                    )}
                                </div>
                            ))}
                            <Button className="mt-3 w-100" variant="outline-success" onClick={handleSubmit} disabled={isSubmitting}>
                                Submit Exam
                            </Button>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
        </section>
        </>
    );
};

export default ExamDashboard;
