import React from 'react'
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Row } from 'react-bootstrap'
import { Link } from 'react-router-dom'

const MyAttmptedTests = ({myAttemptedTests}) => {
  return (
    <>
        <Row>
            {myAttemptedTests.map((test, index) => (
                <Col key={test.id} md={6} lg={3} className="mb-4">
              <Card style={{minHeight: "200px"}} className="shadow-sm p-2 bg-light rounded-4">
                {/* Body */}
                <Card.Body>
                  {/* Smaller title */}
                  <Card.Title className="fs-6">{test.title}</Card.Title>
                  <div className="d-flex justify-content-start mb-2 gap-2">
                    <small>
                      <i className="fa-solid fa-clock"></i> {test.duration}
                    </small>
                    <small>
                      <i className="fa-solid fa-star"></i> {test.marks}
                    </small>
                  </div>
                  <div className="d-flex justify-content-between align-items-center">
                    <small>{test.date}</small>
                  </div>
                    <Button className="btn btn-success mt-4 w-100" as={Link} to="/test-attempt">
                      Appear Again
                    </Button>
                </Card.Body>

                {/* Footer with syllabus link and language icons */}
                {/* <div className="p-2 bg-light">
                  <small>
                    <a href="#!" className="text-success text-decoration-none">
                      Syllabus
                    </a>
                    <span className="ms-2">
                      <FaLanguage />{" "}
                      {test.syllabus.split(",").map((lang, index) => (
                        <span key={index} className="ms-1">
                          {lang.trim()}
                        </span>
                      ))}
                    </span>
                  </small>
                </div> */}
              </Card>
              </Col>
            ))}
          </Row>
    </>
  )
}

export default MyAttmptedTests
