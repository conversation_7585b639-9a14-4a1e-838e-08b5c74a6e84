import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { Modal } from 'react-bootstrap';
import { selectPopupForPage, markPopupAsShown, clearCurrentPopup } from '../redux/slice/viewPopupSlice';

const PopupDisplay = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const { currentPopup, isLoading } = useSelector((state) => state.viewPopup);
  const [showPopup, setShowPopup] = useState(false);
  const [popupTimeout, setPopupTimeout] = useState(null);
  const [delayTimeout, setDelayTimeout] = useState(null);

  // Check for popups when location changes
  useEffect(() => {
    const currentPath = location.pathname;
    
    // Clear any existing timeouts
    if (delayTimeout) {
      clearTimeout(delayTimeout);
    }
    if (popupTimeout) {
      clearTimeout(popupTimeout);
    }

    // Hide current popup if showing
    setShowPopup(false);

    // Fetch popup for current page
    dispatch(selectPopupForPage({ currentPath }));
  }, [location.pathname, dispatch]);

  // Handle showing popup when currentPopup changes
  useEffect(() => {
    if (currentPopup && !isLoading) {
      const delay = currentPopup.delay_ms || 0;
      
      // Set delay timeout
      const delayTimer = setTimeout(() => {
        setShowPopup(true);
        
        // Set duration timeout to auto-close popup
        const duration = currentPopup.display_duration || 5000;
        const durationTimer = setTimeout(() => {
          handleClosePopup();
        }, duration);
        
        setPopupTimeout(durationTimer);
      }, delay);
      
      setDelayTimeout(delayTimer);
    }

    // Cleanup function
    return () => {
      if (delayTimeout) {
        clearTimeout(delayTimeout);
      }
      if (popupTimeout) {
        clearTimeout(popupTimeout);
      }
    };
  }, [currentPopup, isLoading]);

  const handleClosePopup = () => {
    setShowPopup(false);
    
    if (currentPopup) {
      // Mark popup as shown
      dispatch(markPopupAsShown(currentPopup.id));
    }
    
    // Clear current popup
    dispatch(clearCurrentPopup());
    
    // Clear timeouts
    if (popupTimeout) {
      clearTimeout(popupTimeout);
      setPopupTimeout(null);
    }
    if (delayTimeout) {
      clearTimeout(delayTimeout);
      setDelayTimeout(null);
    }
  };

  const handleMainAction = () => {
    if (currentPopup?.link_url) {
      if (currentPopup.anchor_tag === '_blank') {
        window.open(currentPopup.link_url, '_blank');
      } else {
        window.location.href = currentPopup.link_url;
      }
    }
  };

  // Render popup content based on type
  const renderPopupContent = () => {
    if (!currentPopup) return null;

    return (
      <div style={{
        background: 'white',
        borderRadius: '16px',
        padding: '2rem',
        maxWidth: '400px',
        width: '100%',
        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
        textAlign: 'center',
        margin: '0 auto'
      }}>
        {/* Title */}
        <h4 style={{
          marginBottom: '1rem',
          color: '#333',
          fontSize: '1.25rem',
          fontWeight: '600',
          lineHeight: '1.4'
        }}>
          {currentPopup.title}
        </h4>

        {/* Content based on type */}
        {currentPopup.content_type === "text_only" && (
          <div style={{
            color: '#666',
            marginBottom: '1.5rem',
            fontSize: '0.95rem',
            lineHeight: '1.5'
          }}>
            {currentPopup.text_content}
          </div>
        )}

        {currentPopup.content_type === "image_only" && currentPopup.image && (
          <div style={{ marginBottom: '1.5rem' }}>
            <img
              src={currentPopup.image}
              alt="Popup"
              style={{
                maxWidth: '100%',
                maxHeight: '200px',
                objectFit: 'contain',
                borderRadius: '8px'
              }}
            />
          </div>
        )}

        {currentPopup.content_type === "text_and_image" && (
          <>
            {currentPopup.image && (
              <div style={{ marginBottom: '1rem' }}>
                <img
                  src={currentPopup.image}
                  alt="Popup"
                  style={{
                    maxWidth: '100%',
                    maxHeight: '150px',
                    objectFit: 'contain',
                    borderRadius: '8px'
                  }}
                />
              </div>
            )}
            {currentPopup.text_content && (
              <div style={{
                color: '#666',
                marginBottom: '1.5rem',
                fontSize: '0.95rem',
                lineHeight: '1.5'
              }}>
                {currentPopup.text_content}
              </div>
            )}
          </>
        )}

        {currentPopup.content_type === "text_link" && (
          <>
            {currentPopup.text_content && (
              <div style={{
                color: '#666',
                marginBottom: '1.5rem',
                fontSize: '0.95rem',
                lineHeight: '1.5'
              }}>
                {currentPopup.text_content}
              </div>
            )}
          </>
        )}

        {currentPopup.content_type === "link_anchor" && (
          <div style={{
            color: '#666',
            marginBottom: '1.5rem',
            fontSize: '0.95rem',
            lineHeight: '1.5'
          }}>
            {currentPopup.description}
          </div>
        )}

        {/* Action buttons */}
        <div style={{ display: 'flex', gap: '0.75rem', justifyContent: 'center' }}>
          {(currentPopup.content_type === "text_link" || 
            currentPopup.content_type === "link_anchor" || 
            currentPopup.link_url) && (
            <button
              onClick={handleMainAction}
              style={{
                background: '#007bff',
                color: 'white',
                border: 'none',
                padding: '0.75rem 1.5rem',
                borderRadius: '8px',
                fontSize: '0.9rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'background-color 0.2s'
              }}
              onMouseOver={(e) => e.target.style.background = '#0056b3'}
              onMouseOut={(e) => e.target.style.background = '#007bff'}
            >
              {currentPopup.link_text || 'Learn More'}
            </button>
          )}
          <button
            onClick={handleClosePopup}
            style={{
              background: '#6c757d',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '8px',
              fontSize: '0.9rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={(e) => e.target.style.background = '#545b62'}
            onMouseOut={(e) => e.target.style.background = '#6c757d'}
          >
            Close
          </button>
        </div>
      </div>
    );
  };

  if (!currentPopup || !showPopup) {
    return null;
  }

  return (
    <>
      <Modal
        show={showPopup}
        onHide={handleClosePopup}
        size="lg"
        centered
        backdrop="static"
        dialogClassName="popup-display-modal"
      >
        <Modal.Body style={{ 
          padding: '2rem', 
          border: 'none',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '200px',
          background: 'transparent'
        }}>
          {renderPopupContent()}
        </Modal.Body>
      </Modal>
      <style jsx>{`
        .popup-display-modal .modal-dialog {
          max-width: 90vw;
        }
        .popup-display-modal .modal-content {
          border: none;
          border-radius: 16px;
          overflow: hidden;
          background: transparent;
        }
        /* Fullscreen black overlay for modal backdrop */
        .modal-backdrop.show {
          opacity: 1 !important;
          background: rgba(0, 0, 0, 0.5) !important;
        }
      `}</style>
    </>
  );
};

export default PopupDisplay;
