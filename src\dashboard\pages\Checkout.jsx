import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { <PERSON><PERSON>, Col, Container, Row } from 'react-bootstrap';
import { FaTimes, FaCheckCircle, FaExclamationCircle } from "react-icons/fa";
import { validateCouponCode, validateGiftCard, createSubscription, verifyPayment, getRazorpayConfig } from '../../redux/slice/subscriptionSlice';
import { useNavigate } from "react-router-dom";
import { setSelectedPackageId } from "../../redux/slice/packageSlice";
import { toast, Toaster } from "react-hot-toast";

const Checkout = ({ checkoutDetails, setCheckoutDetails }) => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const studentId = useSelector(state => state?.student?.student?.student?.id);
    const [couponCode, setCouponCode] = useState('');
    const [giftCardCode, setGiftCardCode] = useState('');
    const [giftCardPin, setGiftCardPin] = useState('');
    const [discount, setDiscount] = useState(0);
    const [discountType, setDiscountType] = useState('');
    const [couponMessage, setCouponMessage] = useState(null); // Separate message for coupon
    const [giftCardMessage, setGiftCardMessage] = useState(null); // Separate message for gift card
    const [isCouponApplied, setIsCouponApplied] = useState(false);
    const [isGiftCardApplied, setIsGiftCardApplied] = useState(false);
    const [loading, setLoading] = useState(false);
    const [applyingCoupon, setApplyingCoupon] = useState(false); // Separate state for coupon button
    const [applyingGiftCard, setApplyingGiftCard] = useState(false); // Separate state for gift card button

    const handleValidateCoupon = async () => {
        setApplyingCoupon(true);
        setCouponMessage(null); // Clear previous message
        try {
            const response = await dispatch(validateCouponCode({ coupon_code: couponCode })).unwrap();
            if (response.valid) {
                setDiscount(response.discount);
                setDiscountType(response.discount_type);
                setCouponMessage(<><FaCheckCircle className="text-success me-2" />{response.message}</>);
                setIsCouponApplied(true);
                setIsGiftCardApplied(false);
            } else {
                setCouponMessage(<><FaExclamationCircle className="text-danger me-2" />{response.message}</>);
            }
        } catch {
            setCouponMessage(<><FaExclamationCircle className="text-danger me-2" />Invalid coupon code.</>);
        } finally {
            setApplyingCoupon(false);
        }
    };

    const handleValidateGiftCard = async () => {
        setApplyingGiftCard(true);
        setGiftCardMessage(null); // Clear previous message
        try {
            const response = await dispatch(validateGiftCard({ gift_code: giftCardCode })).unwrap();
            if (response.valid) {
                setDiscount(response.amount);
                setDiscountType('amount');
                setGiftCardMessage(<><FaCheckCircle className="text-success me-2" />{response.message}</>);
                setIsGiftCardApplied(true);
                setIsCouponApplied(false);
            } else {
                setGiftCardMessage(<><FaExclamationCircle className="text-danger me-2" />{response.message}</>);
            }
        } catch {
            setGiftCardMessage(<><FaExclamationCircle className="text-danger me-2" />Invalid gift card.</>);
        } finally {
            setApplyingGiftCard(false);
        }
    };

    const handleClearCoupon = () => {
        setCouponCode('');
        setIsCouponApplied(false);
        setCouponMessage(null);
        setDiscount(0); // Reset discount
        setDiscountType(''); // Reset discount type
    };

    const handleClearGiftCard = () => {
        setGiftCardCode('');
        setGiftCardPin('');
        setIsGiftCardApplied(false);
        setGiftCardMessage(null);
        setDiscount(0); // Reset discount
        setDiscountType(''); // Reset discount type
    };

    const handleCheckout = async () => {
        if (!checkoutDetails) return;

        setLoading(true);

        const loadRazorpayScript = () => {
            return new Promise((resolve) => {
                const script = document.createElement("script");
                script.src = "https://checkout.razorpay.com/v1/checkout.js";
                script.onload = () => resolve(true);
                script.onerror = () => resolve(false);
                document.body.appendChild(script);
            });
        };

        const isScriptLoaded = await loadRazorpayScript();
        if (!isScriptLoaded) {
            toast.error("Razorpay SDK failed to load. Are you online?");
            setLoading(false);
            return;
        }

        try {
            const configRes = await dispatch(getRazorpayConfig()).unwrap();
            const subscriptionPayload = {
                student: studentId,
                package: checkoutDetails.packageId,
                start_date: new Date().toISOString().split('T')[0] + "T00:00:00+05:30", // Adding start_date
                ...(isGiftCardApplied && { gift_card_code: giftCardCode, gift_card_pin: giftCardPin }),
                ...(isCouponApplied && { coupon: couponCode }),
            };
            const subscriptionRes = await dispatch(createSubscription(subscriptionPayload)).unwrap();

            const { razorpay_key, company_name, logo_url } = configRes;
            const { razorpay_order, final_price, currency, subscription_id } = subscriptionRes;

            const options = {
                key: razorpay_key,
                amount: razorpay_order.amount,
                currency,
                name: company_name,
                description: "Package Subscription",
                image: logo_url || undefined,
                order_id: razorpay_order.id,
                handler: async function (response) {
                    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = response;
                    try {
                        await dispatch(
                            verifyPayment({
                                razorpay_order_id,
                                razorpay_payment_id,
                                razorpay_signature,
                                subscription_id,
                                amount: razorpay_order.amount,
                            })
                        ).unwrap();

                        // Reset SelectedPackageId
                        dispatch(setSelectedPackageId(null));

                        // Navigate to profile
                        navigate("/dashboard/profile");

                        // Show success toast
                        toast.success("Payment successful! Redirecting to your profile...");
                    } catch (err) {
                        toast.error(`Payment verification failed: ${err.message}`);
                    }
                },
                prefill: {
                    name: "", // Optional: prefill user name
                    email: "", // Optional: prefill email
                },
                theme: {
                    color: "#00c853",
                },
            };

            const rzp = new window.Razorpay(options);
            rzp.open();
        } catch (error) {
            const errorMessage = error?.error || "Payment initiation failed";
            toast.error(errorMessage); // Show specific error message if available
        } finally {
            setLoading(false);
        }
    };

    return (
        <section className='bg-light'>
            <Container className="min-vh-100">
                <Row className="justify-content-center">
                    <Col md={8}>
                        <h1 className="my-5 text-center text-success cabin-sketch-bold">Checkout</h1>
                        <Row>
                            <Col md={6}>
                                <div className="p-3 border rounded bg-white">
                                    <h5 className="fw-bold text-success">{checkoutDetails.packageName}</h5>
                                    <p className="text-muted">What you get:</p>
                                    <ul>
                                        {checkoutDetails.descriptions.map((description, index) => (
                                            <li key={index} className="text-success">
                                                {description}
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            </Col>
                            <Col md={6}>
                                <div className="p-3 border rounded bg-white">
                                    <h5 className="fw-bold">Payment Details</h5>
                                    <p>Original Price: ₹{checkoutDetails.originalPrice}</p>
                                    {discount > 0 && (
                                        <p>
                                            Discount:{" "}
                                            {discountType === "amount"
                                                ? `₹${discount}`
                                                : `${discount}%`}
                                        </p>
                                    )}
                                    <p className="fw-bold">
                                        Final Price: ₹
                                        {discountType === "amount"
                                            ? checkoutDetails.originalPrice - discount
                                            : checkoutDetails.originalPrice *
                                            (1 - discount / 100)}
                                    </p>

                                    {/* Coupon Code Section */}
                                    <div className="d-flex align-items-center mb-3">
                                        <input
                                            type="text"
                                            className="form-control me-2"
                                            placeholder="Enter Coupon Code"
                                            value={couponCode}
                                            onChange={(e) => setCouponCode(e.target.value)}
                                            disabled={isGiftCardApplied || applyingCoupon}
                                        />
                                        {couponCode && (
                                            <FaTimes
                                                className="text-danger me-2 cursor-pointer"
                                                onClick={handleClearCoupon}
                                            />
                                        )}
                                        <Button
                                            variant="success"
                                            onClick={handleValidateCoupon}
                                            disabled={isGiftCardApplied || !couponCode || applyingCoupon}
                                        >
                                            {applyingCoupon ? "Applying..." : "Apply"}
                                        </Button>
                                    </div>
                                    {couponMessage && (
                                        <p className={isCouponApplied ? "text-success" : "text-danger"}>
                                            {couponMessage}
                                        </p>
                                    )}
                                    <input
                                        type="text"
                                        className="form-control me-2"
                                        placeholder="Enter Gift Card Code"
                                        value={giftCardCode}
                                        onChange={(e) => setGiftCardCode(e.target.value)}
                                        disabled={isCouponApplied || applyingGiftCard}
                                    />
                                    {/* Gift Card Section */}
                                    <div className="d-flex align-items-center my-3">

                                        <input
                                            type="password"
                                            className="form-control me-2"
                                            placeholder="Enter Gift Card PIN"
                                            value={giftCardPin}
                                            onChange={(e) => setGiftCardPin(e.target.value)}
                                            disabled={isCouponApplied || applyingGiftCard}
                                        />
                                        {(giftCardCode || giftCardPin) && (
                                            <FaTimes
                                                className="text-danger me-2 cursor-pointer"
                                                onClick={handleClearGiftCard}
                                            />
                                        )}
                                        <Button
                                            variant="success"
                                            onClick={handleValidateGiftCard}
                                            disabled={isCouponApplied || !giftCardCode || !giftCardPin || applyingGiftCard}
                                        >
                                            {applyingGiftCard ? "Applying..." : "Apply"}
                                        </Button>
                                    </div>
                                    {giftCardMessage && (
                                        <p className={isGiftCardApplied ? "text-success" : "text-danger"}>
                                            {giftCardMessage}
                                        </p>
                                    )}

                                    <Button
                                        variant="primary"
                                        onClick={handleCheckout}
                                        disabled={loading}
                                        className="w-100"
                                    >
                                        {loading ? "Processing..." : "Checkout"}
                                    </Button>
                                </div>
                            </Col>
                        </Row>
                    </Col>
                </Row>
            </Container>
            <Toaster />
        </section>
    );
};

export default Checkout;
