import React from "react";
import { But<PERSON> } from "react-bootstrap";

const QuestionNavigation = ({currentQuestionIndex, setCurrentQuestionIndex, currentSection}) => {

    const handleNext = () => {
        if (currentQuestionIndex < currentSection?.questions.length - 1) {
          setCurrentQuestionIndex(currentQuestionIndex + 1);
        }
      };
    
      const handlePrevious = () => {
        if (currentQuestionIndex > 0) {
          setCurrentQuestionIndex(currentQuestionIndex - 1);
        }
      };
  return (
    <>
      <div
        className="d-flex justify-content-between"
        style={{
          position: "sticky",
          bottom: "0",
          backgroundColor: "#fff",
          zIndex: "10",
          padding: "10px",
        }}
      >
        <Button
          variant="secondary"
          onClick={handlePrevious}
          disabled={currentQuestionIndex === 0}
        >
          Previous
        </Button>
        <Button
          variant="success"
          onClick={handleNext}
          disabled={
            currentQuestionIndex === currentSection?.questions.length - 1
          }
        >
          Next
        </Button>
      </div>
    </>
  );
};

export default QuestionNavigation;
