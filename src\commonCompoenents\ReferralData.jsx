import React, { useEffect, useState } from "react";
import { getReferralData } from "../redux/slice/studentSlice";
import toast from "react-hot-toast";
import { useDispatch } from "react-redux";
import { <PERSON><PERSON>, Card, Container, Row, Col } from "react-bootstrap";
// import referData from "../../dummyData/referData";
import { FaShare } from "react-icons/fa";
import { IoShareSocialSharp } from "react-icons/io5";
import Skeleton from "react-loading-skeleton";

const ReferralData = ({ profile }) => {
    const [referData, setReferData] = useState(null);
  const [loadRefer, setLoadRefer] = useState(false);

  const dispatch = useDispatch();

    useEffect(() => {
      const getReferallData = async () => {
        setLoadRefer(true);
        try {
          const res = await dispatch(getReferralData());
          console.log(res);

          if (res?.meta?.requestStatus === "fulfilled") {
              console.log(res);

            console.log(res?.payload);
            setReferData(res?.payload);
          }
        } catch (error) {
          console.log(error);
          toast.error("Error occurred while fetching referral data");
        } finally {
          setLoadRefer(false);
        }
      };

      getReferallData();
    }, [dispatch]);

  const handleShare = () => {
    if (navigator.share) {
      navigator
        .share({
          title: "Referral Code",
          text: `Use my referral code: ${referData?.referral_code}\nAnd get the discount bonus on memberships\n\nClick here to Register\n`,
          url: `https://shashtrarth.com/students/signup-request/${referData?.referral_code}`,
        })
        .then(() => console.log("Successful share"))
        .catch((error) => console.log("Error sharing", error));
    } else {
      // Fallback for browsers that do not support the Web Share API
      navigator.clipboard
        .writeText(
          `Use my referral code: ${referData?.referral_code}\nhttps://shashtrarth.com/students/signup-request/${referData?.qr_code_url}`
        )
        .then(() => {
          toast.success("Referral code and QR code URL copied to clipboard");
        })
        .catch((error) => {
          toast.error("Failed to copy to clipboard");
          console.error("Error copying to clipboard", error);
        });
    }
  };

  return (
    <Container className={`${profile ? "" : "mt-lg-0"}`}>
      {loadRefer ? (
        <>
            <Skeleton height={150} baseColor="#e6ffe6" highlightColor="#c4f7c4"/> 
        </>
      ) : (
        referData && (
          <Card className={`${profile ? 'rounded-3 bg-transparent border-0' : 'border-0 text-center w-100'}`}>
            <Card.Body className="">
              {!profile && (
                <Row className="text-center">
                  <Col>
                    <h5 className="text-center">Referral Code</h5>
                    <p className="fw-bold fs-5 text-success">{referData?.referral_code}</p>
                  </Col>
                </Row>
              )}

              <Row className="text-center">
                <Col>
                  {!profile && <h5 className="m-0 mt-1 text-center">QR Code</h5>}
                  {
                    profile ? (
                        <img
                    src={`${import.meta.env.VITE_BASE_URL}${
                      referData?.qr_code_url
                    }`}
                    alt="QR Code"
                    style={{ maxWidth: "100px" }}
                  />
                    ) : (
                        <img
                    src={`${import.meta.env.VITE_BASE_URL}${
                      referData?.qr_code_url
                    }`}
                    alt="QR Code"
                    style={{ maxWidth: "200px" }}
                  />
                    )
                  }
                  
                  {profile && (
                    <p className="d-flex align-items-center justify-content-center gap-1 text-center fw-semibold">
                      {referData?.referral_code}{" "}
                      <Button
                        variant="success"
                        className="p-0 m-0 bg-transparent text-success border-0 d-flex justify-content-center align-items-center"
                        onClick={handleShare}
                      >
                        <IoShareSocialSharp />
                      </Button>
                    </p>
                  )}
                  {!profile && (
                    <>
                      {/* <p className="fw-semibold">{referData?.referral_code}</p> */}
                      <br/>
                      <Button
                        variant="success"
                        className="btn-sm"
                        onClick={handleShare}
                      >
                        Share
                      </Button>
                    </>
                  )}
                </Col>
              </Row>
            </Card.Body>
          </Card>
        )
      )}
    </Container>
  );
};

export default ReferralData;
