importScripts("https://www.gstatic.com/firebasejs/9.9.0/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/9.9.0/firebase-messaging-compat.js");

// Initialize Firebase App inside the service worker
const firebaseConfig = {
  apiKey: "AIzaSyBJSLcv459JwCzNhkgOndDQ7wzuAx81YwU",
  authDomain: "shashtrarth-fcm.firebaseapp.com",
  projectId: "shashtrarth-fcm",
  storageBucket: "shashtrarth-fcm.firebasestorage.app",
  messagingSenderId: "826207026444",
  appId: "1:826207026444:web:ea2f8a6287c93ecebc1d29",
  measurementId: "G-Y8GGEV9RWX",
};

firebase.initializeApp(firebaseConfig);

const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log(
    "[firebase-messaging-sw.js] Received background message:",
    payload
  );

  const notificationTitle = payload.notification.title;
  const notificationDescription = payload.notification.description;
  const notificationOptions = {
    body: payload.notification.body,
    logo: '',
    description: notificationDescription,
    image: payload.notification.image,
  };

  return self.registration.showNotification(
    notificationTitle,
    notificationOptions
  );
});
