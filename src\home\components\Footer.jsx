import React from "react";
import { Container, Row, Col, ListGroup, Form, Button, InputGroup, Image } from "react-bootstrap";
import { FaFacebookF, FaInstagram, FaLinkedin, FaSearch, FaTwitter, FaShieldAlt, FaCreditCard } from "react-icons/fa";
import { FaXTwitter } from 'react-icons/fa6';

import { Link } from "react-router-dom";
import { useTheme } from "../../context/ThemeContext";

const Footer = () => {
  const { isDarkMode, theme } = useTheme();

  return (
    <footer
      className="py-4"
      id="footer"
      style={{
        backgroundColor: isDarkMode ? '#1a1a1a' : '#343a40',
        color: '#ffffff',
        transition: 'background-color 0.3s ease'
      }}
    >
      <Container fluid className="px-2 px-md-5">
        <Row className="justify-content-between gy-4">
          {/* Left Column: Links */}
          <Col md={4} xs={12} className="mb-md-0 mb-4">
            <h5>Quick Links</h5>
            <ListGroup variant="flush">
              <ListGroup.Item as={Link} to="/terms_condition" className="bg-dark text-white footer-link">
                Terms and Conditions
              </ListGroup.Item>
              <ListGroup.Item as={Link} to="/contact-us" className="bg-dark text-white footer-link">
                Contact Us
              </ListGroup.Item>
              <ListGroup.Item action href="#" className="bg-dark text-white footer-link">
                Parent Company
              </ListGroup.Item>
              <ListGroup.Item as={Link} to="/about-us" className="bg-dark text-white footer-link">
                About Us
              </ListGroup.Item>
              <ListGroup.Item as={Link} to="/privacy_policy" className="bg-dark text-white footer-link">
                Privacy Policy
              </ListGroup.Item>
              <ListGroup.Item as={Link} to="/refund_policy" className="bg-dark text-white footer-link">
                Refund Policy
              </ListGroup.Item>
              {/* <ListGroup.Item action href="#" className="bg-dark text-white footer-link">
                User Policy
              </ListGroup.Item> */}
              <ListGroup.Item as={Link} to="/refer-and-earn" className="bg-dark text-white footer-link">
                Refer and Earn
              </ListGroup.Item>

              <ListGroup.Item action href="https://www.pinakventure.com/work-with-us" className="bg-dark text-white footer-link">
                Work with us
              </ListGroup.Item>

              <ListGroup.Item action href="https://www.pinakventure.com/our-team" className="bg-dark text-white footer-link">
                Our Team
              </ListGroup.Item>

              {/* <ListGroup.Item action href="#" className="bg-dark text-white footer-link">
                Download Our App
              </ListGroup.Item> */}
            </ListGroup>
          </Col>

          {/* Center Column: Address and Email */}
          <Col md={4} xs={12} className="text-md-center mb-md-0 mb-4">
            <h5>Contact Info</h5>
            <p>           
             Pinak Venture, F2/9 Jai Durga Society, Netaji Nagar, Hill No 3, 90ft Road, Sakinaka, Kurla West Mumbai, India
            </p>
            <p>Email: <a href="mailto:<EMAIL>"
              className="text-white text-decoration-none"
              style={{ borderBottom: '1px dotted white' }}><EMAIL></a></p>

<h5>Follow Us</h5>
            <p>Follow us on social media to stay updated!</p>
            <div className="mb-3">
              <Button
                variant="outline-light"
                className="me-2 btn-sm"
                style={{ backgroundColor: "#0A66C2", borderColor: "#0A66C2" }}
              >
                <FaLinkedin className="pb-1" />
              </Button>
              <Button
                variant="outline-light"
                className="me-2 btn-sm"
                style={{
                  background:
                    "linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888)",
                  borderColor: "#cc2366",
                }}
              >
                <FaInstagram className="pb-1" />
              </Button>
              <Button
                variant="outline-light"
                className="me-2 btn-sm"
                style={{ backgroundColor: "#1877F2", borderColor: "#1877F2" }}
              >
                <FaFacebookF className="pb-1" />
              </Button>
              <Button
                variant="outline-light"
                className="me-2 text-white btn-sm"
                style={{ backgroundColor: "#000000", borderColor: "#000000" }}
              >
                <FaXTwitter className="pb-1" />
              </Button>
            </div>

          </Col>

          {/* Right Column: Follow Us and Payment Info */}
          <Col md={4} xs={12} className="mb-md-0 mb-4">
            
            {/* Payment Gateway Information */}
            <div className="mt-0">
              <h6 className="text-white mb-2">
                <FaShieldAlt className="me-2" />
                Secure Payments
              </h6>
              <p className="small mb-2">
                We use <strong>Razorpay</strong> as our trusted payment gateway.
                It supports all major payment modes — UPI, cards, netbanking & wallets.
                Razorpay ensures fast, secure transactions and smooth refunds.
              </p>
              <p className="small mb-4">
                With RazorpayX, business banking gets supercharged — automated payouts,
                current accounts, and more — all in one platform.
              </p>

            

              {/* Security Certifications */}
              <div>
                <h6 className="text-white mb-2">Security Certifications</h6>
                <p className="small mb-2">
                  These are some of the certificates our trusted payment gateway has earned:
                </p>
                <div className="d-flex flex-wrap gap-2">
                  <Image
                    src="./pcidss.jpeg"
                    alt="PCI DSS Certified"
                    width="100"
                    height="60"
                    className="bg-white rounded p-1"
                    style={{ objectFit: "contain" }}
                  />
                  <Image
                    src="./iso.jpeg"
                    alt="ISO 27001"
                    width="100"
                    height="60"
                    className="bg-white rounded p-1"
                    style={{ objectFit: "contain" }}
                  />
                  <Image
                    src="./aicpasoc.jpeg"
                    alt="SOC 2 Type II"
                    width="100"
                    height="60"
                    className="bg-white rounded p-1"
                    style={{ objectFit: "contain" }}
                  />
                  {/* <Image
                    src="#"
                    alt="RBI Approved"
                    width="50"
                    height="30"
                    className="bg-white rounded p-1"
                    style={{ objectFit: "contain" }}
                  /> */}
                </div>
              </div>

                {/* Supported Payment Modes */}
              <div className="my-4">
                <h6 className="text-white mb-2">
                  <FaCreditCard className="me-2" />
                  Accepted Payment Methods
                </h6>
                <p className="small mb-2">
                  We accept all major credit & debit cards, and netbanking options.
                </p>
                {/* Payment Mode Images */}
                <div className="d-flex flex-wrap gap-2 mb-3">
                  <Image
                    src="./credit_and_debit_cards.jpeg"
                    alt="Accepted Credit and Debit Cards"
                    fluid
                    className="bg-white rounded p-1 w-100"
                    style={{ objectFit: "contain", maxWidth: 400 }}
                  />
                </div>
              </div>

            </div>
          </Col>
        </Row>

        {/* Bottom Row: Copyright */}
        <Row className="mt-4">
          <Col className="text-center">
            <p>&copy; 2025 shashtrarth. All Rights Reserved.</p>
          </Col>
        </Row>
      </Container>
    </footer>
  );
};

export default Footer;
